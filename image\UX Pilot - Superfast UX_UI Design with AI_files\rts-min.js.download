window.RTS=(()=>{function e(e){e=e||{},this.clientVersion=21,this.connectionId="",this.connection_startTime,this.socket_connection_time,this.timeoutPeriod=3e4,this.pingTimerInterval=15e3,this.tokenExpiryBufferPeriod=36e5,this.tokenFetchTimeout=2e4,this.channels={},this.timeouts={},this.callbacks={},this.timerID=0,this.attempts=0,this.clientClose,this.traceIdIncreamenter=0,this.reconnectionFlag=!1,this.fallbackWsFailureFlag=!1,this.fallbackWsNullFlag=!1,this.firstConnectionSuccess=!1,this.disableCookieCheck=!0,this.stableConnectionTimer,this.stableConnectionTimeout=2e4,this.resubscriptionTimeout=3e3,this.resubscriptionMaxAttempts=3,this.webhookSendCustomDataLimit=200,this.ping_worker,this.workerURL,this.selfAckOpt=2,this.reconnectionAttempts=e.reconnectionAttempts||3,this.sioReconnectionAttempts=e.sioReconnectionAttempts||1/0,this.reconnectionDelay=this._isNumberAndGreaterThanZero(e.reconnectionDelay)?e.reconnectionDelay:1e3,this.reconnectionDelayMax=e.reconnectionDelayMax||6e4,this.debug=e.debug||!1,this.logger=e.logger||window.console,this.origin=e.origin,this.onConnect=e.onConnect||function(){},this.onConnectError=e.onConnectError||function(){},this.onReconnect=e.onReconnect||function(){},this.onReconnecting=e.onReconnecting||function(){},this.onDisconnect=e.onDisconnect||function(){},this.onFallback=e.onFallback||function(){},this.onMessage=e.onMessage||function(){},this.onAppMessage=e.onAppMessage,this.isHealthCheckEnabled=e.isHealthCheckEnabled||!1,this.disconnectCount=0,this.connected=!1,this.serviceId=e.serviceId||"rts",this.traceId=e.ti||this._generateUid(),e.accId?this.accId=e.accId:this._throw("accId not provided"),e.userId?this.userId=e.userId:this._throw("userId not provided"),e.token?this.token=encodeURIComponent(e.token):this.token="",e.webURL&&(this.webURL=e.webURL),e.leaveOnUnload&&this.leaveOnUnload(),e.hookId?this.hookId=e.hookId:this.hookId="","function"==typeof e.fetchToken&&(this.fetchToken=e.fetchToken),this.socket=null,this.nocookie=null,this.sioUpgradationAllowedServices=[],this.sioUpgradeConnectionId="",this.sioUpgradeServiceId="serviceIdSioUpgradation",this.sioUpgradeAccId="ACCIDSOCIO",this.sioUpgradeUserId="userIdSioUpgradation",this.sioUpgradeTraceId=this._generateUid(),this.isSioUpgradeClose=null,this.upgradedWebsocket=null,this.sioIntervalId,this.initializeSocket(),this.initializeTokenRefresh()}return e.prototype={constructor:e,generateWsUrl:function(e,t){var n="wss";return 0==e.indexOf("http://localhost")&&(n="ws"),e.replace(/[^:]*/,n)+"/ws?"+t},generateSioUrl:function(){return this.origin+"?"+this._getQueryParams()},errorHandler:function(e){var t=this;if(t.log("attempts: ",t.attempts),t.websocket.readyState<2)try{t.websocket.close()}catch(e){t.log(e)}t.attempts>=t.reconnectionAttempts-1?(t.log("failed to connect via websocket, falling back to sio"),t.timerID&&(window.clearTimeout(t.timerID),t.timerID=0,t.attempts=0),delete t.websocket,this.fallbackWsFailureFlag=!0,t.initializeSocketIO(),null!=window.WebSocket&&t.sioUpgradationAllowedServices.includes(t.serviceId)&&window.Worker&&t.socketioUpgradationWithWorker()):(this.firstConnectionSuccess&&(this.reconnectionFlag=!0),t.timerID||(t.log("setting timerID"),t.timerID=setTimeout(function(){clearTimeout(t.timerID),t.timerID=0,t.connected||t.errorHandler()},t.reconnectionDelay*(t._isNumberAndGreaterThanZero(t.attempts)?t.attempts:1)*10),t._onReconnecting(),null!=navigator&&0==navigator.onLine||(t.log("incrementing retry attempts"),t.attempts++),t.initializeSocket()))},initializeSocket:function(){var n,o=this;this.checkCookieSupport(function(e){var t;o.nocookie=e,1!=o.clientClose&&(n=o._getQueryParams(e),e=o.generateWsUrl(o.origin,n),null!=window.WebSocket?(o.log("creating new socket,",new Date),t=new WebSocket(e),o.websocket=t,o.websocket.onopen=function(){o.websocket==t&&(o.stableConnectionTimer=setTimeout(function(){o.attempts=0},o.stableConnectionTimeout),o.reconnectionFlag&&o._onReconnect(),o.initializeWebSocket(t))},o.websocket.onerror=function(e){o.clientClose||o.websocket==t&&(window.clearTimeout(o.stableConnectionTimer),o.connected=!1,o.errorHandler(e))}):(o.reconnectionFlag&&(o.fallbackWsNullFlag=!0),o.initializeSocketIO()))})},initializeWebSocket:function(t){this.log("Initializing websocket connnection..."),this.connection_startTime=(new Date).getTime();let n=this;var e,o=null;this.websocket.onmessage=function(e){n.websocket==t&&("PONG"!=(e=n._safelyParseJSON(e.data)).event&&n.log("Received Data.",e),n._onEvent(e))},this.websocket.onclose=function(e){n.websocket==t&&(window.clearTimeout(n.stableConnectionTimer),n.clientClose?(n.log("websocket closed"),n.websocket.close()):(n.connected&&n._onDisconnect("ping timeout",n),n.log("attempting to reconnect"),n.errorHandler(e)),window.Worker&&n.ping_worker?(n.ping_worker.postMessage({type:"stop_interval",intervalReference:null}),URL.revokeObjectURL(n.workerURL)):clearInterval(o))},this.reconnectionFlag||(this._onConnect(),this.reconnectionFlag=!0),window.Worker?(e=(e=function(){this.onmessage=function(t){if(t.data.type!=this.undefined)switch(t.data.type){case"start_interval":let e=this.setInterval(function(){this.postMessage({type:"trigger_callback",intervalReference:e})},t.data.Interval);this.postMessage({type:"initiated",intervalReference:e});break;case"stop_interval":clearInterval(t.data.intervalReference),close()}}}.toString()).substring(e.indexOf("{")+1,e.lastIndexOf("}")),e=new Blob([e],{type:"application/javascript"}),this.workerURL=URL.createObjectURL(e),this.ping_worker=new Worker(this.workerURL),this.ping_worker.onmessage=function(e){"trigger_callback"==e.data.type?n.websocket==t?1==t.readyState&&t.send("9"):(n.ping_worker.postMessage({type:"stop_interval",intervalReference:o}),URL.revokeObjectURL(n.workerURL)):"initiated"==e.data.type&&(o=e.data.intervalReference)},this.ping_worker.postMessage({type:"start_interval",Interval:n.pingTimerInterval})):o=setInterval(function(){n.websocket!=t?clearInterval(o):1==t.readyState&&t.send("9")},n.pingTimerInterval)},initializeSocketIO:function(){1!=this.clientClose&&(this.log("Initializing socket connection..."),this.connection_startTime=(new Date).getTime(),this.checkCookieSupport(function(e){this.nocookie=e;e=this.io.Manager(this.generateSioUrl(),{forceBase64:!0,reconnectionAttempts:this.sioReconnectionAttempts,reconnectionDelay:this.reconnectionDelay,reconnectionDelayMax:this.reconnectionDelayMax});this.socket=e.socket("/"),this.initializeSocketListeners()}.bind(this)))},socketioUpgradationWithWorker:function(){var c=this,e=(e=function(){this.onmessage=function(e){var{command:e,retries:t,maxRetries:n,baseDelay:o,maxDelay:i,delay:r}=e.data;"start"===e?this.postMessage({command:"initiated",retries:t,maxRetries:n,baseDelay:o,maxDelay:i}):"retry"===e?this.postMessage({command:"retry",retries:t,maxRetries:n,baseDelay:o,maxDelay:i,delay:r}):"complete"===e&&this.postMessage({command:"completed"})}}.toString()).substring(e.indexOf("{")+1,e.lastIndexOf("}")),e=new Blob([e],{type:"application/javascript"});this.workerURL=URL.createObjectURL(e);let a=new Worker(this.workerURL);a.onmessage=function(e){let{command:t,retries:n,maxRetries:o,baseDelay:i,maxDelay:r,delay:s}=e.data;"initiated"===t?(async(e,t,n,o)=>{if(e<=t){var i=Math.min(n*2**e,o);if(!1!==await c.socketioUpgradation(e,t))return console.error("Socket.io upgradation failed"),void a.postMessage({command:"retry",retries:e,maxRetries:t,baseDelay:n,maxDelay:o,delay:i});console.log("Socket.io upgradation completed successfully")}else console.log("Max retries reached. Socket.io upgradation failed.");a.postMessage({command:"complete"})})(n,o,i,r).then():"retry"===t?++n>o?a.postMessage({command:"start",retries:n,maxRetries:o,baseDelay:i,maxDelay:r}):(console.log(`Retry ${n}. Waiting for ${s}ms...`),setTimeout(()=>{a.postMessage({command:"start",retries:n,maxRetries:o,baseDelay:i,maxDelay:r})},s)):"completed"===t&&a.terminate()},a.postMessage({command:"start",retries:0,maxRetries:5,baseDelay:1e3,maxDelay:2e4})},socketioUpgradation:function(n,o){var i=this,r=null,s=i._getQueryParamsForSioUpgrade(i.nocookie),c=i.generateWsUrl(i.origin,s);let a=JSON.stringify({event:"ping",serviceId:i.sioUpgradeServiceId,userId:i.sioUpgradeUserId,accId:i.sioUpgradeAccId});return new Promise(t=>{function e(e){for(var t=new Date;new Date-t<e;);}null==i.upgradedWebsocket?(i.log("creating new socket,",Date()),i.upgradedWebsocket=new WebSocket(c),i.upgradedWebsocket.onopen=function(){s.includes(i.sioUpgradeAccId)&&(i.upgradedWebsocket.send(a),i.sioIntervalId=setInterval(()=>{i.upgradedWebsocket.send("9")},1e4),e(1e3))},i.upgradedWebsocket.onerror=function(){r=!0,i.upgradedWebsocket=null,t(r)}):i.upgradedWebsocket.readyState===WebSocket.OPEN?s.includes(i.sioUpgradeAccId)&&(i.upgradedWebsocket.send(a),e(1e3)):(r=!0,i.upgradedWebsocket=null,t(r)),i.upgradedWebsocket.onmessage=function(e){i.logger.log(e.data);e=JSON.parse(e.data);"PONG"===e.event&&e.userId===i.sioUpgradeUserId&&e.accId===i.sioUpgradeAccId&&e.serviceId===i.sioUpgradeServiceId&&(e.isSioUpgradeClose?(clearInterval(i.sioIntervalId),i.upgradedWebsocket.close(),i.logger.log("Test websocket closed"),i.upgradedWebsocket=null,t(r=!1)):(r=!0,n===o&&clearInterval(i.sioIntervalId),t(r)))}})},initializeSocketListeners:function(){this.log("Binding the socket listeners..."),this.socket.on("connect",this._onConnect.bind(this)),this.socket.on("connect_error",this._onConnectError.bind(this)),this.socket.on("reconnecting",this._onReconnecting.bind(this)),this.socket.on("reconnect",this._onReconnect.bind(this)),this.socket.on("disconnect",this._onDisconnect.bind(this)),this.socket.on("message",this._onMessage.bind(this))},initializeTokenRefresh:function(){var t,e;null!=this.token&&""!=this.token&&"function"==typeof this.fetchToken&&(e=this.getTokenExpiry(decodeURIComponent(this.token)))!=1/0&&(t=this,(e=e-(new Date).getTime()-this.tokenExpiryBufferPeriod)<=0&&(e+=this.tokenExpiryBufferPeriod),setTimeout(function(){1!=t.clientClose&&t.fetchToken(function(e){null!=e&&""!=e&&(t.token=encodeURIComponent(e),t.socket&&t.socket.io&&t.socket.io.uri&&(t.socket.io.uri=t.generateSioUrl()),t.connected)&&t.grant(e),setTimeout(function(){t.initializeTokenRefresh()},t.tokenFetchTimeout)})},e))},getTokenExpiry:function(e){for(var e=JSON.parse(atob(e.split(".")[1])),t=1/0,n=(isNaN(e.exp)||(t=Math.min(t,1e3*parseInt(e.exp))),e.credentials),o=0;o<n.length;o++){var i=n[o];isNaN(i.expire)||(t=Math.min(t,parseInt(i.expire)))}return t},refreshToken:function(){var t;"function"==typeof this.fetchToken&&(t=this).fetchToken(function(e){null!=e&&""!=e&&(t.token=encodeURIComponent(e),t.connected)&&t.grant(e)})},removeSocketListeners:function(e){this.socket&&this.socket.off(e)},checkCookieSupport:function(t){this.disableCookieCheck?t(!0):null!==this.nocookie?t(this.nocookie):!0===(/constructor/i.test(window.HTMLElement)||"[object SafariRemoteNotification]"===(!window.safari||void 0!==window.safari&&window.safari.pushNotification).toString())?t(!0):this.sendCookieRequest("get",function(e){e?this.sendCookieRequest("check",function(e){e?(this.log("Browser supports cookie from a different domain"),t(!1)):(this.log("Browser does not support cookie from a different domain"),t(!0))}.bind(this)):(this.log("Browser does not support cookie from a different domain"),t(!0))}.bind(this))},sendCookieRequest:function(t,n){var e=new XMLHttpRequest;let o=this;var i=this.origin;this.origin&&"/"!==this.origin[this.origin.length-1]&&(i=this.origin+"/v2/cookie/"),e.open("GET",i+t),e.onreadystatechange=function(){if(this.readyState){if(4===this.readyState)if(o.log("Cookie request :: "+t+" response",this.responseText),200<=this.status&&this.status<300||304===this.status){var e=!0;if("check"==t){e=!1;try{e=JSON.parse(this.responseText).exist}catch(e){}}n(e)}else n(!1)}else n(!1)},e.onerror=function(){o.log(e.statusText)},e.withCredentials=!0,e.send()},subscribe:function(n,o,i){void 0===(i=i||{}).resubscriptionAttempts&&(i.resubscriptionAttempts=0),"function"!=typeof o&&(o=null);let r=this,e="Already subscribed to channel: ";var s,c;this.log("Trying to subscribe to channel: ",n),this._connectionCheck(o)&&(!i.forceResubscribe&&this._isChannelSubscribed(n)?(this.log(e+n),i&&(r.channels[n].options=i),i.messageHandler&&(r.channels[n].onMessage=i.messageHandler),o&&o(null,e)):(delete i.forceResubscribe,s=!1,c=setTimeout(function(){s||(s=!0,i.resubscriptionAttempts++,i.resubscriptionAttempts==r.resubscriptionMaxAttempts?(i.resubscriptionAttempts=0,window.clearTimeout(c),r.log("resubscription timed out, could not subscribe to channel",n),o&&o("subscription failed","tried subscribing"+r.resubscriptionMaxAttempts+" times: resubscription failed"),o=null):r.subscribe(n,o,i))},r.resubscriptionTimeout),this._sendMessage({event:"subscribe",channel:n,serviceId:this.serviceId,token:i.token,ti:i.traceId,hookId:i.hookId,opt:i.opt||0},function(e,t){if(null==e){if(i.resubscriptionAttempts=0,s)return;s=!0,window.clearTimeout(c),r.channels[n]={connected:!0,options:i,onMessage:i.messageHandler},r._fireCallback(o,e,t)}e&&r.log("Could not subscribe to channel: ",e,n)})))},__getSocketOpt:function(e){return e=e||0,e|=64},subscribeSocket:function(e,t,n){n.opt=this.__getSocketOpt(n.opt),this.subscribe(e,t,n)},unsubscribe:function(n,o,e){e=e||{},"function"!=typeof o&&(o=null);let i=this;this._connectionCheck(o)&&this._isChannelSubscribed(n,o)&&this._sendMessage({event:"unsubscribe",channel:n,serviceId:this.serviceId,ti:e.traceId,hookId:e.hookId,opt:e.opt||0},function(e,t){e?i.log("Could not unsubscribe from channel: ",e,n):delete i.channels[n],o&&o(e,t)})},unsubscribeSocket:function(e,t,n){(n=n||{}).opt=this.__getSocketOpt(n.opt),this.unsubscribe(e,t,n)},unsubscribeAll:function(e,t){t=t||{},"function"!=typeof e&&(e=null),this._connectionCheck&&(this._sendMessage({event:"unsubscribeall",serviceId:this.serviceId,ti:t.traceId}),this.channels={},e)&&e(null,this.channels)},ping:function(e){e=e||{},this.isHealthCheckEnabled&&this._connectionCheck&&this._sendMessage({event:"ping",userId:this.userId,serviceId:this.serviceId,ti:e.traceId})},channelWho:function(n,o,e){e=e||{},"function"!=typeof o&&(o=null);let i=this;this._connectionCheck(o)&&this._sendMessage({event:"channel_who",channel:n,serviceId:this.serviceId,ti:e.traceId,msg:JSON.stringify(e)},function(e,t){e&&i.log("Could not fetch channel_who: ",e,n),o&&o(e,t)})},userWhere:function(n,e){e=e||{},"function"!=typeof n&&(n=null);let o=this;this._connectionCheck(n)&&this._sendMessage({event:"user_where",serviceId:this.serviceId,ti:e.traceId},function(e,t){e&&o.log("Could not fetch user_where: ",e),n&&n(e,t)})},userThere:function(n,e){e=e||{},"function"!=typeof n&&(n=null);let o=this;this._connectionCheck(n)&&this._sendMessage({accId:this.accId,event:"user_there",userid:this.userId,serviceId:this.serviceId,ti:e.traceId},function(e,t){e&&o.log("Could not fetch user_there: ",e),n&&n(e,t)})},grant:function(e,n,t){t=t||{},"function"!=typeof n&&(n=null);let o=this;this._connectionCheck(n)&&this._sendMessage({event:"grant",msg:e,serviceId:this.serviceId,ti:t.traceId},function(e,t){e&&o.log("Could not grant access: ",e),n&&n(e,t)})},revoke:function(e,n,t){t=t||{},"function"!=typeof n&&(n=null);let o=this;this._connectionCheck(n)&&this._sendMessage({event:"revoke",msg:e,serviceId:this.serviceId,ti:t.traceId},function(e,t){e&&o.log("Could not revoke access: ",e),n&&n(e,t)})},save:function(e,n,o,t){t=t||{},"function"!=typeof o&&(o=null);let i=this;this._connectionCheck()&&this._isChannelSubscribed(e)&&this._sendMessage({event:"save",channel:e,serviceId:this.serviceId,ti:t.traceId,msg:JSON.stringify(n)},function(e,t){e?i.log("Could not fetch user_where: ",e):i.log("Saved data.",n),o&&o(e,t)})},fetch:function(e,n,t){t=t||{},"function"!=typeof n&&(n=null);let o=this;this._connectionCheck(n)&&this._isChannelSubscribed(e,n)&&this._sendMessage({event:"fetch",channel:e,userId:this.userId,serviceId:this.serviceId,ti:t.traceId},function(e,t){e&&o.log("Could not fetch user data: ",e),n&&n(e,t)})},publish:function(e,t,n,o){o=o||{},"function"!=typeof n&&(n=null),this._connectionCheck&&this._isChannelSubscribed(e,n)&&(e={event:"send",channel:e,msg:JSON.stringify(t),userid:o.userId,opt:o.opt||(o.persist?1:0),serviceId:this.serviceId,ti:o.traceId},o.msgType&&(e.msgType=o.msgType),this._sendMessage(e,n),this.log("Message sent",t))},persist:function(e,t,n,o){function i(e,t){r.log("rest message send callback","Error: ",e," | Message: ",t),null!=n&&(n(e,t),n=null)}var r=this,e=("function"!=typeof n&&(n=null),this.origin+"/v1/message/send/"+this.accId+"/"+e+"?token="+this.token),o=(o=o||{}).persist?1:0,o='{"msg":'+JSON.stringify(t)+',"opt":"'+o+'","senderId":"'+this.userId+'"}',s=new XMLHttpRequest;s.onreadystatechange=function(){this.readyState==this.DONE&&(200<=this.status&&this.status<300?(r.log("message sent",t),i(null,s.responseText)):i("Server error, code: "+this.status,s.responseText))},s.ontimeout=function(){i("Timeout",null)},s.onerror=function(){i("Something went wrong",null)},s.timeout=2e4,s.open("POST",e,!0),s.setRequestHeader("Content-Type","application/json"),s.send(o)},sendAck:function(e,t,n,o,i){i=i||{},this._connectionCheck(o="function"!=typeof o?null:o)&&this._isChannelSubscribed(e,o)&&(this._sendMessage({accId:this.accId,event:"client_ack",channel:e,msg:t,userId:n,serviceId:this.serviceId,ti:i.traceId,msgPayload:JSON.stringify(i.message)}),o)&&o(null,"Sent ack message")},sendSelfAck:function(e,t,n,o,i){i=i||{},this._connectionCheck(o="function"!=typeof o?null:o)&&this._isChannelSubscribed(e,o)&&(this._sendMessage({accId:this.accId,event:"self_ack",channel:e,msg:t,connectionId:n,serviceId:this.serviceId,ti:i.traceId,msgPayload:JSON.stringify(i.message)}),o)&&o(null,"Sent self_ack message")},log:function(){var e,t;!0===this.debug&&this.logger&&(e=Array.prototype.slice.call(arguments),t="",e.unshift("RTS:"),e.unshift((new Date).toLocaleTimeString()),e.forEach(function(e){e instanceof Error?t+=e.toString()+" ":t+="object"==typeof e?JSON.stringify(e)+" ":e+" "}),this.logger.log(t))},close:function(e,t){if(this.clientClose=!0,t=t||{},this._connectionCheck(e)&&this._sendMessage({event:"close",serviceId:this.serviceId,hookId:t.hookId,ti:t.traceId}),"function"!=typeof e&&(e=null),this.websocket){try{this.websocket.close()}catch(e){this.log(e)}var n=this;setTimeout(function(){n._onDisconnect("forced close")},0)}else this.disconnect();window.Worker&&this.ping_worker&&this.ping_worker.postMessage({type:"stop_interval",intervalReference:null}),e&&e(null,"Connection Closed")},webhookSend:function(e,t,n,o,i){let r=null;return i=i||{},"function"!=typeof o&&(o=null),t.length>this.webhookSendCustomDataLimit?(r="Data limit exceeded. Failed to send webhook",null!=o?o(r,null):r):n&&0!==n.length?void this._sendMessage({data:t,hookId:n,event:"webhooksend",channel:e,token:i.token,traceId:i.traceId,serviceId:this.serviceId,opt:i.opt||0},function(e,t){return e&&this.log("Could not send webhook: ",e),null!=o?o(e,t):e}):(r="Hook id missing in webhook send",null!=o?o(r,null):r)},disconnect:function(){this.socket&&this.socket.io.disconnect()},reconnect:function(e){this.socket.io.connect()},_fireCallback:function(e){var t;"function"==typeof e&&(t=Array.prototype.slice.call(arguments,1,arguments.length),setTimeout(function(){e.apply(null,t)},0))},_connectionCheck:function(e){var t="Not performing action as client is not connected.";return this.websocket?1==this.websocket.readyState||(this.log(t),e&&e(t),!1):(!1===this.connected&&(this.log(t),e)&&e(t),this.connected)},_sendMessage:function(e,t){var n;t&&(n=this._generateUid(),this.callbacks[n]=t,e.clientId=n,this._startTimeoutErrorTimer(n)),e.accId=this.accId,this.websocket?(9!=e&&this.log("Sending data",e),this.websocket.send(JSON.stringify(e))):this.socket.emit("message",JSON.stringify(e))},_startTimeoutErrorTimer:function(e){let t=this;t.timeouts[e]=setTimeout(function(){t.callbacks[e]&&t.callbacks[e]("Timeout Error"),delete t.timeouts[e]},this.timeoutPeriod)},_generateUid:function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)})},_isNumberAndGreaterThanZero:function(e){return"number"==typeof e&&0<e},_safelyParseJSON:function(t){var e;try{e=JSON.parse(t)}catch(e){return void this.log("MessageParseError",t)}return e},_isChannelSubscribed:function(e,t){var n="Channel "+e+" is not subscribed yet.";return this.channels[e]||(this.log(n),t&&t(n)),!!this.channels[e]},_onConnect:function(){if(this.reconnectionFlag){let n=this;this.connected=!0,n.channels&&0<Object.keys(n.channels).length&&Object.keys(n.channels).forEach(function(e){var t=n.channels[e].options||{};t.forceResubscribe=!0,n.subscribe(e,null,t)}),void(this.fallbackWsFailureFlag&&(this.onFallback(),this._fireCallback(this.onReconnect),this.fallbackWsFailureFlag=!1))}else{var e=(new Date).getTime()-this.connection_startTime;this.log("Socket Connected"),this.websocket?(this.log("Time Taken by Websocket:- ",e),this.websocket.id=this._generateUid()):this.log("Time Taken by Socket.IO:- ",e),this.connected=!0,this.firstConnectionSuccess=!0,this._fireCallback(this.onConnect,this.reconnectionFlag),this.reconnectionFlag=!0}},_onConnectError:function(e){this.log("Socket Connect Error"),this._fireCallback(this.onConnectError,e)},_onReconnecting:function(){this.websocket?this.log("Websocket reconnecting"):this.log("Socket reconnecting"),this._fireCallback(this.onReconnecting)},_onReconnect:function(){let n=this;this.log("Socket reconnected"),window.clearTimeout(this.disconnect_timer),this.connected=!0,Object.keys(n.channels).forEach(function(e){var t=n.channels[e].options||{};t.forceResubscribe=!0,n.subscribe(e,null,t)}),this.fallbackWsNullFlag&&(this.onFallback(),this.fallbackWsNullFlag=!1),this._fireCallback(n.onReconnect)},_onDisconnect:function(e,t){this.log("Disconnected"),this.connected=!1,this.clientClose&&(this.channels={}),this.disconnectCount+=1,this._fireCallback(this.onDisconnect,e,t)},_onEvent:function(e){if("CONNECT"==e.event&&(this.connectionId=e.connId),e.clientId&&(this.timeouts[e.clientId]&&(clearTimeout(this.timeouts[e.clientId]),delete this.timeouts[e.clientId]),this.callbacks[e.clientId])){if(""!==e.err&&e.err){this.log("Error: ",e);try{this.callbacks[e.clientId](e.err)}catch(e){this.log(e)}}else try{this.callbacks[e.clientId](null,e)}catch(e){this.log(e)}delete this.callbacks[e.clientId]}var n;"APP_PUBLISH"==e.event&&"function"==typeof this.onAppMessage?this._fireCallback(this.onAppMessage,e):("send"==e.event&&this.channels[e.channel]&&null!=typeof this.channels[e.channel].options&&this.channels[e.channel].options.opt&this.selfAckOpt&&(n=this).sendSelfAck(e.channel,e.id,this.connectionId,function(e,t){n.log("Error: "+e+",","Response: "+t)},{traceId:e.ti}),("send"==e.event||"CHANNEL_JOIN"==e.event||"LEFT_CHANNEL"===e.event)&&e.channel&&this.channels[e.channel]&&"function"==typeof this.channels[e.channel].onMessage?this._fireCallback(this.channels[e.channel].onMessage,e):this._fireCallback(this.onMessage,e))},_onMessage:function(e){e=this._safelyParseJSON(e);this._onEvent(e)},_getQueryParams:function(){this.traceIdIncreamenter++;var e=["rcid=",this.connectionId,"&accId=",this.accId,"&serviceId=",this.serviceId,"&userId=",this.userId,"&ti=",this.traceId+-+this.traceIdIncreamenter,"&token=",this.token,"&cv=",this.clientVersion].join("");return!0===this.nocookie&&(e+="&nocookie=true"),e},_getQueryParamsForSioUpgrade:function(){this.traceIdIncreamenter++;var e=["rcid=",this.sioUpgradeConnectionId,"&accId=",this.sioUpgradeAccId,"&serviceId=",this.sioUpgradeServiceId,"&userId=",this.sioUpgradeUserId,"&ti=",this.sioUpgradeTraceId+-+this.traceIdIncreamenter,"&cv=",this.clientVersion].join("");return!0===this.nocookie&&(e+="&nocookie=true"),e},_throw:function(e){throw new Error(e)},io:function o(i,r,s){function c(n,e){if(!r[n]){if(!i[n]){var t="function"==typeof require&&require;if(!e&&t)return t(n,!0);if(a)return a(n,!0);throw new Error("Cannot find module '"+n+"'")}e=r[n]={exports:{}};i[n][0].call(e.exports,function(e){var t=i[n][1][e];return c(t||e)},e,e.exports,o,i,r,s)}return r[n].exports}for(var a="function"==typeof require&&require,e=0;e<s.length;e++)c(s[e]);return c}({1:[function(e,t,n){t.exports=e("./lib/")},{"./lib/":2}],2:[function(e,t,n){var r=e("./url"),o=e("socket.io-parser"),s=e("./manager"),c=e("debug")("socket.io-client"),a=(t.exports=n=i,n.managers={});function i(e,t){"object"==typeof e&&(t=e,e=void 0),t=t||{};var e=r(e),n=e.source,o=e.id,i=e.path,i=a[o]&&a[o].nsps[i]&&i==a[o].nsps[i].nsp,i=t.forceNew||t["force new connection"]||!1===t.multiplex||i?(c("ignoring socket cache for %s",n),s(n,t)):(a[o]||(c("new io instance for %s",n),a[o]=s(n,t)),a[o]);return i.socket(e.path)}n.protocol=o.protocol,n.connect=i,n.Manager=e("./manager"),n.Socket=e("./socket")},{"./manager":3,"./socket":5,"./url":6,debug:10,"socket.io-parser":44}],3:[function(e,t,n){e("./url");var c=e("engine.io-client"),o=e("./socket"),i=e("component-emitter"),r=e("socket.io-parser"),a=e("./on"),s=e("component-bind"),h=(e("object-component"),e("debug")("socket.io-client:manager")),l=e("indexof"),u=e("backo2");function p(e,t){if(!(this instanceof p))return new p(e,t);e&&"object"==typeof e&&(t=e,e=void 0),(t=t||{}).path=t.path||"/socket.io",this.nsps={},this.subs=[],this.opts=t,this.reconnection(!1!==t.reconnection),this.reconnectionAttempts(t.reconnectionAttempts||1/0),this.reconnectionDelay(t.reconnectionDelay||1e3),this.reconnectionDelayMax(t.reconnectionDelayMax||5e3),this.randomizationFactor(t.randomizationFactor||.5),this.backoff=new u({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(null==t.timeout?2e4:t.timeout),this.readyState="closed",this.uri=e,this.connected=[],this.encoding=!1,this.packetBuffer=[],this.encoder=new r.Encoder,this.decoder=new r.Decoder,this.autoConnect=!1!==t.autoConnect,this.autoConnect&&this.open()}(t.exports=p).prototype.emitAll=function(){for(var e in this.emit.apply(this,arguments),this.nsps)this.nsps[e].emit.apply(this.nsps[e],arguments)},p.prototype.updateSocketIds=function(){for(var e in this.nsps)this.nsps[e].id=this.engine.id},i(p.prototype),p.prototype.reconnection=function(e){return arguments.length?(this._reconnection=!!e,this):this._reconnection},p.prototype.reconnectionAttempts=function(e){return arguments.length?(this._reconnectionAttempts=e,this):this._reconnectionAttempts},p.prototype.reconnectionDelay=function(e){return arguments.length?(this._reconnectionDelay=e,this.backoff&&this.backoff.setMin(e),this):this._reconnectionDelay},p.prototype.randomizationFactor=function(e){return arguments.length?(this._randomizationFactor=e,this.backoff&&this.backoff.setJitter(e),this):this._randomizationFactor},p.prototype.reconnectionDelayMax=function(e){return arguments.length?(this._reconnectionDelayMax=e,this.backoff&&this.backoff.setMax(e),this):this._reconnectionDelayMax},p.prototype.timeout=function(e){return arguments.length?(this._timeout=e,this):this._timeout},p.prototype.maybeReconnectOnOpen=function(){!this.reconnecting&&this._reconnection&&0===this.backoff.attempts&&this.reconnect()},p.prototype.open=p.prototype.connect=function(n){var e,o,t,i,r,s;return h("readyState %s",this.readyState),~this.readyState.indexOf("open")||(h("opening %s",this.uri),this.engine=c(this.uri,this.opts),e=this.engine,(o=this).readyState="opening",this.skipReconnect=!1,t=a(e,"open",function(){o.onopen(),n&&n()}),i=a(e,"error",function(e){var t;h("connect_error"),o.cleanup(),o.readyState="closed",o.emitAll("connect_error",e),n?((t=new Error("Connection error")).data=e,n(t)):o.maybeReconnectOnOpen()}),!1!==this._timeout&&(r=this._timeout,h("connect attempt will timeout after %d",r),s=setTimeout(function(){h("connect attempt timed out after %d",r),t.destroy(),e.close(),e.emit("error","timeout"),o.emitAll("connect_timeout",r)},r),this.subs.push({destroy:function(){clearTimeout(s)}})),this.subs.push(t),this.subs.push(i)),this},p.prototype.onopen=function(){h("open"),this.cleanup(),this.readyState="open",this.emit("open");var e=this.engine;this.subs.push(a(e,"data",s(this,"ondata"))),this.subs.push(a(this.decoder,"decoded",s(this,"ondecoded"))),this.subs.push(a(e,"error",s(this,"onerror"))),this.subs.push(a(e,"close",s(this,"onclose")))},p.prototype.ondata=function(e){this.decoder.add(e)},p.prototype.ondecoded=function(e){this.emit("packet",e)},p.prototype.onerror=function(e){h("error",e),this.emitAll("error",e)},p.prototype.socket=function(e){var t,n=this.nsps[e];return n||(n=new o(this,e),this.nsps[e]=n,t=this,n.on("connect",function(){n.id=t.engine.id,~l(t.connected,n)||t.connected.push(n)})),n},p.prototype.destroy=function(e){e=l(this.connected,e);~e&&this.connected.splice(e,1),this.connected.length||this.close()},p.prototype.packet=function(e){h("writing packet %j",e);var n=this;n.encoding?n.packetBuffer.push(e):(n.encoding=!0,this.encoder.encode(e,function(e){for(var t=0;t<e.length;t++)n.engine.write(e[t]);n.encoding=!1,n.processPacketQueue()}))},p.prototype.processPacketQueue=function(){var e;0<this.packetBuffer.length&&!this.encoding&&(e=this.packetBuffer.shift(),this.packet(e))},p.prototype.cleanup=function(){for(var e;e=this.subs.shift();)e.destroy();this.packetBuffer=[],this.encoding=!1,this.decoder.destroy()},p.prototype.close=p.prototype.disconnect=function(){this.skipReconnect=!0,this.backoff.reset(),this.readyState="closed",this.engine&&this.engine.close()},p.prototype.onclose=function(e){h("close"),this.cleanup(),this.backoff.reset(),this.readyState="closed",this.emit("close",e),this._reconnection&&!this.skipReconnect&&this.reconnect()},p.prototype.reconnect=function(){if(this.reconnecting||this.skipReconnect)return this;var e,t,n=this;this.backoff.attempts>=this._reconnectionAttempts?(h("reconnect failed"),this.backoff.reset(),this.emitAll("reconnect_failed"),this.reconnecting=!1):(e=this.backoff.duration(),h("will wait %dms before reconnect attempt",e),this.reconnecting=!0,t=setTimeout(function(){n.skipReconnect||(h("attempting reconnect"),n.emitAll("reconnect_attempt",n.backoff.attempts),n.emitAll("reconnecting",n.backoff.attempts),n.skipReconnect)||n.open(function(e){e?(h("reconnect attempt error"),n.reconnecting=!1,n.reconnect(),n.emitAll("reconnect_error",e.data)):(h("reconnect success"),n.onreconnect())})},e),this.subs.push({destroy:function(){clearTimeout(t)}}))},p.prototype.onreconnect=function(){var e=this.backoff.attempts;this.reconnecting=!1,this.backoff.reset(),this.updateSocketIds(),this.emitAll("reconnect",e)}},{"./on":4,"./socket":5,"./url":6,backo2:7,"component-bind":8,"component-emitter":9,debug:10,"engine.io-client":11,indexof:40,"object-component":41,"socket.io-parser":44}],4:[function(e,t,n){t.exports=function(e,t,n){return e.on(t,n),{destroy:function(){e.removeListener(t,n)}}}},{}],5:[function(e,t,n){var r=e("socket.io-parser"),o=e("component-emitter"),s=e("to-array"),i=e("./on"),c=e("component-bind"),a=e("debug")("socket.io-client:socket"),h=e("has-binary"),l=(t.exports=p,{connect:1,connect_error:1,connect_timeout:1,disconnect:1,error:1,reconnect:1,reconnect_attempt:1,reconnect_failed:1,reconnect_error:1,reconnecting:1}),u=o.prototype.emit;function p(e,t){this.io=e,this.nsp=t,(this.json=this).ids=0,this.acks={},this.io.autoConnect&&this.open(),this.receiveBuffer=[],this.sendBuffer=[],this.connected=!1,this.disconnected=!0}o(p.prototype),p.prototype.subEvents=function(){var e;this.subs||(e=this.io,this.subs=[i(e,"open",c(this,"onopen")),i(e,"packet",c(this,"onpacket")),i(e,"close",c(this,"onclose"))])},p.prototype.open=p.prototype.connect=function(){return this.connected||(this.subEvents(),this.io.open(),"open"==this.io.readyState&&this.onopen()),this},p.prototype.send=function(){var e=s(arguments);return e.unshift("message"),this.emit.apply(this,e),this},p.prototype.emit=function(e){var t;return l.hasOwnProperty(e)?u.apply(this,arguments):(e=s(arguments),t=r.EVENT,t={type:t=h(e)?r.BINARY_EVENT:t,data:e},"function"==typeof e[e.length-1]&&(a("emitting packet with ack id %d",this.ids),this.acks[this.ids]=e.pop(),t.id=this.ids++),this.connected?this.packet(t):this.sendBuffer.push(t)),this},p.prototype.packet=function(e){e.nsp=this.nsp,this.io.packet(e)},p.prototype.onopen=function(){a("transport is open - connecting"),"/"!=this.nsp&&this.packet({type:r.CONNECT})},p.prototype.onclose=function(e){a("close (%s)",e),this.connected=!1,this.disconnected=!0,delete this.id,this.emit("disconnect",e)},p.prototype.onpacket=function(e){if(e.nsp==this.nsp)switch(e.type){case r.CONNECT:this.onconnect();break;case r.EVENT:case r.BINARY_EVENT:this.onevent(e);break;case r.ACK:case r.BINARY_ACK:this.onack(e);break;case r.DISCONNECT:this.ondisconnect();break;case r.ERROR:this.emit("error",e.data)}},p.prototype.onevent=function(e){var t=e.data||[];a("emitting event %j",t),null!=e.id&&(a("attaching ack callback to event"),t.push(this.ack(e.id))),this.connected?u.apply(this,t):this.receiveBuffer.push(t)},p.prototype.ack=function(n){var o=this,i=!1;return function(){var e,t;i||(i=!0,e=s(arguments),a("sending ack %j",e),t=h(e)?r.BINARY_ACK:r.ACK,o.packet({type:t,id:n,data:e}))}},p.prototype.onack=function(e){a("calling ack %s with %j",e.id,e.data),this.acks[e.id].apply(this,e.data),delete this.acks[e.id]},p.prototype.onconnect=function(){this.connected=!0,this.disconnected=!1,this.emit("connect"),this.emitBuffered()},p.prototype.emitBuffered=function(){for(var e=0;e<this.receiveBuffer.length;e++)u.apply(this,this.receiveBuffer[e]);for(this.receiveBuffer=[],e=0;e<this.sendBuffer.length;e++)this.packet(this.sendBuffer[e]);this.sendBuffer=[]},p.prototype.ondisconnect=function(){a("server disconnect (%s)",this.nsp),this.destroy(),this.onclose("io server disconnect")},p.prototype.destroy=function(){if(this.subs){for(var e=0;e<this.subs.length;e++)this.subs[e].destroy();this.subs=null}this.io.destroy(this)},p.prototype.close=p.prototype.disconnect=function(){return this.connected&&(a("performing disconnect (%s)",this.nsp),this.packet({type:r.DISCONNECT})),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}},{"./on":4,"component-bind":8,"component-emitter":9,debug:10,"has-binary":36,"socket.io-parser":44,"to-array":48}],6:[function(e,t,n){!function(o){var i=e("parseuri"),r=e("debug")("socket.io-client:url");t.exports=function(e,t){var n=e,t=t||o.location;null==e&&(e=t.protocol+"//"+t.host);"string"==typeof e&&("/"==e.charAt(0)&&(e="/"==e.charAt(1)?t.protocol+e:t.hostname+e),/^(https?|wss?):\/\//.test(e)||(r("protocol-less url %s",e),e=void 0!==t?t.protocol+"//"+e:"https://"+e),r("parse %s",e),n=i(e));n.port||(/^(http|ws)$/.test(n.protocol)?n.port="80":/^(http|ws)s$/.test(n.protocol)&&(n.port="443"));return n.path=n.path||"/",n.id=n.protocol+"://"+n.host+":"+n.port,n.href=n.protocol+"://"+n.host+(t&&t.port==n.port?"":":"+n.port),n}}.call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{debug:10,parseuri:42}],7:[function(e,t,n){function o(e){this.ms=(e=e||{}).min||100,this.max=e.max||1e4,this.factor=e.factor||2,this.jitter=0<e.jitter&&e.jitter<=1?e.jitter:0,this.attempts=0}(t.exports=o).prototype.duration=function(){var e,t,n=this.ms*Math.pow(this.factor,this.attempts++);return this.jitter&&(e=Math.random(),t=Math.floor(e*this.jitter*n),n=0==(1&Math.floor(10*e))?n-t:n+t),0|Math.min(n,this.max)},o.prototype.reset=function(){this.attempts=0},o.prototype.setMin=function(e){this.ms=e},o.prototype.setMax=function(e){this.max=e},o.prototype.setJitter=function(e){this.jitter=e}},{}],8:[function(e,t,n){var o=[].slice;t.exports=function(e,t){if("function"!=typeof(t="string"==typeof t?e[t]:t))throw new Error("bind() requires a function");var n=o.call(arguments,2);return function(){return t.apply(e,n.concat(o.call(arguments)))}}},{}],9:[function(e,t,n){function o(e){if(e){var t,n=e;for(t in o.prototype)n[t]=o.prototype[t];return n}}(t.exports=o).prototype.on=o.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks[e]=this._callbacks[e]||[]).push(t),this},o.prototype.once=function(e,t){var n=this;function o(){n.off(e,o),t.apply(this,arguments)}return this._callbacks=this._callbacks||{},o.fn=t,this.on(e,o),this},o.prototype.off=o.prototype.removeListener=o.prototype.removeAllListeners=o.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},0==arguments.length)this._callbacks={};else{var n=this._callbacks[e];if(n)if(1==arguments.length)delete this._callbacks[e];else for(var o,i=0;i<n.length;i++)if((o=n[i])===t||o.fn===t){n.splice(i,1);break}}return this},o.prototype.emit=function(e){this._callbacks=this._callbacks||{};var t=[].slice.call(arguments,1),n=this._callbacks[e];if(n)for(var o=0,i=(n=n.slice(0)).length;o<i;++o)n[o].apply(this,t);return this},o.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks[e]||[]},o.prototype.hasListeners=function(e){return!!this.listeners(e).length}},{}],10:[function(e,t,n){function i(o){return i.enabled(o)?function(e){e=(t=e)instanceof Error?t.stack||t.message:t;var t=new Date,n=t-(i[o]||t);i[o]=t,e=o+" "+e+" +"+i.humanize(n),window.console&&console.log&&Function.prototype.apply.call(console.log,console,arguments)}:function(){}}(t.exports=i).names=[],i.skips=[],i.enable=function(e){try{localStorage.debug=e}catch(e){}for(var t=(e||"").split(/[\s,]+/),n=t.length,o=0;o<n;o++)"-"===(e=t[o].replace("*",".*?"))[0]?i.skips.push(new RegExp("^"+e.substr(1)+"$")):i.names.push(new RegExp("^"+e+"$"))},i.disable=function(){i.enable("")},i.humanize=function(e){return 36e5<=e?(e/36e5).toFixed(1)+"h":6e4<=e?(e/6e4).toFixed(1)+"m":1e3<=e?(e/1e3|0)+"s":e+"ms"},i.enabled=function(e){for(var t=0,n=i.skips.length;t<n;t++)if(i.skips[t].test(e))return!1;for(t=0,n=i.names.length;t<n;t++)if(i.names[t].test(e))return!0;return!1};try{window.localStorage&&i.enable(localStorage.debug)}catch(e){}},{}],11:[function(e,t,n){t.exports=e("./lib/")},{"./lib/":12}],12:[function(e,t,n){t.exports=e("./socket"),t.exports.parser=e("engine.io-parser")},{"./socket":13,"engine.io-parser":25}],13:[function(t,h,e){!function(n){var o=t("./transports"),e=t("component-emitter"),u=t("debug")("engine.io-client:socket"),i=t("indexof"),r=t("engine.io-parser"),s=t("parseuri"),c=t("parsejson"),a=t("parseqs");function p(e,t){if(!(this instanceof p))return new p(e,t);t=t||{},e&&"object"==typeof e&&(t=e,e=null),e&&(e=s(e),t.host=e.host,t.secure="https"==e.protocol||"wss"==e.protocol,t.port=e.port,e.query)&&(t.query=e.query),this.secure=null!=t.secure?t.secure:n.location&&"https:"==location.protocol,t.host&&(e=t.host.split(":"),t.hostname=e.shift(),e.length?t.port=e.pop():t.port||(t.port=this.secure?"443":"80")),this.agent=t.agent||!1,this.hostname=t.hostname||(n.location?location.hostname:"localhost"),this.port=t.port||(n.location&&location.port?location.port:this.secure?443:80),this.query=t.query||{},"string"==typeof this.query&&(this.query=a.decode(this.query)),this.upgrade=!1!==t.upgrade,this.path=(t.path||"/engine.io").replace(/\/$/,"")+"/",this.forceJSONP=!!t.forceJSONP,this.jsonp=!1!==t.jsonp,this.forceBase64=!!t.forceBase64,this.enablesXDR=!!t.enablesXDR,this.timestampParam=t.timestampParam||"t",this.timestampRequests=t.timestampRequests,this.transports=t.transports||["polling"],this.readyState="",this.writeBuffer=[],this.callbackBuffer=[],this.policyPort=t.policyPort||843,this.rememberUpgrade=t.rememberUpgrade||!1,this.binaryType=null,this.onlyBinaryUpgrades=t.onlyBinaryUpgrades,this.pfx=t.pfx||null,this.key=t.key||null,this.passphrase=t.passphrase||null,this.cert=t.cert||null,this.ca=t.ca||null,this.ciphers=t.ciphers||null,this.rejectUnauthorized=t.rejectUnauthorized||null,this.open()}(h.exports=p).priorWebsocketSuccess=!1,e(p.prototype),p.protocol=r.protocol,(p.Socket=p).Transport=t("./transport"),p.transports=t("./transports"),p.parser=t("engine.io-parser"),p.prototype.createTransport=function(e){u('creating transport "%s"',e);var t=(e=>{var t,n={};for(t in e)e.hasOwnProperty(t)&&(n[t]=e[t]);return n})(this.query);return t.EIO=r.protocol,t.transport=e,this.id&&(t.sid=this.id),new o[e]({agent:this.agent,hostname:this.hostname,port:this.port,secure:this.secure,path:this.path,query:t,forceJSONP:this.forceJSONP,jsonp:this.jsonp,forceBase64:this.forceBase64,enablesXDR:this.enablesXDR,timestampRequests:this.timestampRequests,timestampParam:this.timestampParam,policyPort:this.policyPort,socket:this,pfx:this.pfx,key:this.key,passphrase:this.passphrase,cert:this.cert,ca:this.ca,ciphers:this.ciphers,rejectUnauthorized:this.rejectUnauthorized})},p.prototype.open=function(){var e,t;if(this.rememberUpgrade&&p.priorWebsocketSuccess&&-1!=this.transports.indexOf("websocket"))t="websocket";else{if(0==this.transports.length)return e=this,void setTimeout(function(){e.emit("error","No transports available")},0);t=this.transports[0]}this.readyState="opening";try{t=this.createTransport(t)}catch(e){return this.transports.shift(),void this.open()}t.open(),this.setTransport(t)},p.prototype.setTransport=function(e){u("setting transport %s",e.name);var t=this;this.transport&&(u("clearing existing transport %s",this.transport.name),this.transport.removeAllListeners()),(this.transport=e).on("drain",function(){t.onDrain()}).on("packet",function(e){t.onPacket(e)}).on("error",function(e){t.onError(e)}).on("close",function(){t.onClose("transport close")})},p.prototype.probe=function(n){u('probing transport "%s"',n);var o=this.createTransport(n,{probe:1}),t=!1,i=this;function e(){var e;i.onlyBinaryUpgrades&&(e=!this.supportsBinary&&i.transport.supportsBinary,t=t||e),t||(u('probe transport "%s" opened',n),o.send([{type:"ping",data:"probe"}]),o.once("packet",function(e){t||("pong"==e.type&&"probe"==e.data?(u('probe transport "%s" pong',n),i.upgrading=!0,i.emit("upgrading",o),o&&(p.priorWebsocketSuccess="websocket"==o.name,u('pausing current transport "%s"',i.transport.name),i.transport.pause(function(){t||"closed"!=i.readyState&&(u("changing transport and sending upgrade packet"),l(),i.setTransport(o),o.send([{type:"upgrade"}]),i.emit("upgrade",o),o=null,i.upgrading=!1,i.flush())}))):(u('probe transport "%s" failed',n),(e=new Error("probe error")).transport=o.name,i.emit("upgradeError",e)))}))}function r(){t||(t=!0,l(),o.close(),o=null)}function s(e){var t=new Error("probe error: "+e);t.transport=o.name,r(),u('probe transport "%s" failed because of error: %s',n,e),i.emit("upgradeError",t)}function c(){s("transport closed")}function a(){s("socket closed")}function h(e){o&&e.name!=o.name&&(u('"%s" works - aborting "%s"',e.name,o.name),r())}function l(){o.removeListener("open",e),o.removeListener("error",s),o.removeListener("close",c),i.removeListener("close",a),i.removeListener("upgrading",h)}p.priorWebsocketSuccess=!1,o.once("open",e),o.once("error",s),o.once("close",c),this.once("close",a),this.once("upgrading",h),o.open()},p.prototype.onOpen=function(){if(u("socket open"),this.readyState="open",p.priorWebsocketSuccess="websocket"==this.transport.name,this.emit("open"),this.flush(),"open"==this.readyState&&this.upgrade&&this.transport.pause){u("starting upgrade probes");for(var e=0,t=this.upgrades.length;e<t;e++)this.probe(this.upgrades[e])}},p.prototype.onPacket=function(e){if("opening"==this.readyState||"open"==this.readyState)switch(u('socket receive: type "%s", data "%s"',e.type,e.data),this.emit("packet",e),this.emit("heartbeat"),e.type){case"open":this.onHandshake(c(e.data));break;case"pong":this.setPing();break;case"error":var t=new Error("server error");t.code=e.data,this.emit("error",t);break;case"message":this.emit("data",e.data),this.emit("message",e.data)}else u('packet received with socket readyState "%s"',this.readyState)},p.prototype.onHandshake=function(e){this.emit("handshake",e),this.id=e.sid,this.transport.query.sid=e.sid,this.upgrades=this.filterUpgrades(e.upgrades),this.pingInterval=e.pingInterval,this.pingTimeout=e.pingTimeout,this.onOpen(),"closed"!=this.readyState&&(this.setPing(),this.removeListener("heartbeat",this.onHeartbeat),this.on("heartbeat",this.onHeartbeat))},p.prototype.onHeartbeat=function(e){clearTimeout(this.pingTimeoutTimer);var t=this;t.pingTimeoutTimer=setTimeout(function(){"closed"!=t.readyState&&t.onClose("ping timeout")},e||t.pingInterval+t.pingTimeout)},p.prototype.setPing=function(){var e=this;clearTimeout(e.pingIntervalTimer),e.pingIntervalTimer=setTimeout(function(){u("writing ping packet - expecting pong within %sms",e.pingTimeout),e.ping(),e.onHeartbeat(e.pingTimeout)},e.pingInterval)},p.prototype.ping=function(){this.sendPacket("ping")},p.prototype.onDrain=function(){for(var e=0;e<this.prevBufferLen;e++)this.callbackBuffer[e]&&this.callbackBuffer[e]();this.writeBuffer.splice(0,this.prevBufferLen),this.callbackBuffer.splice(0,this.prevBufferLen),(this.prevBufferLen=0)==this.writeBuffer.length?this.emit("drain"):this.flush()},p.prototype.flush=function(){"closed"!=this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length&&(u("flushing %d packets in socket",this.writeBuffer.length),this.transport.send(this.writeBuffer),this.prevBufferLen=this.writeBuffer.length,this.emit("flush"))},p.prototype.write=p.prototype.send=function(e,t){return this.sendPacket("message",e,t),this},p.prototype.sendPacket=function(e,t,n){"closing"!=this.readyState&&"closed"!=this.readyState&&(this.emit("packetCreate",e={type:e,data:t}),this.writeBuffer.push(e),this.callbackBuffer.push(n),this.flush())},p.prototype.close=function(){function e(){o.onClose("forced close"),u("socket closing - telling transport to close"),o.transport.close()}function t(){o.removeListener("upgrade",t),o.removeListener("upgradeError",t),e()}function n(){o.once("upgrade",t),o.once("upgradeError",t)}var o;return"opening"!=this.readyState&&"open"!=this.readyState||(this.readyState="closing",(o=this).writeBuffer.length?this.once("drain",function(){(this.upgrading?n:e)()}):(this.upgrading?n:e)()),this},p.prototype.onError=function(e){u("socket error %j",e),p.priorWebsocketSuccess=!1,this.emit("error",e),this.onClose("transport error",e)},p.prototype.onClose=function(e,t){var n;"opening"!=this.readyState&&"open"!=this.readyState&&"closing"!=this.readyState||(u('socket close with reason: "%s"',e),n=this,clearTimeout(this.pingIntervalTimer),clearTimeout(this.pingTimeoutTimer),setTimeout(function(){n.writeBuffer=[],n.callbackBuffer=[],n.prevBufferLen=0},0),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),this.readyState="closed",this.id=null,this.emit("close",e,t))},p.prototype.filterUpgrades=function(e){for(var t=[],n=0,o=e.length;n<o;n++)~i(this.transports,e[n])&&t.push(e[n]);return t}}.call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./transport":14,"./transports":15,"component-emitter":9,debug:22,"engine.io-parser":25,indexof:40,parsejson:32,parseqs:33,parseuri:34}],14:[function(e,t,n){var o=e("engine.io-parser");function i(e){this.path=e.path,this.hostname=e.hostname,this.port=e.port,this.secure=e.secure,this.query=e.query,this.timestampParam=e.timestampParam,this.timestampRequests=e.timestampRequests,this.readyState="",this.agent=e.agent||!1,this.socket=e.socket,this.enablesXDR=e.enablesXDR,this.pfx=e.pfx,this.key=e.key,this.passphrase=e.passphrase,this.cert=e.cert,this.ca=e.ca,this.ciphers=e.ciphers,this.rejectUnauthorized=e.rejectUnauthorized}e("component-emitter")((t.exports=i).prototype),i.timestamps=0,i.prototype.onError=function(e,t){e=new Error(e);return e.type="TransportError",e.description=t,this.emit("error",e),this},i.prototype.open=function(){return"closed"!=this.readyState&&""!=this.readyState||(this.readyState="opening",this.doOpen()),this},i.prototype.close=function(){return"opening"!=this.readyState&&"open"!=this.readyState||(this.doClose(),this.onClose()),this},i.prototype.send=function(e){if("open"!=this.readyState)throw new Error("Transport not open");this.write(e)},i.prototype.onOpen=function(){this.readyState="open",this.writable=!0,this.emit("open")},i.prototype.onData=function(e){e=o.decodePacket(e,this.socket.binaryType);this.onPacket(e)},i.prototype.onPacket=function(e){this.emit("packet",e)},i.prototype.onClose=function(){this.readyState="closed",this.emit("close")}},{"component-emitter":9,"engine.io-parser":25}],15:[function(t,e,n){!function(s){var c=t("xmlhttprequest"),a=t("./polling-xhr"),h=t("./polling-jsonp"),e=t("./websocket");n.polling=function(e){var t=!1,n=!1,o=!1!==e.jsonp;{var i,r;s.location&&(i="https:"==location.protocol,r=(r=location.port)||(i?443:80),t=e.hostname!=location.hostname||r!=e.port,n=e.secure!=i)}{if(e.xdomain=t,e.xscheme=n,"open"in new c(e)&&!e.forceJSONP)return new a(e);if(o)return new h(e);throw new Error("JSONP disabled")}},n.websocket=e}.call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./polling-jsonp":16,"./polling-xhr":17,"./websocket":19,xmlhttprequest:20}],16:[function(s,c,e){!function(n){var o,i=s("./polling"),e=s("component-inherit"),h=(c.exports=t,/\n/g),l=/\\n/g;function r(){}function t(e){i.call(this,e),this.query=this.query||{},o||(n.___eio||(n.___eio=[]),o=n.___eio),this.index=o.length;var t=this;o.push(function(e){t.onData(e)}),this.query.j=this.index,n.document&&n.addEventListener&&n.addEventListener("beforeunload",function(){t.script&&(t.script.onerror=r)},!1)}e(t,i),t.prototype.supportsBinary=!1,t.prototype.doClose=function(){this.script&&(this.script.parentNode.removeChild(this.script),this.script=null),this.form&&(this.form.parentNode.removeChild(this.form),this.form=null,this.iframe=null),i.prototype.doClose.call(this)},t.prototype.doPoll=function(){var t=this,e=document.createElement("script"),n=(this.script&&(this.script.parentNode.removeChild(this.script),this.script=null),e.async=!0,e.src=this.uri(),e.onerror=function(e){t.onError("jsonp poll error",e)},document.getElementsByTagName("script")[0]);n.parentNode.insertBefore(e,n),this.script=e,"undefined"!=typeof navigator&&/gecko/i.test(navigator.userAgent)&&setTimeout(function(){var e=document.createElement("iframe");document.body.appendChild(e),document.body.removeChild(e)},100)},t.prototype.doWrite=function(e,t){var n,o,i,r,s=this;function c(){a(),t()}function a(){if(s.iframe)try{s.form.removeChild(s.iframe)}catch(e){s.onError("jsonp polling iframe removal error",e)}try{var e='<iframe src="javascript:0" name="'+s.iframeId+'">';r=document.createElement(e)}catch(e){(r=document.createElement("iframe")).name=s.iframeId,r.src="javascript:0"}r.id=s.iframeId,s.form.appendChild(r),s.iframe=r}this.form||(n=document.createElement("form"),o=document.createElement("textarea"),i=this.iframeId="eio_iframe_"+this.index,n.className="socketio",n.style.position="absolute",n.style.top="-1000px",n.style.left="-1000px",n.target=i,n.method="POST",n.setAttribute("accept-charset","utf-8"),o.name="d",n.appendChild(o),document.body.appendChild(n),this.form=n,this.area=o),this.form.action=this.uri(),a(),e=e.replace(l,"\\\n"),this.area.value=e.replace(h,"\\n");try{this.form.submit()}catch(e){}this.iframe.attachEvent?this.iframe.onreadystatechange=function(){"complete"==s.iframe.readyState&&c()}:this.iframe.onload=c}}.call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./polling":18,"component-inherit":21}],17:[function(l,u,e){!function(o){var i=l("xmlhttprequest"),r=l("./polling"),e=l("component-emitter"),t=l("component-inherit"),s=l("debug")("engine.io-client:polling-xhr");function n(){}function c(e){var t,n;r.call(this,e),o.location&&(t="https:"==location.protocol,n=(n=location.port)||(t?443:80),this.xd=e.hostname!=o.location.hostname||n!=e.port,this.xs=e.secure!=t)}function a(e){this.method=e.method||"GET",this.uri=e.uri,this.xd=!!e.xd,this.xs=!!e.xs,this.async=!1!==e.async,this.data=null!=e.data?e.data:null,this.agent=e.agent,this.isBinary=e.isBinary,this.supportsBinary=e.supportsBinary,this.enablesXDR=e.enablesXDR,this.pfx=e.pfx,this.key=e.key,this.passphrase=e.passphrase,this.cert=e.cert,this.ca=e.ca,this.ciphers=e.ciphers,this.rejectUnauthorized=e.rejectUnauthorized,this.create()}function h(){for(var e in a.requests)a.requests.hasOwnProperty(e)&&a.requests[e].abort()}u.exports=c,u.exports.Request=a,t(c,r),c.prototype.supportsBinary=!0,c.prototype.request=function(e){return(e=e||{}).uri=this.uri(),e.xd=this.xd,e.xs=this.xs,e.agent=this.agent||!1,e.supportsBinary=this.supportsBinary,e.enablesXDR=this.enablesXDR,e.pfx=this.pfx,e.key=this.key,e.passphrase=this.passphrase,e.cert=this.cert,e.ca=this.ca,e.ciphers=this.ciphers,e.rejectUnauthorized=this.rejectUnauthorized,new a(e)},c.prototype.doWrite=function(e,t){var e=this.request({method:"POST",data:e,isBinary:"string"!=typeof e&&void 0!==e}),n=this;e.on("success",t),e.on("error",function(e){n.onError("xhr post error",e)}),this.sendXhr=e},c.prototype.doPoll=function(){s("xhr poll");var e=this.request(),t=this;e.on("data",function(e){t.onData(e)}),e.on("error",function(e){t.onError("xhr poll error",e)}),this.pollXhr=e},e(a.prototype),a.prototype.create=function(){var e={agent:this.agent,xdomain:this.xd,xscheme:this.xs,enablesXDR:this.enablesXDR},t=(e.pfx=this.pfx,e.key=this.key,e.passphrase=this.passphrase,e.cert=this.cert,e.ca=this.ca,e.ciphers=this.ciphers,e.rejectUnauthorized=this.rejectUnauthorized,this.xhr=new i(e)),n=this;try{if(s("xhr open %s: %s",this.method,this.uri),t.open(this.method,this.uri,this.async),this.supportsBinary&&(t.responseType="arraybuffer"),"POST"==this.method)try{this.isBinary?t.setRequestHeader("Content-type","application/octet-stream"):t.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(e){}"withCredentials"in t&&(t.withCredentials=!0),this.hasXDR()?(t.onload=function(){n.onLoad()},t.onerror=function(){n.onError(t.responseText)}):t.onreadystatechange=function(){4==t.readyState&&(200==t.status||1223==t.status?n.onLoad():setTimeout(function(){n.onError(t.status)},0))},s("xhr data %s",this.data),t.send(this.data)}catch(e){return void setTimeout(function(){n.onError(e)},0)}o.document&&(this.index=a.requestsCount++,a.requests[this.index]=this)},a.prototype.onSuccess=function(){this.emit("success"),this.cleanup()},a.prototype.onData=function(e){this.emit("data",e),this.onSuccess()},a.prototype.onError=function(e){this.emit("error",e),this.cleanup(!0)},a.prototype.cleanup=function(e){if(null!=this.xhr){if(this.hasXDR()?this.xhr.onload=this.xhr.onerror=n:this.xhr.onreadystatechange=n,e)try{this.xhr.abort()}catch(e){}o.document&&delete a.requests[this.index],this.xhr=null}},a.prototype.onLoad=function(){var e,t;try{try{t=this.xhr.getResponseHeader("Content-Type").split(";")[0]}catch(e){}e="application/octet-stream"===t?this.xhr.response:this.supportsBinary?"ok":this.xhr.responseText}catch(e){this.onError(e)}null!=e&&this.onData(e)},a.prototype.hasXDR=function(){return void 0!==o.XDomainRequest&&!this.xs&&this.enablesXDR},a.prototype.abort=function(){this.cleanup()},o.document&&(a.requestsCount=0,a.requests={},o.attachEvent?o.attachEvent("onunload",h):o.addEventListener&&o.addEventListener("beforeunload",h,!1))}.call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./polling":18,"component-emitter":9,"component-inherit":21,debug:22,xmlhttprequest:20}],18:[function(e,t,n){var o=e("../transport"),i=e("parseqs"),r=e("engine.io-parser"),s=e("component-inherit"),c=e("debug")("engine.io-client:polling"),a=(t.exports=h,null!=new(e("xmlhttprequest"))({xdomain:!1}).responseType);function h(e){var t=e&&e.forceBase64;a&&!t||(this.supportsBinary=!1),o.call(this,e)}s(h,o),h.prototype.name="polling",h.prototype.doOpen=function(){this.poll()},h.prototype.pause=function(e){var t,n=this;function o(){c("paused"),n.readyState="paused",e()}this.readyState="pausing",this.polling||!this.writable?(t=0,this.polling&&(c("we are currently polling - waiting to pause"),t++,this.once("pollComplete",function(){c("pre-pause polling complete"),--t||o()})),this.writable||(c("we are currently writing - waiting to pause"),t++,this.once("drain",function(){c("pre-pause writing complete"),--t||o()}))):o()},h.prototype.poll=function(){c("polling"),this.polling=!0,this.doPoll(),this.emit("poll")},h.prototype.onData=function(e){var o=this;c("polling got data %s",e);r.decodePayload(e,this.socket.binaryType,function(e,t,n){if("opening"==o.readyState&&o.onOpen(),"close"==e.type)return o.onClose(),!1;o.onPacket(e)}),"closed"!=this.readyState&&(this.polling=!1,this.emit("pollComplete"),"open"==this.readyState?this.poll():c('ignoring poll - transport state "%s"',this.readyState))},h.prototype.doClose=function(){var e=this;function t(){c("writing close packet"),e.write([{type:"close"}])}"open"==this.readyState?(c("transport open - closing"),t()):(c("transport not open - deferring close"),this.once("open",t))},h.prototype.write=function(e){function t(){n.writable=!0,n.emit("drain")}this.writable=!1;var n=this;r.encodePayload(e,this.supportsBinary,function(e){n.doWrite(e,t)})},h.prototype.uri=function(){var e=this.query||{},t=this.secure?"https":"http",n="";return!1!==this.timestampRequests&&(e[this.timestampParam]=+new Date+"-"+o.timestamps++),this.supportsBinary||e.sid||(e.b64=1),e=i.encode(e),this.port&&("https"==t&&443!=this.port||"http"==t&&80!=this.port)&&(n=":"+this.port),e.length&&(e="?"+e),t+"://"+this.hostname+n+this.path+e}},{"../transport":14,"component-inherit":21,debug:22,"engine.io-parser":25,parseqs:33,xmlhttprequest:20}],19:[function(e,t,n){var o=e("../transport"),i=e("engine.io-parser"),r=e("parseqs"),s=e("component-inherit"),c=e("debug")("engine.io-client:websocket"),a=e("ws");function h(e){e&&e.forceBase64&&(this.supportsBinary=!1),o.call(this,e)}s(t.exports=h,o),h.prototype.name="websocket",h.prototype.supportsBinary=!0,h.prototype.doOpen=function(){var e,t;this.check()&&(e=this.uri(),(t={agent:this.agent}).pfx=this.pfx,t.key=this.key,t.passphrase=this.passphrase,t.cert=this.cert,t.ca=this.ca,t.ciphers=this.ciphers,t.rejectUnauthorized=this.rejectUnauthorized,this.ws=new a(e,void 0,t),void 0===this.ws.binaryType&&(this.supportsBinary=!1),this.ws.binaryType="arraybuffer",this.addEventListeners())},h.prototype.addEventListeners=function(){var t=this;this.ws.onopen=function(){t.onOpen()},this.ws.onclose=function(){t.onClose()},this.ws.onmessage=function(e){t.onData(e.data)},this.ws.onerror=function(e){t.onError("websocket error",e)}},"undefined"!=typeof navigator&&/iPad|iPhone|iPod/i.test(navigator.userAgent)&&(h.prototype.onData=function(e){var t=this;setTimeout(function(){o.prototype.onData.call(t,e)},0)}),h.prototype.write=function(e){var t=this;this.writable=!1;for(var n=0,o=e.length;n<o;n++)i.encodePacket(e[n],this.supportsBinary,function(e){try{t.ws.send(e)}catch(e){c("websocket closed before onclose event")}});setTimeout(function(){t.writable=!0,t.emit("drain")},0)},h.prototype.onClose=function(){o.prototype.onClose.call(this)},h.prototype.doClose=function(){void 0!==this.ws&&this.ws.close()},h.prototype.uri=function(){var e=this.query||{},t=this.secure?"wss":"ws",n="";return this.port&&("wss"==t&&443!=this.port||"ws"==t&&80!=this.port)&&(n=":"+this.port),this.timestampRequests&&(e[this.timestampParam]=+new Date),this.supportsBinary||(e.b64=1),(e=r.encode(e)).length&&(e="?"+e),t+"://"+this.hostname+n+this.path+e},h.prototype.check=function(){return!(!a||"__initialize"in a&&this.name===h.prototype.name)}},{"../transport":14,"component-inherit":21,debug:22,"engine.io-parser":25,parseqs:33,ws:35}],20:[function(e,t,n){var o=e("has-cors");t.exports=function(e){var t=e.xdomain,n=e.xscheme,e=e.enablesXDR;try{if("undefined"!=typeof XMLHttpRequest&&(!t||o))return new XMLHttpRequest}catch(e){}try{if("undefined"!=typeof XDomainRequest&&!n&&e)return new XDomainRequest}catch(e){}if(!t)try{return new ActiveXObject("Microsoft.XMLHTTP")}catch(e){}}},{"has-cors":38}],21:[function(e,t,n){t.exports=function(e,t){function n(){}n.prototype=t.prototype,e.prototype=new n,e.prototype.constructor=e}},{}],22:[function(e,t,i){function n(){var e;try{e=localStorage.debug}catch(e){}return e}(i=t.exports=e("./debug")).log=function(){return"object"==typeof console&&"function"==typeof console.log&&Function.prototype.apply.call(console.log,console,arguments)},i.formatArgs=function(){var t,n,e=arguments,o=this.useColors;return e[0]=(o?"%c":"")+this.namespace+(o?" %c":" ")+e[0]+(o?"%c ":" ")+"+"+i.humanize(this.diff),o&&(o="color: "+this.color,(e=[e[0],o,"color: inherit"].concat(Array.prototype.slice.call(e,1)))[n=t=0].replace(/%[a-z%]/g,function(e){"%%"!==e&&(t++,"%c"===e)&&(n=t)}),e.splice(n,0,o)),e},i.save=function(e){try{null==e?localStorage.removeItem("debug"):localStorage.debug=e}catch(e){}},i.load=n,i.useColors=function(){return"WebkitAppearance"in document.documentElement.style||window.console&&(console.firebug||console.exception&&console.table)||navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&31<=parseInt(RegExp.$1,10)},i.colors=["lightseagreen","forestgreen","goldenrod","dodgerblue","darkorchid","crimson"],i.formatters.j=function(e){return JSON.stringify(e)},i.enable(n())},{"./debug":23}],23:[function(e,t,s){(s=t.exports=function(e){function t(){}function n(){var o=n,e=+new Date,t=e-(c||e),i=(o.diff=t,o.prev=c,o.curr=e,c=e,null==o.useColors&&(o.useColors=s.useColors()),null==o.color&&o.useColors&&(o.color=s.colors[a++%s.colors.length]),Array.prototype.slice.call(arguments)),r=(i[0]=s.coerce(i[0]),"string"!=typeof i[0]&&(i=["%o"].concat(i)),0);i[0]=i[0].replace(/%([a-z%])/g,function(e,t){var n;return"%%"!==e&&(r++,"function"==typeof(t=s.formatters[t]))&&(n=i[r],e=t.call(o,n),i.splice(r,1),r--),e}),"function"==typeof s.formatArgs&&(i=s.formatArgs.apply(o,i)),(n.log||s.log||console.log.bind(console)).apply(o,i)}t.enabled=!1,n.enabled=!0;var o=s.enabled(e)?n:t;return o.namespace=e,o}).coerce=function(e){return e instanceof Error?e.stack||e.message:e},s.disable=function(){s.enable("")},s.enable=function(e){s.save(e);for(var t=(e||"").split(/[\s,]+/),n=t.length,o=0;o<n;o++)t[o]&&("-"===(e=t[o].replace(/\*/g,".*?"))[0]?s.skips.push(new RegExp("^"+e.substr(1)+"$")):s.names.push(new RegExp("^"+e+"$")))},s.enabled=function(e){var t,n;for(t=0,n=s.skips.length;t<n;t++)if(s.skips[t].test(e))return!1;for(t=0,n=s.names.length;t<n;t++)if(s.names[t].test(e))return!0;return!1},s.humanize=e("ms"),s.names=[],s.skips=[],s.formatters={};var c,a=0},{ms:24}],24:[function(e,t,n){var i=36e5,r=864e5;function s(e,t,n){if(!(e<t))return e<1.5*t?Math.floor(e/t)+" "+n:Math.ceil(e/t)+" "+n+"s"}t.exports=function(e,t){if(t=t||{},"string"==typeof e){var n=e;if(n=/^((?:\d+)?\.?\d+) *(ms|seconds?|s|minutes?|m|hours?|h|days?|d|years?|y)?$/i.exec(n)){var o=parseFloat(n[1]);switch((n[2]||"ms").toLowerCase()){case"years":case"year":case"y":return 315576e5*o;case"days":case"day":case"d":return o*r;case"hours":case"hour":case"h":return o*i;case"minutes":case"minute":case"m":return 6e4*o;case"seconds":case"second":case"s":return 1e3*o;case"ms":return o}}}else return t.long?s(n=e,r,"day")||s(n,i,"hour")||s(n,6e4,"minute")||s(n,1e3,"second")||n+" ms":r<=(t=e)?Math.round(t/r)+"d":i<=t?Math.round(t/i)+"h":6e4<=t?Math.round(t/6e4)+"m":1e3<=t?Math.round(t/1e3)+"s":t+"ms"}},{}],25:[function(a,e,v){!function(g){var e=a("./keys"),t=a("has-binary"),f=a("arraybuffer.slice"),i=a("base64-arraybuffer"),s=a("after"),y=a("utf8"),n=navigator.userAgent.match(/Android/i),o=/PhantomJS/i.test(navigator.userAgent),m=n||o,b=(v.protocol=3,v.packets={open:0,close:1,ping:2,pong:3,message:4,upgrade:5,noop:6}),r=e(b),d={type:"error",data:"parser error"},k=a("blob");function c(e,t,n){for(var i=new Array(e.length),o=s(e.length,n),r=0;r<e.length;r++)((n,e,o)=>{t(e,function(e,t){i[n]=t,o(e,i)})})(r,e[r],o)}v.encodePacket=function(e,t,n,o){"function"==typeof t&&(o=t,t=!1),"function"==typeof n&&(o=n,n=null);var i,r,s,c,a=void 0===e.data?void 0:e.data.buffer||e.data;if(g.ArrayBuffer&&a instanceof ArrayBuffer){var h=e,l=o;if(!(u=t))return v.encodeBase64Packet(h,l);var u=h.data,p=new Uint8Array(u),f=new Uint8Array(1+u.byteLength);f[0]=b[h.type];for(var d=0;d<p.length;d++)f[d+1]=p[d];return l(f.buffer)}return k&&a instanceof g.Blob?(u=e,h=o,(l=t)?m?(i=u,s=h,(r=l)?((c=new FileReader).onload=function(){i.data=c.result,v.encodePacket(i,r,!0,s)},c.readAsArrayBuffer(i.data)):v.encodeBase64Packet(i,s)):((l=new Uint8Array(1))[0]=b[u.type],l=new k([l.buffer,u.data]),h(l)):v.encodeBase64Packet(u,h)):a&&a.base64?(t=e,a=o,t="b"+v.packets[e.type]+e.data.data,a(t)):(a=b[e.type],void 0!==e.data&&(a+=n?y.encode(String(e.data)):String(e.data)),o(""+a))},v.encodeBase64Packet=function(t,n){var o,i,r="b"+v.packets[t.type];if(k&&t.data instanceof k)return(o=new FileReader).onload=function(){var e=o.result.split(",")[1];n(r+e)},o.readAsDataURL(t.data);try{i=String.fromCharCode.apply(null,new Uint8Array(t.data))}catch(e){for(var s=new Uint8Array(t.data),c=new Array(s.length),a=0;a<s.length;a++)c[a]=s[a];i=String.fromCharCode.apply(null,c)}return r+=g.btoa(i),n(r)},v.safeDecode=function(t){if("string"!=typeof t)return t;if(t.includes("�"))return console.warn("Skipping decoding: Possible corrupted UTF-8 sequence"),t;try{var e=(new TextEncoder).encode(t);if(t===new TextDecoder("utf-8").decode(e))return console.log("Already UTF-8, skipping decode"),t}catch(e){return console.error("Encoding check failed:",e),t}try{return y.decode(t)}catch(e){return console.error("UTF-8 Decoding Error:",e),t}},v.decodePacket=function(e,t,n){if("string"==typeof e||void 0===e){if("b"==e.charAt(0))return v.decodeBase64Packet(e.substr(1),t);if(n)try{e=v.safeDecode(e)}catch(e){}var o=e.charAt(0);return Number(o)==o&&r[o]?1<e.length?{type:r[o],data:e.substring(1)}:{type:r[o]}:d}o=new Uint8Array(e)[0],n=f(e,1);return k&&"blob"===t&&(n=new k([n])),{type:r[o],data:n}},v.decodeBase64Packet=function(e,t){var n,o=r[e.charAt(0)];return g.ArrayBuffer?(n=i.decode(e.substr(1)),{type:o,data:n="blob"===t&&k?new k([n]):n}):{type:o,data:{base64:!0,data:e.substr(1)}}},v.encodePayload=function(e,n,o){"function"==typeof n&&(o=n,n=null);var i=t(e);return n&&i?k&&!m?v.encodePayloadAsBlob(e,o):v.encodePayloadAsArrayBuffer(e,o):e.length?void c(e,function(e,t){v.encodePacket(e,!!i&&n,!0,function(e){t(null,e.length+":"+e)})},function(e,t){return o(t.join(""))}):o("0:")},v.decodePayload=function(e,t,n){if("string"!=typeof e)return v.decodePayloadAsBinary(e,t,n);if("function"==typeof t&&(n=t,t=null),""==e)return n(d,0,1);for(var o,i="",r=0,s=e.length;r<s;r++){var c=e.charAt(r);if(":"!=c)i+=c;else{if(""==i||i!=(o=Number(i)))return n(d,0,1);if((c=e.substr(r+1,o)).length){if(c=v.decodePacket(c,t,!0),d.type==c.type&&d.data==c.data)return n(d,0,1);if(!1===n(c,r+o,s))return}r+=o,i=""}}return""!=i?n(d,0,1):void 0},v.encodePayloadAsArrayBuffer=function(e,o){if(!e.length)return o(new ArrayBuffer(0));c(e,function(e,t){v.encodePacket(e,!0,!0,function(e){return t(null,e)})},function(e,t){var n=t.reduce(function(e,t){t="string"==typeof t?t.length:t.byteLength;return e+t.toString().length+t+2},0),s=new Uint8Array(n),c=0;return t.forEach(function(e){var t="string"==typeof e,n=e;if(t){for(var o=new Uint8Array(e.length),i=0;i<e.length;i++)o[i]=e.charCodeAt(i);n=o.buffer}s[c++]=t?0:1;for(var r=n.byteLength.toString(),i=0;i<r.length;i++)s[c++]=parseInt(r[i]);s[c++]=255;for(o=new Uint8Array(n),i=0;i<o.length;i++)s[c++]=o[i]}),o(s.buffer)})},v.encodePayloadAsBlob=function(e,n){c(e,function(e,s){v.encodePacket(e,!0,!0,function(e){var t=new Uint8Array(1);if(t[0]=1,"string"==typeof e){for(var n=new Uint8Array(e.length),o=0;o<e.length;o++)n[o]=e.charCodeAt(o);e=n.buffer,t[0]=0}for(var i=(e instanceof ArrayBuffer?e.byteLength:e.size).toString(),r=new Uint8Array(i.length+1),o=0;o<i.length;o++)r[o]=parseInt(i[o]);r[i.length]=255,k&&(t=new k([t.buffer,r.buffer,e]),s(null,t))})},function(e,t){return n(new k(t))})},v.decodePayloadAsBinary=function(e,n,o){"function"==typeof n&&(o=n,n=null);for(var t=e,i=[],r=!1;0<t.byteLength;){for(var s=new Uint8Array(t),c=0===s[0],a="",h=1;255!=s[h];h++){if(310<a.length){r=!0;break}a+=s[h]}if(r)return o(d,0,1);var t=f(t,2+a.length),a=parseInt(a),l=f(t,0,a);if(c)try{l=String.fromCharCode.apply(null,new Uint8Array(l))}catch(e){for(var u=new Uint8Array(l),l="",h=0;h<u.length;h++)l+=String.fromCharCode(u[h])}i.push(l),t=f(t,a)}var p=i.length;i.forEach(function(e,t){o(v.decodePacket(e,n,!0),t,p)})}}.call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./keys":26,after:27,"arraybuffer.slice":28,"base64-arraybuffer":29,blob:30,"has-binary":36,utf8:31}],26:[function(e,t,n){t.exports=Object.keys||function(e){var t,n=[],o=Object.prototype.hasOwnProperty;for(t in e)o.call(e,t)&&n.push(t);return n}},{}],27:[function(e,t,n){function s(){}t.exports=function(e,n,o){var i=!1;return o=o||s,0===(r.count=e)?n():r;function r(e,t){if(r.count<=0)throw new Error("after called too many times");--r.count,e?(i=!0,n(e),n=o):0!==r.count||i||n(null,t)}}},{}],28:[function(e,t,n){t.exports=function(e,t,n){var o=e.byteLength;if(t=t||0,n=n||o,e.slice)return e.slice(t,n);if(t<0&&(t+=o),n<0&&(n+=o),o<n&&(n=o),o<=t||n<=t||0===o)return new ArrayBuffer(0);for(var i=new Uint8Array(e),r=new Uint8Array(n-t),s=t,c=0;s<n;s++,c++)r[c]=i[s];return r.buffer}},{}],29:[function(e,t,n){var l;l="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",n.encode=function(e){for(var t=new Uint8Array(e),n=t.length,o="",i=0;i<n;i+=3)o=(o=(o=(o+=l[t[i]>>2])+l[(3&t[i])<<4|t[i+1]>>4])+l[(15&t[i+1])<<2|t[i+2]>>6])+l[63&t[i+2]];return n%3==2?o=o.substring(0,o.length-1)+"=":n%3==1&&(o=o.substring(0,o.length-2)+"=="),o},n.decode=function(e){for(var t,n,o,i,r=.75*e.length,s=e.length,c=0,r=("="===e[e.length-1]&&(r--,"="===e[e.length-2])&&r--,new ArrayBuffer(r)),a=new Uint8Array(r),h=0;h<s;h+=4)t=l.indexOf(e[h]),n=l.indexOf(e[h+1]),o=l.indexOf(e[h+2]),i=l.indexOf(e[h+3]),a[c++]=t<<2|n>>4,a[c++]=(15&n)<<4|o>>2,a[c++]=(3&o)<<6|63&i;return r}},{}],30:[function(e,a,t){!function(e){var i=e.BlobBuilder||e.WebKitBlobBuilder||e.MSBlobBuilder||e.MozBlobBuilder,t=(()=>{try{return 2===new Blob(["hi"]).size}catch(e){return!1}})(),n=t&&(()=>{try{return 2===new Blob([new Uint8Array([1,2])]).size}catch(e){return!1}})(),o=i&&i.prototype.append&&i.prototype.getBlob;function r(e){for(var t=0;t<e.length;t++){var n,o,i=e[t];i.buffer instanceof ArrayBuffer&&(n=i.buffer,i.byteLength!==n.byteLength&&((o=new Uint8Array(i.byteLength)).set(new Uint8Array(n,i.byteOffset,i.byteLength)),n=o.buffer),e[t]=n)}}function s(e,t){t=t||{};var n=new i;r(e);for(var o=0;o<e.length;o++)n.append(e[o]);return t.type?n.getBlob(t.type):n.getBlob()}function c(e,t){return r(e),new Blob(e,t||{})}a.exports=t?n?e.Blob:c:o?s:void 0}.call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],31:[function(e,g,y){!function(e){var a,h,l,t=this,n="object"==typeof y&&y,o="object"==typeof g&&g&&g.exports==n&&g,e="object"==typeof e&&e,u=(e.global!==e&&e.window!==e||(t=e),String.fromCharCode);function p(e){for(var t,n,o=[],i=0,r=e.length;i<r;)55296<=(t=e.charCodeAt(i++))&&t<=56319&&i<r?56320==(64512&(n=e.charCodeAt(i++)))?o.push(((1023&t)<<10)+(1023&n)+65536):(o.push(t),i--):o.push(t);return o}function f(e){if(55296<=e&&e<=57343)throw Error("Lone surrogate U+"+e.toString(16).toUpperCase()+" is not a scalar value")}function c(e,t){return u(e>>t&63|128)}function d(){if(h<=l)throw Error("Invalid byte index");var e=255&a[l];if(l++,128==(192&e))return 63&e;throw Error("Invalid continuation byte")}var i={version:"2.0.0",encode:function(e){for(var t,n,o=p(e),i=o.length,r=-1,s="";++r<i;)t=o[r],s+=(n=void 0,0==(4294967168&(t=t))?u(t):(n="",0==(4294965248&t)?n=u(t>>6&31|192):0==(4294901760&t)?(f(t),n=u(t>>12&15|224),n+=c(t,6)):0==(4292870144&t)&&(n=u(t>>18&7|240),n=(n+=c(t,12))+c(t,6)),n+u(63&t|128)));return s},decode:function(e){a=p(e),h=a.length,l=0;for(var t,n=[];!1!==(t=(()=>{var e,t;if(h<l)throw Error("Invalid byte index");if(l==h)return!1;if(e=255&a[l],l++,0==(128&e))return e;if(192==(224&e)){if(128<=(t=(31&e)<<6|d()))return t;throw Error("Invalid continuation byte")}if(224==(240&e)){if(2048<=(t=(15&e)<<12|d()<<6|d()))return f(t),t;throw Error("Invalid continuation byte")}if(240==(248&e)&&65536<=(t=(15&e)<<18|d()<<12|d()<<6|d())&&t<=1114111)return t;throw Error("Invalid UTF-8 detected")})());)n.push(t);for(var o,i=n,r=i.length,s=-1,c="";++s<r;)65535<(o=i[s])&&(c+=u((o-=65536)>>>10&1023|55296),o=56320|1023&o),c+=u(o);return c}};if(n&&!n.nodeType)if(o)o.exports=i;else{var r,s={}.hasOwnProperty;for(r in i)s.call(i,r)&&(n[r]=i[r])}else t.utf8=i}.call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],32:[function(e,a,t){!function(t){var n=/^[\],:{}\s]*$/,o=/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,i=/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,r=/(?:^|:|,)(?:\s*\[)+/g,s=/^\s+/,c=/\s+$/;a.exports=function(e){return"string"==typeof e&&e?(e=e.replace(s,"").replace(c,""),t.JSON&&JSON.parse?JSON.parse(e):n.test(e.replace(o,"@").replace(i,"]").replace(r,""))?new Function("return "+e)():void 0):null}}.call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],33:[function(e,t,n){n.encode=function(e){var t,n="";for(t in e)e.hasOwnProperty(t)&&(n.length&&(n+="&"),n+=encodeURIComponent(t)+"="+encodeURIComponent(e[t]));return n},n.decode=function(e){for(var t={},n=e.split("&"),o=0,i=n.length;o<i;o++){var r=n[o].split("=");t[decodeURIComponent(r[0])]=decodeURIComponent(r[1])}return t}},{}],34:[function(e,t,n){var c=/^(?:(?![^:@]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,a=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];t.exports=function(e){for(var t=e,n=e.indexOf("["),o=e.indexOf("]"),i=(-1!=n&&-1!=o&&(e=e.substring(0,n)+e.substring(n,o).replace(/:/g,";")+e.substring(o,e.length)),c.exec(e||"")),r={},s=14;s--;)r[a[s]]=i[s]||"";return-1!=n&&-1!=o&&(r.source=t,r.host=r.host.substring(1,r.host.length-1).replace(/;/g,":"),r.authority=r.authority.replace("[","").replace("]","").replace(/;/g,":"),r.ipv6uri=!0),r}},{}],35:[function(e,t,n){var o=function(){return this}(),i=o.WebSocket||o.MozWebSocket;function r(e,t,n){t=t?new i(e,t):new i(e);return t}t.exports=i?r:null,i&&(r.prototype=i.prototype)},{}],36:[function(e,t,n){!function(i){var r=e("isarray");t.exports=function(e){return function e(t){if(t){if(i.Buffer&&i.Buffer.isBuffer(t)||i.ArrayBuffer&&t instanceof ArrayBuffer||i.Blob&&t instanceof Blob||i.File&&t instanceof File)return!0;if(r(t)){for(var n=0;n<t.length;n++)if(e(t[n]))return!0}else if(t&&"object"==typeof t)for(var o in t=t.toJSON?t.toJSON():t)if(Object.prototype.hasOwnProperty.call(t,o)&&e(t[o]))return!0}return!1}(e)}}.call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{isarray:37}],37:[function(e,t,n){t.exports=Array.isArray||function(e){return"[object Array]"==Object.prototype.toString.call(e)}},{}],38:[function(e,t,n){e=e("global");try{t.exports="XMLHttpRequest"in e&&"withCredentials"in new e.XMLHttpRequest}catch(e){t.exports=!1}},{global:39}],39:[function(e,t,n){t.exports=function(){return this}()},{}],40:[function(e,t,n){var o=[].indexOf;t.exports=function(e,t){if(o)return e.indexOf(t);for(var n=0;n<e.length;++n)if(e[n]===t)return n;return-1}},{}],41:[function(e,t,n){var o=Object.prototype.hasOwnProperty;n.keys=Object.keys||function(e){var t,n=[];for(t in e)o.call(e,t)&&n.push(t);return n},n.values=function(e){var t,n=[];for(t in e)o.call(e,t)&&n.push(e[t]);return n},n.merge=function(e,t){for(var n in t)o.call(t,n)&&(e[n]=t[n]);return e},n.length=function(e){return n.keys(e).length},n.isEmpty=function(e){return 0==n.length(e)}},{}],42:[function(e,t,n){var i=/^(?:(?![^:@]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,r=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];t.exports=function(e){for(var t=i.exec(e||""),n={},o=14;o--;)n[r[o]]=t[o]||"";return n}},{}],43:[function(e,t,n){!function(l){var u=e("isarray"),p=e("./is-buffer");n.deconstructPacket=function(e){var s=[],t=e.data;return e.data=function e(t){if(!t)return t;var n;if(p(t))return n={_placeholder:!0,num:s.length},s.push(t),n;if(u(t))for(var o=new Array(t.length),i=0;i<t.length;i++)o[i]=e(t[i]);else{if("object"!=typeof t||t instanceof Date)return t;var r,o={};for(r in t)o[r]=e(t[r])}return o}(t),e.attachments=s.length,{packet:e,buffers:s}},n.reconstructPacket=function(e,i){return e.data=function e(t){if(t&&t._placeholder)return i[t.num];if(u(t))for(var n=0;n<t.length;n++)t[n]=e(t[n]);else if(t&&"object"==typeof t)for(var o in t)t[o]=e(t[o]);return t}(e.data),e.attachments=void 0,e},n.removeBlobs=function(e,c){var a=0,h=e;!function e(t,n,o){if(t)if(l.Blob&&t instanceof Blob||l.File&&t instanceof File){a++;var i=new FileReader;i.onload=function(){o?o[n]=this.result:h=this.result,--a||c(h)},i.readAsArrayBuffer(t)}else if(u(t))for(var r=0;r<t.length;r++)e(t[r],r,t);else if(t&&"object"==typeof t&&!p(t))for(var s in t)e(t[s],s,t)}(h),a||c(h)}}.call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./is-buffer":45,isarray:46}],44:[function(e,t,s){var c=e("debug")("socket.io-parser"),a=e("json3"),n=(e("isarray"),e("component-emitter")),o=e("./binary"),i=e("./is-buffer");function r(){}function h(e){var t="",n=!1;return t+=e.type,s.BINARY_EVENT!=e.type&&s.BINARY_ACK!=e.type||(t=t+e.attachments+"-"),e.nsp&&"/"!=e.nsp&&(n=!0,t+=e.nsp),null!=e.id&&(n&&(t+=",",n=!1),t+=e.id),null!=e.data&&(n&&(t+=","),t+=a.stringify(e.data)),c("encoded %j as %s",e,t),t}function l(){this.reconstructor=null}function u(e){this.reconPack=e,this.buffers=[]}function p(){return{type:s.ERROR,data:"parser error"}}s.protocol=4,s.types=["CONNECT","DISCONNECT","EVENT","BINARY_EVENT","ACK","BINARY_ACK","ERROR"],s.CONNECT=0,s.DISCONNECT=1,s.EVENT=2,s.ACK=3,s.ERROR=4,s.BINARY_EVENT=5,s.BINARY_ACK=6,s.Encoder=r,s.Decoder=l,r.prototype.encode=function(e,t){var n;c("encoding packet %j",e),s.BINARY_EVENT==e.type||s.BINARY_ACK==e.type?(n=t,o.removeBlobs(e,function(e){var t=h((e=o.deconstructPacket(e)).packet);(e=e.buffers).unshift(t),n(e)})):t([h(e)])},n(l.prototype),l.prototype.add=function(e){var t;if("string"==typeof e)t=(e=>{var t={},n=0;if(t.type=Number(e.charAt(0)),null==s.types[t.type])return p();if(s.BINARY_EVENT==t.type||s.BINARY_ACK==t.type){for(var o="";"-"!=e.charAt(++n)&&(o+=e.charAt(n),n!=e.length););if(o!=Number(o)||"-"!=e.charAt(n))throw new Error("Illegal attachments");t.attachments=Number(o)}if("/"==e.charAt(n+1))for(t.nsp="";++n;){if(","==(i=e.charAt(n)))break;if(t.nsp+=i,n==e.length)break}else t.nsp="/";var i,r=e.charAt(n+1);if(""!==r&&Number(r)==r){for(t.id="";++n;){if(null==(i=e.charAt(n))||Number(i)!=i){--n;break}if(t.id+=e.charAt(n),n==e.length)break}t.id=Number(t.id)}if(e.charAt(++n))try{t.data=a.parse(e.substr(n))}catch(e){return p()}return c("decoded %s as %j",e,t),t})(e),(s.BINARY_EVENT!=t.type&&s.BINARY_ACK!=t.type||(this.reconstructor=new u(t),0===this.reconstructor.reconPack.attachments))&&this.emit("decoded",t);else{if(!i(e)&&!e.base64)throw new Error("Unknown type: "+e);if(!this.reconstructor)throw new Error("got binary data when not reconstructing a packet");(t=this.reconstructor.takeBinaryData(e))&&(this.reconstructor=null,this.emit("decoded",t))}},l.prototype.destroy=function(){this.reconstructor&&this.reconstructor.finishedReconstruction()},u.prototype.takeBinaryData=function(e){return this.buffers.push(e),this.buffers.length==this.reconPack.attachments?(e=o.reconstructPacket(this.reconPack,this.buffers),this.finishedReconstruction(),e):null},u.prototype.finishedReconstruction=function(){this.reconPack=null,this.buffers=[]}},{"./binary":43,"./is-buffer":45,"component-emitter":9,debug:10,isarray:46,json3:47}],45:[function(e,n,t){!function(t){n.exports=function(e){return t.Buffer&&t.Buffer.isBuffer(e)||t.ArrayBuffer&&e instanceof ArrayBuffer}}.call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],46:[function(e,t,n){t.exports=e(37)},{}],47:[function(e,N,t){var w,I,l,x,C,_,c,S,n,A,u,a,o,T,h,R,B,p,f,d,g,y,i,r,s,m,E,b=this,U={}.toString,k="object"==typeof JSON&&JSON,v="object"==typeof t&&t&&!t.nodeType&&t,O=(v&&k?(v.stringify=k.stringify,v.parse=k.parse):v=b.JSON=k||{},new Date(-0xc782b5b800cec));try{O=-109252==O.getUTCFullYear()&&0===O.getUTCMonth()&&1===O.getUTCDate()&&10==O.getUTCHours()&&37==O.getUTCMinutes()&&6==O.getUTCSeconds()&&708==O.getUTCMilliseconds()}catch(e){}function D(e){if(D[e]!==I)return D[e];var t;if("bug-string-char-index"==e)t="a"!="a"[0];else if("json"==e)t=D("json-stringify")&&D("json-parse");else{var n='{"a":[1,true,false,null,"\\u0000\\b\\n\\f\\r\\t"]}';if("json-stringify"==e){var o=v.stringify,i="function"==typeof o&&O;if(i){(r=function(){return 1}).toJSON=r;try{i="0"===o(0)&&"0"===o(new Number)&&'""'==o(new String)&&o(U)===I&&o(I)===I&&o()===I&&"1"===o(r)&&"[1]"==o([r])&&"[null]"==o([I])&&"null"==o(null)&&"[null,null,null]"==o([I,U,null])&&o({a:[r,!0,!1,null,"\0\b\n\f\r\t"]})==n&&"1"===o(null,r)&&"[\n 1,\n 2\n]"==o([1,2],null,1)&&'"-271821-04-20T00:00:00.000Z"'==o(new Date(-864e13))&&'"+275760-09-13T00:00:00.000Z"'==o(new Date(864e13))&&'"-000001-01-01T00:00:00.000Z"'==o(new Date(-621987552e5))&&'"1969-12-31T23:59:59.999Z"'==o(new Date(-1))}catch(e){i=!1}}t=i}if("json-parse"==e){o=v.parse;if("function"==typeof o)try{if(0===o("0")&&!o(!1)){var r,s=5==(r=o(n)).a.length&&1===r.a[0];if(s){try{s=!o('"\t"')}catch(e){}if(s)try{s=1!==o("01")}catch(e){}if(s)try{s=1!==o("1.")}catch(e){}}}}catch(e){s=!1}t=s}}return D[e]=!!t}D("json")||(l="[object Function]",x="[object Number]",C="[object String]",_="[object Array]",c=D("bug-string-char-index"),O||(S=Math.floor,n=[0,31,59,90,120,151,181,212,243,273,304,334],A=function(e,t){return n[t]+365*(e-1970)+S((e-1969+(t=+(1<t)))/4)-S((e-1901+t)/100)+S((e-1601+t)/400)}),w=(w={}.hasOwnProperty)||function(e){var n,t={__proto__:null};return t.__proto__={toString:1},w=t.toString!=U?function(e){var t=this.__proto__,e=e in(this.__proto__=null,this);return this.__proto__=t,e}:(n=t.constructor,function(e){var t=(this.constructor||n).prototype;return e in this&&!(e in t&&this[e]===t[e])}),t=null,w.call(this,e)},u={boolean:1,number:1,string:1,undefined:1},E=function(e,t){var n,h,o,i=0;for(o in(n=function(){this.valueOf=0}).prototype.valueOf=0,h=new n)w.call(h,o)&&i++;return h=null,(E=i?2==i?function(e,t){var n,o={},i=U.call(e)==l;for(n in e)i&&"prototype"==n||w.call(o,n)||(o[n]=1,!w.call(e,n))||t(n)}:function(e,t){var n,o,i=U.call(e)==l;for(n in e)i&&"prototype"==n||!w.call(e,n)||(o="constructor"===n)||t(n);(o||w.call(e,n="constructor"))&&t(n)}:(h=["valueOf","toString","toLocaleString","propertyIsEnumerable","isPrototypeOf","hasOwnProperty","constructor"],function(e,t){var n,o,i,r,s,c=U.call(e)==l,a=c||"function"==typeof e.constructor||("object"==(s=typeof(i=e)[r="hasOwnProperty"])?!i[r]:u[s])?w:e.hasOwnProperty;for(n in e)c&&"prototype"==n||!a.call(e,n)||t(n);for(o=h.length;n=h[--o];a.call(e,n)&&t(n));}))(e,t)},D("json-stringify")||(a={92:"\\\\",34:'\\"',8:"\\b",12:"\\f",10:"\\n",13:"\\r",9:"\\t"},o="000000",T=function(e,t){return(o+(t||0)).slice(-e)},h="\\u00",R=function(e){var t,n='"',o=0,i=e.length,r=10<i&&c;for(r&&(t=e.split(""));o<i;o++){var s=e.charCodeAt(o);switch(s){case 8:case 9:case 10:case 12:case 13:case 34:case 92:n+=a[s];break;default:s<32?n+=h+T(2,s.toString(16)):n+=r?t[o]:c?e.charAt(o):e[o]}}return n+'"'},B=function(e,t,n,o,i,r,s){var c,a,h,l,u,p,f,d,g,y,m,b,k,v;try{c=t[e]}catch(e){}if("object"==typeof c&&c)if("[object Date]"!=(a=U.call(c))||w.call(c,"toJSON"))"function"==typeof c.toJSON&&(a!=x&&a!=C&&a!=_||w.call(c,"toJSON"))&&(c=c.toJSON(e));else if(-1/0<c&&c<1/0){if(A){for(u=S(c/864e5),h=S(u/365.2425)+1970-1;A(h+1,0)<=u;h++);for(l=S((u-A(h,0))/30.42);A(h,l+1)<=u;l++);u=1+u-A(h,l),k=S((d=(c%864e5+864e5)%864e5)/36e5)%24,p=S(d/6e4)%60,f=S(d/1e3)%60,d=d%1e3}else h=c.getUTCFullYear(),l=c.getUTCMonth(),u=c.getUTCDate(),k=c.getUTCHours(),p=c.getUTCMinutes(),f=c.getUTCSeconds(),d=c.getUTCMilliseconds();c=(h<=0||1e4<=h?(h<0?"-":"+")+T(6,h<0?-h:h):T(4,h))+"-"+T(2,l+1)+"-"+T(2,u)+"T"+T(2,k)+":"+T(2,p)+":"+T(2,f)+"."+T(3,d)+"Z"}else c=null;if(null===(c=n?n.call(t,e,c):c))return"null";if("[object Boolean]"==(a=U.call(c)))return""+c;if(a==x)return-1/0<c&&c<1/0?""+c:"null";if(a==C)return R(""+c);if("object"==typeof c){for(b=s.length;b--;)if(s[b]===c)throw TypeError();if(s.push(c),g=[],k=r,r+=i,a==_){for(m=0,b=c.length;m<b;m++)y=B(m,c,n,o,i,r,s),g.push(y===I?"null":y);v=g.length?i?"[\n"+r+g.join(",\n"+r)+"\n"+k+"]":"["+g.join(",")+"]":"[]"}else E(o||c,function(e){var t=B(e,c,n,o,i,r,s);t!==I&&g.push(R(e)+":"+(i?" ":"")+t)}),v=g.length?i?"{\n"+r+g.join(",\n"+r)+"\n"+k+"}":"{"+g.join(",")+"}":"{}";return s.pop(),v}},v.stringify=function(e,t,n){var o,i,r;if("function"==typeof t||"object"==typeof t&&t)if((r=U.call(t))==l)i=t;else if(r==_)for(var s,c={},a=0,h=t.length;a<h;s=t[a++],(r=U.call(s))!=C&&r!=x||(c[s]=1));if(n)if((r=U.call(n))==x){if(0<(n-=n%1))for(o="",10<n&&(n=10);o.length<n;o+=" ");}else r==C&&(o=n.length<=10?n:n.slice(0,10));return B("",((s={})[""]=e,s),i,c,o,"",[])}),D("json-parse"))||(p=String.fromCharCode,f={92:"\\",34:'"',47:"/",98:"\b",116:"\t",110:"\n",102:"\f",114:"\r"},y=function(){throw d=g=null,SyntaxError()},i=function(){for(var e,t,n,o,i,r=g,s=r.length;d<s;)switch(i=r.charCodeAt(d)){case 9:case 10:case 13:case 32:d++;break;case 123:case 125:case 91:case 93:case 58:case 44:return e=c?r.charAt(d):r[d],d++,e;case 34:for(e="@",d++;d<s;)if((i=r.charCodeAt(d))<32)y();else if(92==i)switch(i=r.charCodeAt(++d)){case 92:case 34:case 47:case 98:case 116:case 110:case 102:case 114:e+=f[i],d++;break;case 117:for(t=++d,n=d+4;d<n;d++)48<=(i=r.charCodeAt(d))&&i<=57||97<=i&&i<=102||65<=i&&i<=70||y();e+=p("0x"+r.slice(t,d));break;default:y()}else{if(34==i)break;for(i=r.charCodeAt(d),t=d;32<=i&&92!=i&&34!=i;)i=r.charCodeAt(++d);e+=r.slice(t,d)}if(34==r.charCodeAt(d))return d++,e;y();default:if(t=d,45==i&&(o=!0,i=r.charCodeAt(++d)),48<=i&&i<=57){for(48==i&&48<=(i=r.charCodeAt(d+1))&&i<=57&&y(),o=!1;d<s&&48<=(i=r.charCodeAt(d))&&i<=57;d++);if(46==r.charCodeAt(d)){for(n=++d;n<s&&48<=(i=r.charCodeAt(n))&&i<=57;n++);n==d&&y(),d=n}if(101==(i=r.charCodeAt(d))||69==i){for(43!=(i=r.charCodeAt(++d))&&45!=i||d++,n=d;n<s&&48<=(i=r.charCodeAt(n))&&i<=57;n++);n==d&&y(),d=n}return+r.slice(t,d)}if(o&&y(),"true"==r.slice(d,d+4))return d+=4,!0;if("false"==r.slice(d,d+5))return d+=5,!1;if("null"==r.slice(d,d+4))return d+=4,null;y()}return"$"},r=function(e){var t,n;if("$"==e&&y(),"string"==typeof e){if("@"==(c?e.charAt(0):e[0]))return e.slice(1);if("["==e){for(t=[];"]"!=(e=i());n=n||!0)!n||","==e&&"]"!=(e=i())||y(),","==e&&y(),t.push(r(e));return t}if("{"==e){for(t={};"}"!=(e=i());n=n||!0)!n||","==e&&"}"!=(e=i())||y(),","!=e&&"string"==typeof e&&"@"==(c?e.charAt(0):e[0])&&":"==i()||y(),t[e.slice(1)]=r(i());return t}y()}return e},s=function(e,t,n){n=m(e,t,n);n===I?delete e[t]:e[t]=n},m=function(e,t,n){var o,i=e[t];if("object"==typeof i&&i)if(U.call(i)==_)for(o=i.length;o--;)s(i,o,n);else E(i,function(e){s(i,e,n)});return n.call(e,t,i)},v.parse=function(e,t){var n;return d=0,g=""+e,e=r(i()),"$"!=i()&&y(),d=g=null,t&&U.call(t)==l?m(((n={})[""]=e,n),"",t):e})},{}],48:[function(e,t,n){t.exports=function(e,t){for(var n=[],o=(t=t||0)||0;o<e.length;o++)n[o-t]=e[o];return n}},{}]},{},[1])(1)},e})();
