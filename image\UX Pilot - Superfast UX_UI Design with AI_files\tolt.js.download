(()=>{let o="https://58qr5yci46.execute-api.us-east-1.amazonaws.com/v1",c=null,d=null;function t(t,e){r.push({command:t,data:e})}var r;window.tolt_referral=null,window.tolt_data=null,window.tolt=window.tolt||(r=[],t.queue=r,t);let s=(e,t)=>{((e,r)=>{let o=0,n=setInterval(()=>{var t=document.querySelectorAll(e);(0<t.length||10<=o)&&(clearInterval(n),0<t.length)&&r(t),o++},500)})({payment_links:t?`a[href^="${t}"], a[href^="https://buy.stripe.com"]`:'a[href^="https://buy.stripe.com"]',pricing_table:"stripe-pricing-table",buy_button:"stripe-buy-button"}[e],t=>{("payment_links"===e?t=>{if(window.tolt_referral&&0<t.length)for(var e=0;e<t.length;e++){var r=t[e];-1===r.href.indexOf("client_reference_id")&&(-1===r.href.indexOf("?")?r.href=r.href+"?client_reference_id="+window.tolt_referral:r.href=r.href+"&client_reference_id="+window.tolt_referral)}}:t=>{if(window.tolt_referral&&0<t.length)for(var e=0;e<t.length;e++){var r=t[e];r.hasAttribute("client-reference-id")||r.setAttribute("client-reference-id",window.tolt_referral)}})(t)})};function u(t,e,r){var o=new Date,r=(o.setTime(o.getTime()+24*r*60*60*1e3),"expires="+o.toUTCString());document.cookie="tolt_referral"===t?`tolt_referral=${e};${r};path=/;domain=${d};samesite=none;secure`:`${t}=${encodeURIComponent(JSON.stringify(e))};${r};path=/;domain=${d};samesite=none;secure`}function f(e){e=("; "+document.cookie).split(`; ${e}=`);if(2===e.length){e=decodeURIComponent(e.pop().split(";").shift());try{return JSON.parse(e)}catch(t){return e}}}async function p(e,t){if(c&&d)try{var r=await(await fetch(o+`/${e}?id=`+window.tolt_referral,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...t,public_id:c})})).json();return"signup"===e&&r.customer_id&&(window.tolt_data.customer_id=r.customer_id,u("tolt_data",window.tolt_data,30)),r}catch(t){console.error(`API call to ${e} failed:`,t)}else console.warn("Public ID (data-tolt) or domain is missing. API calls will not be made.")}async function w(t,e){switch(t){case"signup":case"conversion":case"clicks":return p(t,e);default:console.warn("Unknown command:",t,e)}}(async()=>{var t,e;t=document.querySelector("script[data-tolt]"),(c=t?t.getAttribute("data-tolt"):null)?(t=async()=>{var t,e;d=(l=window.location.hostname).endsWith(".local")?2<(o=l.split(".")).length?o.slice(-2).join("."):l:"undefined"!=typeof psl&&psl.parse(l).domain||l,f("tolt_referral");if(f("tolt_referral")||f("tolt_data"))window.tolt_referral=f("tolt_referral"),window.tolt_data=f("tolt_data");else{var r,o,n=new URLSearchParams(window.location.search);let t={};for(r of["tolt","ref","aff","via","lmref","fpr","tap_s","afmc","f2f-ref","join"])if(n.has(r)){var a=window.location.href;t={public_id:c,param:r,code:n.get(r),page:a,referrer:document?.referrer};break}0!==Object.keys(t).length&&(o=await p("clicks",t))&&o.click_id&&(l=o.cookie_duration||30,console.log("Referral code:",o.referral),u("tolt_referral",o.referral,l),window.tolt_referral=o.referral,window.tolt_data={click_id:o.click_id||null,cookie_duration:l||null,partner_id:o.partner_id||null,customer_id:o.customer_id||null,program_id:o.program_id||null},u("tolt_data",window.tolt_data,l))}await 0;for({command:t,data:e}of window.tolt.queue||[])await w(t,e);window.tolt.queue=[],await 0,window.tolt=async function(t,e){return w(t,e)},window.tolt.signup=async function(t){return w("signup",{email:t,public_id:c})};var i,l=document.querySelector("script[data-tolt]");l&&l.hasAttribute("data-stripe-type")&&(i=l.getAttribute("data-stripe-type"),l=l.getAttribute("data-stripe-domain"),s(i,l)),document.dispatchEvent(new Event("toltScriptLoaded"))},(e=document.createElement("script")).src="https://cdn.jsdelivr.net/npm/psl/dist/psl.min.js",e.onload=t,e.onerror=()=>console.error("Failed to load the PSL script"),document.head.appendChild(e)):console.warn("Public ID (data-tolt) is missing. Script initialization aborted.")})()})();