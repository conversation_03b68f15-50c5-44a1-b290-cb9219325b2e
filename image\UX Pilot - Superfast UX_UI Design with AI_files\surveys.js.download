!function(){"use strict";function e(){return e=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var i in t)({}).hasOwnProperty.call(t,i)&&(e[i]=t[i])}return e},e.apply(null,arguments)}var r,t,i,n,o,a,u,s,l={},c=[],p=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,d=Array.isArray;function v(e,r){for(var t in r)e[t]=r[t];return e}function f(e){var r=e.parentNode;r&&r.removeChild(e)}function h(e,t,i){var n,o,a,u={};for(a in t)"key"==a?n=t[a]:"ref"==a?o=t[a]:u[a]=t[a];if(arguments.length>2&&(u.children=arguments.length>3?r.call(arguments,2):i),"function"==typeof e&&null!=e.defaultProps)for(a in e.defaultProps)void 0===u[a]&&(u[a]=e.defaultProps[a]);return y(e,u,n,o,null)}function y(e,r,n,o,a){var u={type:e,props:r,key:n,ref:o,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:null==a?++i:a,__i:-1,__u:0};return null==a&&null!=t.vnode&&t.vnode(u),u}function b(e){return e.children}function m(e,r){this.props=e,this.context=r}function g(e,r){if(null==r)return e.__?g(e.__,e.__i+1):null;for(var t;r<e.__k.length;r++)if(null!=(t=e.__k[r])&&null!=t.__e)return t.__e;return"function"==typeof e.type?g(e):null}function x(e){var r,t;if(null!=(e=e.__)&&null!=e.__c){for(e.__e=e.__c.base=null,r=0;r<e.__k.length;r++)if(null!=(t=e.__k[r])&&null!=t.__e){e.__e=e.__c.base=t.__e;break}return x(e)}}function w(e){(!e.__d&&(e.__d=!0)&&n.push(e)&&!k.__r++||o!==t.debounceRendering)&&((o=t.debounceRendering)||a)(k)}function k(){var e,r,i,o,a,s,l,c,p;for(n.sort(u);e=n.shift();)e.__d&&(r=n.length,o=void 0,s=(a=(i=e).__v).__e,c=[],p=[],(l=i.__P)&&((o=v({},a)).__v=a.__v+1,t.vnode&&t.vnode(o),H(l,o,a,i.__n,void 0!==l.ownerSVGElement,32&a.__u?[s]:null,c,null==s?g(a):s,!!(32&a.__u),p),o.__.__k[o.__i]=o,I(c,o,p),o.__e!=s&&x(o)),n.length>r&&n.sort(u));k.__r=0}function C(e,r,t,i,n,o,a,u,s,p,v){var f,h,m,x,w,k=i&&i.__k||c,C=r.length;for(t.__d=s,function(e,r,t){var i,n,o,a,u,s=r.length,l=t.length,c=l,p=0;for(e.__k=[],i=0;i<s;i++)null!=(n=e.__k[i]=null==(n=r[i])||"boolean"==typeof n||"function"==typeof n?null:"string"==typeof n||"number"==typeof n||"bigint"==typeof n||n.constructor==String?y(null,n,null,null,n):d(n)?y(b,{children:n},null,null,null):void 0===n.constructor&&n.__b>0?y(n.type,n.props,n.key,n.ref?n.ref:null,n.__v):n)?(n.__=e,n.__b=e.__b+1,u=q(n,t,a=i+p,c),n.__i=u,o=null,-1!==u&&(c--,(o=t[u])&&(o.__u|=131072)),null==o||null===o.__v?(-1==u&&p--,"function"!=typeof n.type&&(n.__u|=65536)):u!==a&&(u===a+1?p++:u>a?c>s-a?p+=u-a:p--:p=u<a&&u==a-1?u-a:0,u!==i+p&&(n.__u|=65536))):(o=t[i])&&null==o.key&&o.__e&&(o.__e==e.__d&&(e.__d=g(o)),L(o,o,!1),t[i]=null,c--);if(c)for(i=0;i<l;i++)null!=(o=t[i])&&0==(131072&o.__u)&&(o.__e==e.__d&&(e.__d=g(o)),L(o,o))}(t,r,k),s=t.__d,f=0;f<C;f++)null!=(m=t.__k[f])&&"boolean"!=typeof m&&"function"!=typeof m&&(h=-1===m.__i?l:k[m.__i]||l,m.__i=f,H(e,m,h,n,o,a,u,s,p,v),x=m.__e,m.ref&&h.ref!=m.ref&&(h.ref&&Z(h.ref,null,m),v.push(m.ref,m.__c||x,m)),null==w&&null!=x&&(w=x),65536&m.__u||h.__k===m.__k?s=S(m,s,e):"function"==typeof m.type&&void 0!==m.__d?s=m.__d:x&&(s=x.nextSibling),m.__d=void 0,m.__u&=-196609);t.__d=s,t.__e=w}function S(e,r,t){var i,n;if("function"==typeof e.type){for(i=e.__k,n=0;i&&n<i.length;n++)i[n]&&(i[n].__=e,r=S(i[n],r,t));return r}return e.__e!=r&&(t.insertBefore(e.__e,r||null),r=e.__e),r&&r.nextSibling}function q(e,r,t,i){var n=e.key,o=e.type,a=t-1,u=t+1,s=r[t];if(null===s||s&&n==s.key&&o===s.type)return t;if(i>(null!=s&&0==(131072&s.__u)?1:0))for(;a>=0||u<r.length;){if(a>=0){if((s=r[a])&&0==(131072&s.__u)&&n==s.key&&o===s.type)return a;a--}if(u<r.length){if((s=r[u])&&0==(131072&s.__u)&&n==s.key&&o===s.type)return u;u++}}return-1}function _(e,r,t){"-"===r[0]?e.setProperty(r,null==t?"":t):e[r]=null==t?"":"number"!=typeof t||p.test(r)?t:t+"px"}function T(e,r,t,i,n){var o;e:if("style"===r)if("string"==typeof t)e.style.cssText=t;else{if("string"==typeof i&&(e.style.cssText=i=""),i)for(r in i)t&&r in t||_(e.style,r,"");if(t)for(r in t)i&&t[r]===i[r]||_(e.style,r,t[r])}else if("o"===r[0]&&"n"===r[1])o=r!==(r=r.replace(/(PointerCapture)$|Capture$/,"$1")),r=r.toLowerCase()in e?r.toLowerCase().slice(2):r.slice(2),e.l||(e.l={}),e.l[r+o]=t,t?i?t.u=i.u:(t.u=Date.now(),e.addEventListener(r,o?P:M,o)):e.removeEventListener(r,o?P:M,o);else{if(n)r=r.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!==r&&"height"!==r&&"href"!==r&&"list"!==r&&"form"!==r&&"tabIndex"!==r&&"download"!==r&&"rowSpan"!==r&&"colSpan"!==r&&"role"!==r&&r in e)try{e[r]=null==t?"":t;break e}catch(e){}"function"==typeof t||(null==t||!1===t&&"-"!==r[4]?e.removeAttribute(r):e.setAttribute(r,t))}}function M(e){var r=this.l[e.type+!1];if(e.t){if(e.t<=r.u)return}else e.t=Date.now();return r(t.event?t.event(e):e)}function P(e){return this.l[e.type+!0](t.event?t.event(e):e)}function H(e,r,i,n,o,a,u,s,l,c){var p,f,h,y,g,x,w,k,S,q,_,T,M,P,H,I=r.type;if(void 0!==r.constructor)return null;128&i.__u&&(l=!!(32&i.__u),a=[s=r.__e=i.__e]),(p=t.__b)&&p(r);e:if("function"==typeof I)try{if(k=r.props,S=(p=I.contextType)&&n[p.__c],q=p?S?S.props.value:p.__:n,i.__c?w=(f=r.__c=i.__c).__=f.__E:("prototype"in I&&I.prototype.render?r.__c=f=new I(k,q):(r.__c=f=new m(k,q),f.constructor=I,f.render=N),S&&S.sub(f),f.props=k,f.state||(f.state={}),f.context=q,f.__n=n,h=f.__d=!0,f.__h=[],f._sb=[]),null==f.__s&&(f.__s=f.state),null!=I.getDerivedStateFromProps&&(f.__s==f.state&&(f.__s=v({},f.__s)),v(f.__s,I.getDerivedStateFromProps(k,f.__s))),y=f.props,g=f.state,f.__v=r,h)null==I.getDerivedStateFromProps&&null!=f.componentWillMount&&f.componentWillMount(),null!=f.componentDidMount&&f.__h.push(f.componentDidMount);else{if(null==I.getDerivedStateFromProps&&k!==y&&null!=f.componentWillReceiveProps&&f.componentWillReceiveProps(k,q),!f.__e&&(null!=f.shouldComponentUpdate&&!1===f.shouldComponentUpdate(k,f.__s,q)||r.__v===i.__v)){for(r.__v!==i.__v&&(f.props=k,f.state=f.__s,f.__d=!1),r.__e=i.__e,r.__k=i.__k,r.__k.forEach((function(e){e&&(e.__=r)})),_=0;_<f._sb.length;_++)f.__h.push(f._sb[_]);f._sb=[],f.__h.length&&u.push(f);break e}null!=f.componentWillUpdate&&f.componentWillUpdate(k,f.__s,q),null!=f.componentDidUpdate&&f.__h.push((function(){f.componentDidUpdate(y,g,x)}))}if(f.context=q,f.props=k,f.__P=e,f.__e=!1,T=t.__r,M=0,"prototype"in I&&I.prototype.render){for(f.state=f.__s,f.__d=!1,T&&T(r),p=f.render(f.props,f.state,f.context),P=0;P<f._sb.length;P++)f.__h.push(f._sb[P]);f._sb=[]}else do{f.__d=!1,T&&T(r),p=f.render(f.props,f.state,f.context),f.state=f.__s}while(f.__d&&++M<25);f.state=f.__s,null!=f.getChildContext&&(n=v(v({},n),f.getChildContext())),h||null==f.getSnapshotBeforeUpdate||(x=f.getSnapshotBeforeUpdate(y,g)),C(e,d(H=null!=p&&p.type===b&&null==p.key?p.props.children:p)?H:[H],r,i,n,o,a,u,s,l,c),f.base=r.__e,r.__u&=-161,f.__h.length&&u.push(f),w&&(f.__E=f.__=null)}catch(e){r.__v=null,l||null!=a?(r.__e=s,r.__u|=l?160:32,a[a.indexOf(s)]=null):(r.__e=i.__e,r.__k=i.__k),t.__e(e,r,i)}else null==a&&r.__v===i.__v?(r.__k=i.__k,r.__e=i.__e):r.__e=V(i.__e,r,i,n,o,a,u,l,c);(p=t.diffed)&&p(r)}function I(e,r,i){r.__d=void 0;for(var n=0;n<i.length;n++)Z(i[n],i[++n],i[++n]);t.__c&&t.__c(r,e),e.some((function(r){try{e=r.__h,r.__h=[],e.some((function(e){e.call(r)}))}catch(e){t.__e(e,r.__v)}}))}function V(e,t,i,n,o,a,u,s,c){var p,v,h,y,b,m,x,w=i.props,k=t.props,S=t.type;if("svg"===S&&(o=!0),null!=a)for(p=0;p<a.length;p++)if((b=a[p])&&"setAttribute"in b==!!S&&(S?b.localName===S:3===b.nodeType)){e=b,a[p]=null;break}if(null==e){if(null===S)return document.createTextNode(k);e=o?document.createElementNS("http://www.w3.org/2000/svg",S):document.createElement(S,k.is&&k),a=null,s=!1}if(null===S)w===k||s&&e.data===k||(e.data=k);else{if(a=a&&r.call(e.childNodes),w=i.props||l,!s&&null!=a)for(w={},p=0;p<e.attributes.length;p++)w[(b=e.attributes[p]).name]=b.value;for(p in w)b=w[p],"children"==p||("dangerouslySetInnerHTML"==p?h=b:"key"===p||p in k||T(e,p,null,b,o));for(p in k)b=k[p],"children"==p?y=b:"dangerouslySetInnerHTML"==p?v=b:"value"==p?m=b:"checked"==p?x=b:"key"===p||s&&"function"!=typeof b||w[p]===b||T(e,p,b,w[p],o);if(v)s||h&&(v.__html===h.__html||v.__html===e.innerHTML)||(e.innerHTML=v.__html),t.__k=[];else if(h&&(e.innerHTML=""),C(e,d(y)?y:[y],t,i,n,o&&"foreignObject"!==S,a,u,a?a[0]:i.__k&&g(i,0),s,c),null!=a)for(p=a.length;p--;)null!=a[p]&&f(a[p]);s||(p="value",void 0!==m&&(m!==e[p]||"progress"===S&&!m||"option"===S&&m!==w[p])&&T(e,p,m,w[p],!1),p="checked",void 0!==x&&x!==e[p]&&T(e,p,x,w[p],!1))}return e}function Z(e,r,i){try{"function"==typeof e?e(r):e.current=r}catch(e){t.__e(e,i)}}function L(e,r,i){var n,o;if(t.unmount&&t.unmount(e),(n=e.ref)&&(n.current&&n.current!==e.__e||Z(n,null,r)),null!=(n=e.__c)){if(n.componentWillUnmount)try{n.componentWillUnmount()}catch(e){t.__e(e,r)}n.base=n.__P=null,e.__c=void 0}if(n=e.__k)for(o=0;o<n.length;o++)n[o]&&L(n[o],r,i||"function"!=typeof e.type);i||null==e.__e||f(e.__e),e.__=e.__e=e.__d=void 0}function N(e,r,t){return this.constructor(e,t)}function D(e,i,n){var o,a,u,s;t.__&&t.__(e,i),a=(o="function"==typeof n)?null:i.__k,u=[],s=[],H(i,e=(!o&&n||i).__k=h(b,null,[e]),a||l,l,void 0!==i.ownerSVGElement,!o&&n?[n]:a?null:i.firstChild?r.call(i.childNodes):null,u,!o&&n?n:a?a.__e:i.firstChild,o,s),I(u,e,s)}function E(e,t,i){var n,o,a,u,s=v({},e.props);for(a in e.type&&e.type.defaultProps&&(u=e.type.defaultProps),t)"key"==a?n=t[a]:"ref"==a?o=t[a]:s[a]=void 0===t[a]&&void 0!==u?u[a]:t[a];return arguments.length>2&&(s.children=arguments.length>3?r.call(arguments,2):i),y(e.type,s,n||e.key,o||e.ref,null)}r=c.slice,t={__e:function(e,r,t,i){for(var n,o,a;r=r.__;)if((n=r.__c)&&!n.__)try{if((o=n.constructor)&&null!=o.getDerivedStateFromError&&(n.setState(o.getDerivedStateFromError(e)),a=n.__d),null!=n.componentDidCatch&&(n.componentDidCatch(e,i||{}),a=n.__d),a)return n.__E=n}catch(r){e=r}throw e}},i=0,m.prototype.setState=function(e,r){var t;t=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=v({},this.state),"function"==typeof e&&(e=e(v({},t),this.props)),e&&v(t,e),null!=e&&this.__v&&(r&&this._sb.push(r),w(this))},m.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),w(this))},m.prototype.render=b,n=[],a="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,u=function(e,r){return e.__v.__b-r.__v.__b},k.__r=0,s=0;var F,A,Q,B,j=0,z=[],R=[],U=t.__b,O=t.__r,$=t.diffed,W=t.__c,K=t.unmount;function Y(e,r){t.__h&&t.__h(A,e,j||r),j=0;var i=A.__H||(A.__H={__:[],__h:[]});return e>=i.__.length&&i.__.push({__V:R}),i.__[e]}function G(e){return j=1,function(e,r,t){var i=Y(F++,2);if(i.t=e,!i.__c&&(i.__=[se(void 0,r),function(e){var r=i.__N?i.__N[0]:i.__[0],t=i.t(r,e);r!==t&&(i.__N=[t,i.__[1]],i.__c.setState({}))}],i.__c=A,!A.u)){var n=function(e,r,t){if(!i.__c.__H)return!0;var n=i.__c.__H.__.filter((function(e){return e.__c}));if(n.every((function(e){return!e.__N})))return!o||o.call(this,e,r,t);var a=!1;return n.forEach((function(e){if(e.__N){var r=e.__[0];e.__=e.__N,e.__N=void 0,r!==e.__[0]&&(a=!0)}})),!(!a&&i.__c.props===e)&&(!o||o.call(this,e,r,t))};A.u=!0;var o=A.shouldComponentUpdate,a=A.componentWillUpdate;A.componentWillUpdate=function(e,r,t){if(this.__e){var i=o;o=void 0,n(e,r,t),o=i}a&&a.call(this,e,r,t)},A.shouldComponentUpdate=n}return i.__N||i.__}(se,e)}function J(e,r){var i=Y(F++,3);!t.__s&&ue(i.__H,r)&&(i.__=e,i.i=r,A.__H.__h.push(i))}function X(e){return j=5,ee((function(){return{current:e}}),[])}function ee(e,r){var t=Y(F++,7);return ue(t.__H,r)?(t.__V=e(),t.i=r,t.__h=e,t.__V):t.__}function re(e){var r=A.context[e.__c],t=Y(F++,9);return t.c=e,r?(null==t.__&&(t.__=!0,r.sub(A)),r.props.value):e.__}function te(){for(var e;e=z.shift();)if(e.__P&&e.__H)try{e.__H.__h.forEach(oe),e.__H.__h.forEach(ae),e.__H.__h=[]}catch(r){e.__H.__h=[],t.__e(r,e.__v)}}t.__b=function(e){A=null,U&&U(e)},t.__r=function(e){O&&O(e),F=0;var r=(A=e.__c).__H;r&&(Q===A?(r.__h=[],A.__h=[],r.__.forEach((function(e){e.__N&&(e.__=e.__N),e.__V=R,e.__N=e.i=void 0}))):(r.__h.forEach(oe),r.__h.forEach(ae),r.__h=[],F=0)),Q=A},t.diffed=function(e){$&&$(e);var r=e.__c;r&&r.__H&&(r.__H.__h.length&&(1!==z.push(r)&&B===t.requestAnimationFrame||((B=t.requestAnimationFrame)||ne)(te)),r.__H.__.forEach((function(e){e.i&&(e.__H=e.i),e.__V!==R&&(e.__=e.__V),e.i=void 0,e.__V=R}))),Q=A=null},t.__c=function(e,r){r.some((function(e){try{e.__h.forEach(oe),e.__h=e.__h.filter((function(e){return!e.__||ae(e)}))}catch(i){r.some((function(e){e.__h&&(e.__h=[])})),r=[],t.__e(i,e.__v)}})),W&&W(e,r)},t.unmount=function(e){K&&K(e);var r,i=e.__c;i&&i.__H&&(i.__H.__.forEach((function(e){try{oe(e)}catch(e){r=e}})),i.__H=void 0,r&&t.__e(r,i.__v))};var ie="function"==typeof requestAnimationFrame;function ne(e){var r,t=function(){clearTimeout(i),ie&&cancelAnimationFrame(r),setTimeout(e)},i=setTimeout(t,100);ie&&(r=requestAnimationFrame(t))}function oe(e){var r=A,t=e.__c;"function"==typeof t&&(e.__c=void 0,t()),A=r}function ae(e){var r=A;e.__c=e.__(),A=r}function ue(e,r){return!e||e.length!==r.length||r.some((function(r,t){return r!==e[t]}))}function se(e,r){return"function"==typeof r?r(e):r}var le=function(e){return e.Button="button",e.Tab="tab",e.Selector="selector",e}({}),ce=function(e){return e.TopLeft="top_left",e.TopRight="top_right",e.TopCenter="top_center",e.MiddleLeft="middle_left",e.MiddleRight="middle_right",e.MiddleCenter="middle_center",e.Left="left",e.Center="center",e.Right="right",e.NextToTrigger="next_to_trigger",e}({}),pe=function(e){return e.Popover="popover",e.API="api",e.Widget="widget",e.ExternalSurvey="external_survey",e}({}),de=function(e){return e.Open="open",e.MultipleChoice="multiple_choice",e.SingleChoice="single_choice",e.Rating="rating",e.Link="link",e}({}),ve=function(e){return e.NextQuestion="next_question",e.End="end",e.ResponseBased="response_based",e.SpecificQuestion="specific_question",e}({}),fe=function(e){return e.Once="once",e.Recurring="recurring",e.Always="always",e}({}),he=function(e){return e.SHOWN="survey shown",e.DISMISSED="survey dismissed",e.SENT="survey sent",e}({}),ye=function(e){return e.SURVEY_ID="$survey_id",e.SURVEY_NAME="$survey_name",e.SURVEY_RESPONSE="$survey_response",e.SURVEY_ITERATION="$survey_iteration",e.SURVEY_ITERATION_START_DATE="$survey_iteration_start_date",e.SURVEY_PARTIALLY_COMPLETED="$survey_partially_completed",e.SURVEY_SUBMISSION_ID="$survey_submission_id",e.SURVEY_QUESTIONS="$survey_questions",e.SURVEY_COMPLETED="$survey_completed",e}({}),be="undefined"!=typeof window?window:void 0,me="undefined"!=typeof globalThis?globalThis:be,ge=null==me?void 0:me.navigator,xe=null==me?void 0:me.document;null==me||me.location,null==me||me.fetch,null!=me&&me.XMLHttpRequest&&"withCredentials"in new me.XMLHttpRequest&&me.XMLHttpRequest,null==me||me.AbortController;var we=null==ge?void 0:ge.userAgent,ke=null!=be?be:{},Ce=Array.isArray,Se=Object.prototype.toString,qe=Ce||function(e){return"[object Array]"===Se.call(e)},_e=e=>void 0===e,Te=e=>"[object String]"==Se.call(e),Me=e=>null===e,Pe=e=>_e(e)||Me(e),He=e=>"[object Number]"==Se.call(e),Ie=e=>{var r={o:function(r){if(be&&ke.POSTHOG_DEBUG&&!_e(be.console)&&be.console){for(var t=("__rrweb_original__"in be.console[r]?be.console[r].__rrweb_original__:be.console[r]),i=arguments.length,n=new Array(i>1?i-1:0),o=1;o<i;o++)n[o-1]=arguments[o];t(e,...n)}},info:function(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];r.o("log",...t)},warn:function(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];r.o("warn",...t)},error:function(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];r.o("error",...t)},critical:function(){for(var r=arguments.length,t=new Array(r),i=0;i<r;i++)t[i]=arguments[i];console.error(e,...t)},uninitializedWarning:e=>{r.error("You must initialize PostHog before calling "+e)},createLogger:r=>Ie(e+" "+r)};return r},Ve=Ie("[PostHog.js]").createLogger;function Ze(e,r,t,i){var{capture:n=!1,passive:o=!0}={};null==e||e.addEventListener(r,t,{capture:n,passive:o})}var Le=Ve("[Surveys]");var Ne="seenSurvey_",De="inProgressSurvey_",Ee=[pe.Popover,pe.Widget,pe.API];Math.trunc||(Math.trunc=function(e){return e<0?Math.ceil(e):Math.floor(e)}),Number.isInteger||(Number.isInteger=function(e){return He(e)&&isFinite(e)&&Math.floor(e)===e});var Fe="0123456789abcdef";class Ae{constructor(e){if(this.bytes=e,16!==e.length)throw new TypeError("not 128-bit length")}static fromFieldsV7(e,r,t,i){if(!Number.isInteger(e)||!Number.isInteger(r)||!Number.isInteger(t)||!Number.isInteger(i)||e<0||r<0||t<0||i<0||e>0xffffffffffff||r>4095||t>1073741823||i>4294967295)throw new RangeError("invalid field value");var n=new Uint8Array(16);return n[0]=e/Math.pow(2,40),n[1]=e/Math.pow(2,32),n[2]=e/Math.pow(2,24),n[3]=e/Math.pow(2,16),n[4]=e/Math.pow(2,8),n[5]=e,n[6]=112|r>>>8,n[7]=r,n[8]=128|t>>>24,n[9]=t>>>16,n[10]=t>>>8,n[11]=t,n[12]=i>>>24,n[13]=i>>>16,n[14]=i>>>8,n[15]=i,new Ae(n)}toString(){for(var e="",r=0;r<this.bytes.length;r++)e=e+Fe.charAt(this.bytes[r]>>>4)+Fe.charAt(15&this.bytes[r]),3!==r&&5!==r&&7!==r&&9!==r||(e+="-");if(36!==e.length)throw new Error("Invalid UUIDv7 was generated");return e}clone(){return new Ae(this.bytes.slice(0))}equals(e){return 0===this.compareTo(e)}compareTo(e){for(var r=0;r<16;r++){var t=this.bytes[r]-e.bytes[r];if(0!==t)return Math.sign(t)}return 0}}class Qe{constructor(){this.p=0,this.v=0,this.h=new ze}generate(){var e=this.generateOrAbort();if(_e(e)){this.p=0;var r=this.generateOrAbort();if(_e(r))throw new Error("Could not generate UUID after timestamp reset");return r}return e}generateOrAbort(){var e=Date.now();if(e>this.p)this.p=e,this.m();else{if(!(e+1e4>this.p))return;this.v++,this.v>4398046511103&&(this.p++,this.m())}return Ae.fromFieldsV7(this.p,Math.trunc(this.v/Math.pow(2,30)),this.v&Math.pow(2,30)-1,this.h.nextUint32())}m(){this.v=1024*this.h.nextUint32()+(1023&this.h.nextUint32())}}var Be,je=e=>{if("undefined"!=typeof UUIDV7_DENY_WEAK_RNG&&UUIDV7_DENY_WEAK_RNG)throw new Error("no cryptographically strong RNG available");for(var r=0;r<e.length;r++)e[r]=65536*Math.trunc(65536*Math.random())+Math.trunc(65536*Math.random());return e};be&&!_e(be.crypto)&&crypto.getRandomValues&&(je=e=>crypto.getRandomValues(e));class ze{constructor(){this.k=new Uint32Array(8),this.C=1/0}nextUint32(){return this.C>=this.k.length&&(je(this.k),this.C=0),this.k[this.C++]}}var Re=()=>Ue().toString(),Ue=()=>(Be||(Be=new Qe)).generate(),Oe="Mobile",$e="Android",We="Tablet",Ke=$e+" "+We,Ye="iPad",Ge="Apple Watch",Je="BlackBerry",Xe="Nintendo",er="PlayStation",rr="Xbox",tr="Windows Phone",ir="Nokia",nr="Ouya",or="Generic",ar=or+" "+Oe.toLowerCase(),ur=or+" "+We.toLowerCase(),sr=new RegExp(rr,"i"),lr=new RegExp(er+" \\w+","i"),cr=new RegExp(Xe+" \\w+","i"),pr=new RegExp(Je+"|PlayBook|BB10","i"),dr=function(e){var r=function(e){return cr.test(e)?Xe:lr.test(e)?er:sr.test(e)?rr:new RegExp(nr,"i").test(e)?nr:new RegExp("("+tr+"|WPDesktop)","i").test(e)?tr:/iPad/.test(e)?Ye:/iPod/.test(e)?"iPod Touch":/iPhone/.test(e)?"iPhone":/(watch)(?: ?os[,/]|\d,\d\/)[\d.]+/i.test(e)?Ge:pr.test(e)?Je:/(kobo)\s(ereader|touch)/i.test(e)?"Kobo":new RegExp(ir,"i").test(e)?ir:/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i.test(e)||/(kf[a-z]+)( bui|\)).+silk\//i.test(e)?"Kindle Fire":/(Android|ZTE)/i.test(e)?!new RegExp(Oe).test(e)||/(9138B|TB782B|Nexus [97]|pixel c|HUAWEISHT|BTV|noble nook|smart ultra 6)/i.test(e)?/pixel[\daxl ]{1,6}/i.test(e)&&!/pixel c/i.test(e)||/(huaweimed-al00|tah-|APA|SM-G92|i980|zte|U304AA)/i.test(e)||/lmy47v/i.test(e)&&!/QTAQZ3/i.test(e)?$e:Ke:$e:new RegExp("(pda|"+Oe+")","i").test(e)?ar:new RegExp(We,"i").test(e)&&!new RegExp(We+" pc","i").test(e)?ur:""}(e);return r===Ye||r===Ke||"Kobo"===r||"Kindle Fire"===r||r===ur?We:r===Xe||r===rr||r===er||r===nr?"Console":r===Ge?"Wearable":r?Oe:"Desktop"};null!=ge&&ge.sendBeacon;var vr=function(e,r){if(!function(e){try{new RegExp(e)}catch(e){return!1}return!0}(r))return!1;try{return new RegExp(r).test(e)}catch(e){return!1}},fr={exact:(e,r)=>r.some((r=>e.some((e=>r===e)))),is_not:(e,r)=>r.every((r=>e.every((e=>r!==e)))),regex:(e,r)=>r.some((r=>e.some((e=>vr(r,e))))),not_regex:(e,r)=>r.every((r=>e.every((e=>!vr(r,e))))),icontains:(e,r)=>r.map(hr).some((r=>e.map(hr).some((e=>r.includes(e))))),not_icontains:(e,r)=>r.map(hr).every((r=>e.map(hr).every((e=>!r.includes(e)))))},hr=e=>e.toLowerCase(),yr=Ve("[Stylesheet Loader]"),br=be,mr=xe;function gr(e){return"$survey_response_"+e}var xr="#020617",wr={fontFamily:"inherit",backgroundColor:"#eeeded",submitButtonColor:"black",submitButtonTextColor:"white",ratingButtonColor:"white",ratingButtonActiveColor:"black",borderColor:"#c9c6c6",placeholder:"Start typing...",whiteLabel:!1,displayThankYouMessage:!0,thankYouMessageHeader:"Thank you for your feedback!",position:ce.Right,widgetType:le.Tab,widgetLabel:"Feedback",widgetColor:"black",zIndex:"2147483647",disabledButtonOpacity:"0.6",maxWidth:"300px",textSubtleColor:"#939393",boxPadding:"20px 24px",boxShadow:"0 4px 12px rgba(0, 0, 0, 0.15)",borderRadius:"10px",shuffleQuestions:!1,surveyPopupDelaySeconds:void 0,outlineColor:"rgba(59, 130, 246, 0.8)",inputBackground:"white",inputTextColor:xr,scrollbarThumbColor:"var(--ph-survey-border-color)",scrollbarTrackColor:"var(--ph-survey-background-color)"};function kr(e){if("#"===e[0]){var r=e.replace(/^#/,"");return"rgb("+parseInt(r.slice(0,2),16)+","+parseInt(r.slice(2,4),16)+","+parseInt(r.slice(4,6),16)+")"}return"rgb(255, 255, 255)"}function Cr(e){var r;void 0===e&&(e=wr.backgroundColor),"#"===e[0]&&(r=kr(e)),e.startsWith("rgb")&&(r=e);var t={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",green:"#008000",greenyellow:"#adff2f",honeydew:"#f0fff0",hotpink:"#ff69b4","indianred ":"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgrey:"#d3d3d3",lightgreen:"#90ee90",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370d8",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#d87093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"}[e.toLowerCase()];if(t&&(r=kr(t)),!r)return xr;var i=r.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d+(?:\.\d+)?))?\)$/);if(i){var n=parseInt(i[1]),o=parseInt(i[2]),a=parseInt(i[3]);return Math.sqrt(n*n*.299+o*o*.587+a*a*.114)>127.5?xr:"white"}return xr}function Sr(e){var r=((e,r,t)=>{var i,n=e.createElement("style");return n.innerText=r,null!=t&&null!=(i=t.config)&&i.prepare_external_dependency_stylesheet&&(n=t.config.prepare_external_dependency_stylesheet(n)),n||(yr.error("prepare_external_dependency_stylesheet returned null"),null)})(mr,':host{--ph-survey-font-family:-apple-system,BlinkMacSystemFont,"Inter","Segoe UI","Roboto",Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol";--ph-survey-box-padding:20px 24px;--ph-survey-max-width:300px;--ph-survey-z-index:2147483647;--ph-survey-border-color:#dcdcdc;--ph-survey-border-bottom:1.5px solid var(--ph-survey-border-color);--ph-survey-border-radius:10px;--ph-survey-background-color:#eeeded;--ph-survey-box-shadow:0 4px 12px rgba(0,0,0,.15);--ph-survey-submit-button-color:#000;--ph-survey-submit-button-text-color:#fff;--ph-survey-rating-bg-color:#fff;--ph-survey-rating-text-color:#020617;--ph-survey-rating-active-bg-color:#000;--ph-survey-rating-active-text-color:#fff;--ph-survey-text-primary-color:#020617;--ph-survey-text-subtle-color:#939393;--ph-widget-color:#e0a045;--ph-widget-text-color:#fff;--ph-survey-scrollbar-thumb-color:var(--ph-survey-border-color);--ph-survey-scrollbar-track-color:var(--ph-survey-background-color);--ph-survey-outline-color:rgba(59,130,246,.8);--ph-survey-input-background:#fff;--ph-survey-input-text-color:#020617;--ph-survey-disabled-button-opacity:0.6}.ph-survey{bottom:0;height:fit-content;margin:0;max-width:85%;min-width:300px;position:fixed;width:var(--ph-survey-max-width);z-index:var(--ph-survey-z-index)}.ph-survey h3,.ph-survey p{margin:0}.ph-survey *{box-sizing:border-box;color:var(--ph-survey-text-primary-color);font-family:var(--ph-survey-font-family)}.ph-survey .multiple-choice-options label,.ph-survey input[type=text],.ph-survey textarea{background:var(--ph-survey-input-background);border:1.5px solid var(--ph-survey-border-color);border-radius:4px;color:var(--ph-survey-input-text-color);padding:10px;transition:border-color .2s ease-out,box-shadow .2s ease-out,transform .15s ease-out}.ph-survey input[type=text],.ph-survey textarea{transition:border-color .2s ease-out,box-shadow .2s ease-out,transform .15s ease-out}.ph-survey input{margin:0}.ph-survey .form-submit:focus,.ph-survey .form-submit:focus-visible,.ph-survey input[type=checkbox]:focus,.ph-survey input[type=checkbox]:focus-visible,.ph-survey input[type=radio]:focus,.ph-survey input[type=radio]:focus-visible,.ph-survey input[type=text]:focus,.ph-survey input[type=text]:focus-visible,.ph-survey textarea:focus,.ph-survey textarea:focus-visible{border-color:var(--ph-survey-rating-active-bg-color);outline:1.5px solid var(--ph-survey-outline-color);outline-offset:2px}.ph-survey button:focus:not(:focus-visible),.ph-survey input[type=checkbox]:focus:not(:focus-visible),.ph-survey input[type=radio]:focus:not(:focus-visible),.ph-survey input[type=text]:focus:not(:focus-visible),.ph-survey textarea:focus:not(:focus-visible){outline:none}.ph-survey input[type=text]:hover:not(:focus),.ph-survey textarea:hover:not(:focus){border-color:var(--ph-survey-rating-active-bg-color)}@media (max-width:768px){.ph-survey input[type=text],.ph-survey textarea{font-size:1rem}}.ph-survey .form-cancel,.ph-survey .multiple-choice-options label,.ph-survey .rating-options-number,.ph-survey input[type=checkbox],.ph-survey input[type=radio]{border:1.5px solid var(--ph-survey-border-color)}.ph-survey .footer-branding,.ph-survey .form-cancel,.ph-survey .form-submit,.ph-survey .ratings-emoji,.ph-survey .ratings-number,.ph-survey input[type=checkbox],.ph-survey input[type=radio],.ph-survey label{transition:all .2s ease-out}@media (prefers-reduced-motion:no-preference){.ph-survey button:active,.ph-survey input[type=checkbox]:active,.ph-survey input[type=radio]:active,.ph-survey label:active{transition-duration:.1s}}.ph-survey-widget-tab{background:var(--ph-widget-color);border:none;border-radius:3px 3px 0 0;color:var(--ph-widget-text-color);cursor:pointer;padding:10px 12px;position:fixed;right:0;text-align:center;top:50%;transform:rotate(-90deg) translateY(-100%);transform-origin:right top;transition:padding-bottom .2s ease-out;z-index:var(--ph-survey-z-index)}.ph-survey-widget-tab:hover{padding-bottom:16px}@keyframes ph-survey-fade-in{0%{opacity:0}to{opacity:1}}.survey-box{gap:16px}.bottom-section,.survey-box{display:flex;flex-direction:column}.bottom-section{gap:8px}.thank-you-message-header~.bottom-section{padding-top:16px}.question-container,.thank-you-message{display:flex;flex-direction:column;gap:8px}.survey-question{font-size:14px;font-weight:500}.survey-question-description{font-size:13px;opacity:.8;padding-top:4px}.question-textarea-wrapper{display:flex;flex-direction:column}.survey-form{animation:ph-survey-fade-in .3s ease-out forwards}.survey-form,.thank-you-message{background:var(--ph-survey-background-color);border:1.5px solid var(--ph-survey-border-color);border-bottom:var(--ph-survey-border-bottom);border-radius:var(--ph-survey-border-radius);box-shadow:var(--ph-survey-box-shadow);margin:0;padding:var(--ph-survey-box-padding);position:relative;text-align:left;width:100%;z-index:var(--ph-survey-z-index)}.survey-form input[type=text],.survey-form textarea{min-width:100%}:is(.survey-form textarea):focus,:is(.survey-form textarea):focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.08)}:is(.survey-form textarea):focus:not(:focus-visible){box-shadow:none}.survey-box:has(.survey-question:empty):not(:has(.survey-question-description)) .multiple-choice-options,.survey-box:has(.survey-question:empty):not(:has(.survey-question-description)) textarea{margin-top:0}.multiple-choice-options{border:none;display:flex;flex-direction:column;font-size:14px;gap:8px;margin:0;padding:1px 0}.multiple-choice-options .response-choice,.multiple-choice-options label{align-items:center;color:inherit;display:flex;gap:8px}.multiple-choice-options label{cursor:pointer;font-size:13px}:is(.multiple-choice-options label):hover:not(:has(input:checked)){border-color:var(--ph-survey-text-subtle-color);box-shadow:0 2px 8px rgba(0,0,0,.08)}:is(.multiple-choice-options label):has(input:checked){border-color:var(--ph-survey-rating-active-bg-color);box-shadow:0 1px 4px rgba(0,0,0,.05)}.choice-option-open:is(.multiple-choice-options label){flex-wrap:wrap}:is(.multiple-choice-options label) span{color:inherit}.multiple-choice-options input[type=checkbox],.multiple-choice-options input[type=radio]{appearance:none;-webkit-appearance:none;-moz-appearance:none;background:var(--ph-survey-input-background);border-radius:3px;cursor:pointer;flex-shrink:0;height:1rem;position:relative;transition:all .2s cubic-bezier(.4,0,.2,1),transform .15s ease-out;width:1rem;z-index:1}:is(.multiple-choice-options input[type=checkbox],.multiple-choice-options input[type=radio]):focus{transform:scale(1.05)}:is(.multiple-choice-options input[type=checkbox],.multiple-choice-options input[type=radio]):hover{border-color:var(--ph-survey-rating-active-bg-color);transform:scale(1.05)}:is(.multiple-choice-options input[type=checkbox],.multiple-choice-options input[type=radio]):active{transform:scale(.95)}:is(.multiple-choice-options input[type=checkbox],.multiple-choice-options input[type=radio]):checked{background:var(--ph-survey-rating-active-bg-color);border-color:var(--ph-survey-rating-active-bg-color);transform:scale(1)}:is(.multiple-choice-options input[type=checkbox],.multiple-choice-options input[type=radio]):checked:hover{transform:scale(1.05)}.multiple-choice-options input[type=checkbox]:checked:after{animation:ph-survey-checkmark-reveal .2s ease-out .1s forwards;border:solid var(--ph-survey-rating-active-text-color);border-width:0 2px 2px 0;height:8px;left:4px;transform:rotate(45deg) scale(0);width:4px}.multiple-choice-options input[type=radio]:checked:after{animation:ph-survey-radio-reveal .15s ease-out .05s forwards;background:var(--ph-survey-rating-active-text-color);border-radius:50%;height:6px;left:5px;top:5px;transform:scale(0);width:6px}.multiple-choice-options input[type=checkbox]:checked:after,.multiple-choice-options input[type=radio]:checked:after{box-sizing:content-box;content:"";position:absolute}.multiple-choice-options input[type=radio]{border-radius:50%}.multiple-choice-options input[type=radio]:checked{border:none}:is(.multiple-choice-options input[type=checkbox]:checked,.multiple-choice-options input[type=radio]:checked)~*{font-weight:700}:is(:is(.multiple-choice-options .choice-option-open) input[type=text])::placeholder{color:var(--ph-survey-text-subtle-color);font-weight:400}.rating-options-emoji{display:flex;justify-content:space-between}.ratings-emoji{background-color:transparent;border:none;font-size:16px;opacity:.5;padding:0}.ratings-emoji:hover{cursor:pointer;opacity:1;transform:scale(1.15)}.ratings-emoji.rating-active{opacity:1}.ratings-emoji svg{fill:var(--ph-survey-text-primary-color);transition:fill .2s ease-out}.rating-options-number{border-radius:6px;display:grid;grid-auto-columns:1fr;grid-auto-flow:column;overflow:hidden}.rating-options-number .ratings-number{border:none;border-right:1px solid var(--ph-survey-border-color);color:var(--ph-survey-rating-text-color);cursor:pointer;font-weight:700;text-align:center}:is(.rating-options-number .ratings-number):last-of-type{border-right:0}.rating-active:is(.rating-options-number .ratings-number){background:var(--ph-survey-rating-active-bg-color);color:var(--ph-survey-rating-active-text-color)}.ratings-number{background-color:var(--ph-survey-rating-bg-color);border:none;padding:8px 0}.ratings-number .rating-active{background-color:var(--ph-survey-rating-active-bg-color)}.ratings-number:hover{cursor:pointer}.rating-text{display:flex;flex-direction:row;font-size:11px;justify-content:space-between;opacity:.7}.form-submit{background:var(--ph-survey-submit-button-color);border:none;border-radius:6px;box-shadow:0 2px 0 rgba(0,0,0,.045);color:var(--ph-survey-submit-button-text-color);cursor:pointer;font-weight:700;min-width:100%;padding:12px;text-align:center;user-select:none}.form-submit:not([disabled]):hover{box-shadow:0 4px 8px rgba(0,0,0,.1);transform:scale(1.02)}.form-submit:not([disabled]):active{box-shadow:0 1px 2px rgba(0,0,0,.05);transform:scale(.98)}.form-submit[disabled]{cursor:not-allowed;opacity:var(--ph-survey-disabled-button-opacity)}.form-cancel{background:#fff;border-radius:100%;cursor:pointer;line-height:0;padding:12px;position:absolute;right:0;top:0;transform:translate(50%,-50%)}.form-cancel:hover{opacity:.7;transform:translate(50%,-50%) scale(1.1)}.footer-branding{align-items:center;display:flex;font-size:11px;font-weight:500;gap:4px;justify-content:center;opacity:.6;text-decoration:none}.footer-branding:hover{opacity:1}.footer-branding a{text-decoration:none}.thank-you-message{text-align:center}.thank-you-message-header{margin:10px 0 0}.thank-you-message-body{font-size:14px;opacity:.8}.limit-height{max-height:256px;overflow-x:hidden;overflow-y:auto;scrollbar-color:var(--ph-survey-scrollbar-thumb-color) var(--ph-survey-scrollbar-track-color);scrollbar-width:thin}.limit-height::-webkit-scrollbar{width:8px}.limit-height::-webkit-scrollbar-track{background:var(--ph-survey-scrollbar-track-color);border-radius:4px}.limit-height::-webkit-scrollbar-thumb{background-color:var(--ph-survey-scrollbar-thumb-color);border:2px solid var(--ph-survey-scrollbar-track-color);border-radius:4px}:is(.limit-height::-webkit-scrollbar-thumb):hover{background-color:var(--ph-survey-text-subtle-color)}.sr-only{clip:rect(0,0,0,0);border:0;height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;white-space:nowrap;width:1px}@media (prefers-reduced-motion:reduce){.ph-survey *{animation-duration:.01ms!important;animation-iteration-count:1!important;scroll-behavior:auto!important;transition-duration:.01ms!important}}@keyframes ph-survey-checkmark-reveal{0%{opacity:0;transform:rotate(45deg) scale(0)}50%{opacity:1;transform:rotate(45deg) scale(1.2)}to{opacity:1;transform:rotate(45deg) scale(1)}}@keyframes ph-survey-radio-reveal{0%{opacity:0;transform:scale(0)}50%{opacity:1;transform:scale(1.3)}to{opacity:1;transform:scale(1)}}',e);return null==r||r.setAttribute("data-ph-survey-style","true"),r}var qr=(r,t,i)=>{var n=Rr(r),o=mr.querySelector("."+n);if(o&&o.shadowRoot)return{shadow:o.shadowRoot,isNewlyCreated:!1};var a=mr.createElement("div");((r,t,i)=>{var n=e({},wr,i),o=r.style,a=![ce.Center,ce.Left,ce.Right].includes(n.position)||t===pe.Widget&&(null==i?void 0:i.widgetType)===le.Tab;o.setProperty("--ph-survey-font-family",function(e){if("inherit"===e)return"inherit";var r='BlinkMacSystemFont, "Inter", "Segoe UI", "Roboto", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"';return e?e+", "+r:"-apple-system, "+r}(n.fontFamily)),o.setProperty("--ph-survey-box-padding",n.boxPadding),o.setProperty("--ph-survey-max-width",n.maxWidth),o.setProperty("--ph-survey-z-index",n.zIndex),o.setProperty("--ph-survey-border-color",n.borderColor),a?(o.setProperty("--ph-survey-border-radius",n.borderRadius),o.setProperty("--ph-survey-border-bottom","1.5px solid var(--ph-survey-border-color)")):(o.setProperty("--ph-survey-border-bottom","none"),o.setProperty("--ph-survey-border-radius",n.borderRadius+" "+n.borderRadius+" 0 0")),o.setProperty("--ph-survey-background-color",n.backgroundColor),o.setProperty("--ph-survey-box-shadow",n.boxShadow),o.setProperty("--ph-survey-disabled-button-opacity",n.disabledButtonOpacity),o.setProperty("--ph-survey-submit-button-color",n.submitButtonColor),o.setProperty("--ph-survey-submit-button-text-color",(null==i?void 0:i.submitButtonTextColor)||Cr(n.submitButtonColor)),o.setProperty("--ph-survey-rating-bg-color",n.ratingButtonColor),o.setProperty("--ph-survey-rating-text-color",Cr(n.ratingButtonColor)),o.setProperty("--ph-survey-rating-active-bg-color",n.ratingButtonActiveColor),o.setProperty("--ph-survey-rating-active-text-color",Cr(n.ratingButtonActiveColor)),o.setProperty("--ph-survey-text-primary-color",Cr(n.backgroundColor)),o.setProperty("--ph-survey-text-subtle-color",n.textSubtleColor),o.setProperty("--ph-widget-color",n.widgetColor),o.setProperty("--ph-widget-text-color",Cr(n.widgetColor)),o.setProperty("--ph-widget-z-index",n.zIndex),"white"===n.backgroundColor&&o.setProperty("--ph-survey-input-background","#f8f8f8"),o.setProperty("--ph-survey-input-background",n.inputBackground),o.setProperty("--ph-survey-input-text-color",Cr(n.inputBackground)),o.setProperty("--ph-survey-scrollbar-thumb-color",n.scrollbarThumbColor),o.setProperty("--ph-survey-scrollbar-track-color",n.scrollbarTrackColor),o.setProperty("--ph-survey-outline-color",n.outlineColor)})(a,r.type,r.appearance),a.className=n;var u=a.attachShadow({mode:"open"}),s=Sr(t);if(s){var l=u.querySelector("style");l&&u.removeChild(l),u.appendChild(s)}return mr.body.appendChild(a),{shadow:u,isNewlyCreated:!0}},_r=(e,r)=>{if(!r)return null;var t=e[gr(r)];return qe(t)?[...t]:t},Tr=r=>{var{responses:t,survey:i,surveySubmissionId:n,posthog:o,isSurveyCompleted:a}=r;o?(o.capture(he.SENT,e({[ye.SURVEY_NAME]:i.name,[ye.SURVEY_ID]:i.id,[ye.SURVEY_ITERATION]:i.current_iteration,[ye.SURVEY_ITERATION_START_DATE]:i.current_iteration_start_date,[ye.SURVEY_QUESTIONS]:i.questions.map((e=>({id:e.id,question:e.question,response:_r(t,e.id)}))),[ye.SURVEY_SUBMISSION_ID]:n,[ye.SURVEY_COMPLETED]:a,sessionRecordingUrl:null==o.get_session_replay_url?void 0:o.get_session_replay_url()},t)),a&&(br.dispatchEvent(new CustomEvent("PHSurveySent",{detail:{surveyId:i.id}})),zr(i))):Le.error("[survey sent] event not captured, PostHog instance not found.")},Mr=(r,t,i)=>{if(t){if(!i){var n=Br(r);t.capture(he.DISMISSED,e({[ye.SURVEY_NAME]:r.name,[ye.SURVEY_ID]:r.id,[ye.SURVEY_ITERATION]:r.current_iteration,[ye.SURVEY_ITERATION_START_DATE]:r.current_iteration_start_date,[ye.SURVEY_PARTIALLY_COMPLETED]:Object.values((null==n?void 0:n.responses)||{}).filter((e=>!Pe(e))).length>0,sessionRecordingUrl:null==t.get_session_replay_url?void 0:t.get_session_replay_url()},null==n?void 0:n.responses,{[ye.SURVEY_SUBMISSION_ID]:null==n?void 0:n.surveySubmissionId,[ye.SURVEY_QUESTIONS]:r.questions.map((e=>({id:e.id,question:e.question,response:_r((null==n?void 0:n.responses)||{},e.id)})))})),zr(r),localStorage.setItem(Zr(r),"true"),br.dispatchEvent(new CustomEvent("PHSurveyClosed",{detail:{surveyId:r.id}}))}}else Le.error("[survey dismissed] event not captured, PostHog instance not found.")},Pr=e=>e.map((e=>({sort:Math.floor(10*Math.random()),value:e}))).sort(((e,r)=>e.sort-r.sort)).map((e=>e.value)),Hr=(e,r)=>e.length===r.length&&e.every(((e,t)=>e===r[t]))?r.reverse():r,Ir=e=>e.appearance&&e.appearance.shuffleQuestions&&!e.enable_partial_responses?Hr(e.questions,Pr(e.questions)):e.questions,Vr=e=>{var r;return!(null==(r=e.conditions)||null==(r=r.events)||!r.repeatedActivation||!(e=>{var r,t;return null!=(null==(r=e.conditions)||null==(r=r.events)||null==(r=r.values)?void 0:r.length)&&(null==(t=e.conditions)||null==(t=t.events)||null==(t=t.values)?void 0:t.length)>0})(e))||e.schedule===fe.Always||jr(e)},Zr=e=>{var r=""+Ne+e.id;return e.current_iteration&&e.current_iteration>0&&(r=""+Ne+e.id+"_"+e.current_iteration),r},Lr=function(e,r){var t={__c:r="__cC"+s++,__:e,Consumer:function(e,r){return e.children(r)},Provider:function(e){var t,i;return this.getChildContext||(t=[],(i={})[r]=this,this.getChildContext=function(){return i},this.shouldComponentUpdate=function(e){this.props.value!==e.value&&t.some((function(e){e.__e=!0,w(e)}))},this.sub=function(e){t.push(e);var r=e.componentWillUnmount;e.componentWillUnmount=function(){t.splice(t.indexOf(e),1),r&&r.call(e)}}),e.children}};return t.Provider.__=t.Consumer.contextType=t}({isPreviewMode:!1,previewPageIndex:0,onPopupSurveyDismissed:()=>{},isPopup:!0,onPreviewSubmit:()=>{},surveySubmissionId:""}),Nr=()=>re(Lr),Dr=e=>{var{component:r,children:t,renderAsHtml:i,style:n}=e;return E(r,i?{dangerouslySetInnerHTML:{__html:t},style:n}:{children:t,style:n})};function Er(e){return null!=e?e:"icontains"}function Fr(e){var r,t,i;if(null==(r=e.conditions)||!r.url)return!0;var n=null==br||null==(t=br.location)?void 0:t.href;if(!n)return!1;var o=[e.conditions.url],a=Er(null==(i=e.conditions)?void 0:i.urlMatchType);return fr[a](o,[n])}var Ar=e=>{var r=""+De+e.id;return e.current_iteration&&e.current_iteration>0&&(r=""+De+e.id+"_"+e.current_iteration),r},Qr=(e,r)=>{try{localStorage.setItem(Ar(e),JSON.stringify(r))}catch(e){Le.error("Error setting in-progress survey state in localStorage",e)}},Br=e=>{try{var r=localStorage.getItem(Ar(e));if(r)return JSON.parse(r)}catch(e){Le.error("Error getting in-progress survey state from localStorage",e)}return null},jr=e=>{var r=Br(e);return!Pe(null==r?void 0:r.surveySubmissionId)},zr=e=>{try{localStorage.removeItem(Ar(e))}catch(e){Le.error("Error clearing in-progress survey state from localStorage",e)}};function Rr(e,r){void 0===r&&(r=!1);var t="PostHogSurvey-"+e.id;return r?"."+t:t}var Ur=0;function Or(e,r,i,n,o,a){var u,s,l={};for(s in r)"ref"==s?u=r[s]:l[s]=r[s];var c={type:e,props:l,key:i,ref:u,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:--Ur,__i:-1,__u:0,__source:o,__self:a};if("function"==typeof e&&(u=e.defaultProps))for(s in u)void 0===l[s]&&(l[s]=u[s]);return t.vnode&&t.vnode(c),c}var $r=Or("svg",{className:"emoji-svg",xmlns:"http://www.w3.org/2000/svg",height:"48",viewBox:"0 -960 960 960",width:"48",children:Or("path",{d:"M626-533q22.5 0 38.25-15.75T680-587q0-22.5-15.75-38.25T626-641q-22.5 0-38.25 15.75T572-587q0 22.5 15.75 38.25T626-533Zm-292 0q22.5 0 38.25-15.75T388-587q0-22.5-15.75-38.25T334-641q-22.5 0-38.25 15.75T280-587q0 22.5 15.75 38.25T334-533Zm146 272q66 0 121.5-35.5T682-393h-52q-23 40-63 61.5T480.5-310q-46.5 0-87-21T331-393h-53q26 61 81 96.5T480-261Zm0 181q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-400Zm0 340q142.375 0 241.188-98.812Q820-337.625 820-480t-98.812-241.188Q622.375-820 480-820t-241.188 98.812Q140-622.375 140-480t98.812 241.188Q337.625-140 480-140Z"})}),Wr=Or("svg",{className:"emoji-svg",xmlns:"http://www.w3.org/2000/svg",height:"48",viewBox:"0 -960 960 960",width:"48",children:Or("path",{d:"M626-533q22.5 0 38.25-15.75T680-587q0-22.5-15.75-38.25T626-641q-22.5 0-38.25 15.75T572-587q0 22.5 15.75 38.25T626-533Zm-292 0q22.5 0 38.25-15.75T388-587q0-22.5-15.75-38.25T334-641q-22.5 0-38.25 15.75T280-587q0 22.5 15.75 38.25T334-533Zm20 194h253v-49H354v49ZM480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-400Zm0 340q142.375 0 241.188-98.812Q820-337.625 820-480t-98.812-241.188Q622.375-820 480-820t-241.188 98.812Q140-622.375 140-480t98.812 241.188Q337.625-140 480-140Z"})}),Kr=Or("svg",{className:"emoji-svg",xmlns:"http://www.w3.org/2000/svg",height:"48",viewBox:"0 -960 960 960",width:"48",children:Or("path",{d:"M626-533q22.5 0 38.25-15.75T680-587q0-22.5-15.75-38.25T626-641q-22.5 0-38.25 15.75T572-587q0 22.5 15.75 38.25T626-533Zm-292 0q22.5 0 38.25-15.75T388-587q0-22.5-15.75-38.25T334-641q-22.5 0-38.25 15.75T280-587q0 22.5 15.75 38.25T334-533Zm146.174 116Q413-417 358.5-379.5T278-280h53q22-42 62.173-65t87.5-23Q528-368 567.5-344.5T630-280h52q-25-63-79.826-100-54.826-37-122-37ZM480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-400Zm0 340q142.375 0 241.188-98.812Q820-337.625 820-480t-98.812-241.188Q622.375-820 480-820t-241.188 98.812Q140-622.375 140-480t98.812 241.188Q337.625-140 480-140Z"})}),Yr=Or("svg",{className:"emoji-svg",xmlns:"http://www.w3.org/2000/svg",height:"48",viewBox:"0 -960 960 960",width:"48",children:Or("path",{d:"M480-417q-67 0-121.5 37.5T278-280h404q-25-63-80-100t-122-37Zm-183-72 50-45 45 45 31-36-45-45 45-45-31-36-45 45-50-45-31 36 45 45-45 45 31 36Zm272 0 44-45 51 45 31-36-45-45 45-45-31-36-51 45-44-45-31 36 44 45-44 45 31 36ZM480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-400Zm0 340q142 0 241-99t99-241q0-142-99-241t-241-99q-142 0-241 99t-99 241q0 142 99 241t241 99Z"})}),Gr=Or("svg",{className:"emoji-svg",xmlns:"http://www.w3.org/2000/svg",height:"48",viewBox:"0 -960 960 960",width:"48",children:Or("path",{d:"M479.504-261Q537-261 585.5-287q48.5-26 78.5-72.4 6-11.6-.75-22.6-6.75-11-20.25-11H316.918Q303-393 296.5-382t-.5 22.6q30 46.4 78.5 72.4 48.5 26 105.004 26ZM347-578l27 27q7.636 8 17.818 8Q402-543 410-551q8-8 8-18t-8-18l-42-42q-8.8-9-20.9-9-12.1 0-21.1 9l-42 42q-8 7.636-8 17.818Q276-559 284-551q8 8 18 8t18-8l27-27Zm267 0 27 27q7.714 8 18 8t18-8q8-7.636 8-17.818Q685-579 677-587l-42-42q-8.8-9-20.9-9-12.1 0-21.1 9l-42 42q-8 7.714-8 18t8 18q7.636 8 17.818 8Q579-543 587-551l27-27ZM480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-400Zm0 340q142.375 0 241.188-98.812Q820-337.625 820-480t-98.812-241.188Q622.375-820 480-820t-241.188 98.812Q140-622.375 140-480t98.812 241.188Q337.625-140 480-140Z"})}),Jr=Or("svg",{width:"12",height:"12",viewBox:"0 0 12 12",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-labelledby":"close-survey-title",children:[Or("title",{id:"close-survey-title",children:"Close survey"}),Or("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M0.164752 0.164752C0.384422 -0.0549175 0.740578 -0.0549175 0.960248 0.164752L6 5.20451L11.0398 0.164752C11.2594 -0.0549175 11.6156 -0.0549175 11.8352 0.164752C12.0549 0.384422 12.0549 0.740578 11.8352 0.960248L6.79549 6L11.8352 11.0398C12.0549 11.2594 12.0549 11.6156 11.8352 11.8352C11.6156 12.0549 11.2594 12.0549 11.0398 11.8352L6 6.79549L0.960248 11.8352C0.740578 12.0549 0.384422 12.0549 0.164752 11.8352C-0.0549175 11.6156 -0.0549175 11.2594 0.164752 11.0398L5.20451 6L0.164752 0.960248C-0.0549175 0.740578 -0.0549175 0.384422 0.164752 0.164752Z",fill:"black"})]}),Xr=Or("svg",{width:"77",height:"14",viewBox:"0 0 77 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[Or("g",{"clip-path":"url(#clip0_2415_6911)",children:[Or("mask",{id:"mask0_2415_6911",style:{maskType:"luminance"},maskUnits:"userSpaceOnUse",x:"0",y:"0",width:"77",height:"14",children:Or("path",{d:"M0.5 0H76.5V14H0.5V0Z",fill:"white"})}),Or("g",{mask:"url(#mask0_2415_6911)",children:[Or("path",{d:"M5.77226 8.02931C5.59388 8.37329 5.08474 8.37329 4.90634 8.02931L4.4797 7.20672C4.41155 7.07535 4.41155 6.9207 4.4797 6.78933L4.90634 5.96669C5.08474 5.62276 5.59388 5.62276 5.77226 5.96669L6.19893 6.78933C6.26709 6.9207 6.26709 7.07535 6.19893 7.20672L5.77226 8.02931ZM5.77226 12.6946C5.59388 13.0386 5.08474 13.0386 4.90634 12.6946L4.4797 11.872C4.41155 11.7406 4.41155 11.586 4.4797 11.4546L4.90634 10.632C5.08474 10.288 5.59388 10.288 5.77226 10.632L6.19893 11.4546C6.26709 11.586 6.26709 11.7406 6.19893 11.872L5.77226 12.6946Z",fill:"#1D4AFF"}),Or("path",{d:"M0.5 10.9238C0.5 10.508 1.02142 10.2998 1.32637 10.5938L3.54508 12.7327C3.85003 13.0267 3.63405 13.5294 3.20279 13.5294H0.984076C0.716728 13.5294 0.5 13.3205 0.5 13.0627V10.9238ZM0.5 8.67083C0.5 8.79459 0.551001 8.91331 0.641783 9.00081L5.19753 13.3927C5.28831 13.4802 5.41144 13.5294 5.53982 13.5294H8.0421C8.47337 13.5294 8.68936 13.0267 8.3844 12.7327L1.32637 5.92856C1.02142 5.63456 0.5 5.84278 0.5 6.25854V8.67083ZM0.5 4.00556C0.5 4.12932 0.551001 4.24802 0.641783 4.33554L10.0368 13.3927C10.1276 13.4802 10.2508 13.5294 10.3791 13.5294H12.8814C13.3127 13.5294 13.5287 13.0267 13.2237 12.7327L1.32637 1.26329C1.02142 0.969312 0.5 1.17752 0.5 1.59327V4.00556ZM5.33931 4.00556C5.33931 4.12932 5.39033 4.24802 5.4811 4.33554L14.1916 12.7327C14.4965 13.0267 15.0179 12.8185 15.0179 12.4028V9.99047C15.0179 9.86671 14.9669 9.74799 14.8762 9.66049L6.16568 1.26329C5.86071 0.969307 5.33931 1.17752 5.33931 1.59327V4.00556ZM11.005 1.26329C10.7 0.969307 10.1786 1.17752 10.1786 1.59327V4.00556C10.1786 4.12932 10.2296 4.24802 10.3204 4.33554L14.1916 8.06748C14.4965 8.36148 15.0179 8.15325 15.0179 7.7375V5.3252C15.0179 5.20144 14.9669 5.08272 14.8762 4.99522L11.005 1.26329Z",fill:"#F9BD2B"}),Or("path",{d:"M21.0852 10.981L16.5288 6.58843C16.2238 6.29443 15.7024 6.50266 15.7024 6.91841V13.0627C15.7024 13.3205 15.9191 13.5294 16.1865 13.5294H23.2446C23.5119 13.5294 23.7287 13.3205 23.7287 13.0627V12.5032C23.7287 12.2455 23.511 12.0396 23.2459 12.0063C22.4323 11.9042 21.6713 11.546 21.0852 10.981ZM18.0252 12.0365C17.5978 12.0365 17.251 11.7021 17.251 11.2901C17.251 10.878 17.5978 10.5436 18.0252 10.5436C18.4527 10.5436 18.7996 10.878 18.7996 11.2901C18.7996 11.7021 18.4527 12.0365 18.0252 12.0365Z",fill:"currentColor"}),Or("path",{d:"M0.5 13.0627C0.5 13.3205 0.716728 13.5294 0.984076 13.5294H3.20279C3.63405 13.5294 3.85003 13.0267 3.54508 12.7327L1.32637 10.5938C1.02142 10.2998 0.5 10.508 0.5 10.9238V13.0627ZM5.33931 5.13191L1.32637 1.26329C1.02142 0.969306 0.5 1.17752 0.5 1.59327V4.00556C0.5 4.12932 0.551001 4.24802 0.641783 4.33554L5.33931 8.86412V5.13191ZM1.32637 5.92855C1.02142 5.63455 0.5 5.84278 0.5 6.25853V8.67083C0.5 8.79459 0.551001 8.91331 0.641783 9.00081L5.33931 13.5294V9.79717L1.32637 5.92855Z",fill:"#1D4AFF"}),Or("path",{d:"M10.1787 5.3252C10.1787 5.20144 10.1277 5.08272 10.0369 4.99522L6.16572 1.26329C5.8608 0.969306 5.33936 1.17752 5.33936 1.59327V4.00556C5.33936 4.12932 5.39037 4.24802 5.48114 4.33554L10.1787 8.86412V5.3252ZM5.33936 13.5294H8.04214C8.47341 13.5294 8.6894 13.0267 8.38443 12.7327L5.33936 9.79717V13.5294ZM5.33936 5.13191V8.67083C5.33936 8.79459 5.39037 8.91331 5.48114 9.00081L10.1787 13.5294V9.99047C10.1787 9.86671 10.1277 9.74803 10.0369 9.66049L5.33936 5.13191Z",fill:"#F54E00"}),Or("path",{d:"M29.375 11.6667H31.3636V8.48772H33.0249C34.8499 8.48772 36.0204 7.4443 36.0204 5.83052C36.0204 4.21681 34.8499 3.17334 33.0249 3.17334H29.375V11.6667ZM31.3636 6.84972V4.81136H32.8236C33.5787 4.81136 34.0318 5.19958 34.0318 5.83052C34.0318 6.4615 33.5787 6.84972 32.8236 6.84972H31.3636ZM39.618 11.7637C41.5563 11.7637 42.9659 10.429 42.9659 8.60905C42.9659 6.78905 41.5563 5.45438 39.618 5.45438C37.6546 5.45438 36.2701 6.78905 36.2701 8.60905C36.2701 10.429 37.6546 11.7637 39.618 11.7637ZM38.1077 8.60905C38.1077 7.63838 38.7118 6.97105 39.618 6.97105C40.5116 6.97105 41.1157 7.63838 41.1157 8.60905C41.1157 9.57972 40.5116 10.2471 39.618 10.2471C38.7118 10.2471 38.1077 9.57972 38.1077 8.60905ZM46.1482 11.7637C47.6333 11.7637 48.6402 10.8658 48.6402 9.81025C48.6402 7.33505 45.2294 8.13585 45.2294 7.16518C45.2294 6.8983 45.5189 6.72843 45.9342 6.72843C46.3622 6.72843 46.8782 6.98318 47.0418 7.54132L48.527 6.94678C48.2375 6.06105 47.1677 5.45438 45.8713 5.45438C44.4743 5.45438 43.6058 6.25518 43.6058 7.21372C43.6058 9.53118 46.9663 8.88812 46.9663 9.84665C46.9663 10.1864 46.6391 10.417 46.1482 10.417C45.4434 10.417 44.9525 9.94376 44.8015 9.3735L43.3164 9.93158C43.6436 10.8537 44.6001 11.7637 46.1482 11.7637ZM53.4241 11.606L53.2982 10.0651C53.0843 10.1743 52.8074 10.2106 52.5808 10.2106C52.1278 10.2106 51.8257 9.89523 51.8257 9.34918V7.03172H53.3612V5.55145H51.8257V3.78001H49.9755V5.55145H48.9687V7.03172H49.9755V9.57972C49.9755 11.06 51.0202 11.7637 52.3921 11.7637C52.7696 11.7637 53.122 11.7031 53.4241 11.606ZM59.8749 3.17334V6.47358H56.376V3.17334H54.3874V11.6667H56.376V8.11158H59.8749V11.6667H61.8761V3.17334H59.8749ZM66.2899 11.7637C68.2281 11.7637 69.6378 10.429 69.6378 8.60905C69.6378 6.78905 68.2281 5.45438 66.2899 5.45438C64.3265 5.45438 62.942 6.78905 62.942 8.60905C62.942 10.429 64.3265 11.7637 66.2899 11.7637ZM64.7796 8.60905C64.7796 7.63838 65.3837 6.97105 66.2899 6.97105C67.1835 6.97105 67.7876 7.63838 67.7876 8.60905C67.7876 9.57972 67.1835 10.2471 66.2899 10.2471C65.3837 10.2471 64.7796 9.57972 64.7796 8.60905ZM73.2088 11.4725C73.901 11.4725 74.5177 11.242 74.845 10.8416V11.424C74.845 12.1034 74.2786 12.5767 73.4102 12.5767C72.7935 12.5767 72.2523 12.2854 72.1642 11.788L70.4776 12.0428C70.7042 13.1955 71.925 13.972 73.4102 13.972C75.361 13.972 76.6574 12.8679 76.6574 11.2298V5.55145H74.8324V6.07318C74.4926 5.69705 73.9136 5.45438 73.171 5.45438C71.409 5.45438 70.3014 6.61918 70.3014 8.46345C70.3014 10.3077 71.409 11.4725 73.2088 11.4725ZM72.1012 8.46345C72.1012 7.55345 72.655 6.97105 73.5109 6.97105C74.3793 6.97105 74.9331 7.55345 74.9331 8.46345C74.9331 9.37345 74.3793 9.95585 73.5109 9.95585C72.655 9.95585 72.1012 9.37345 72.1012 8.46345Z",fill:"currentColor"})]})]}),Or("defs",{children:Or("clipPath",{id:"clip0_2415_6911",children:Or("rect",{width:"76",height:"14",fill:"white",transform:"translate(0.5)"})})})]});function et(){return Or("a",{href:"https://posthog.com/surveys",target:"_blank",rel:"noopener",className:"footer-branding",children:["Survey by ",Xr]})}function rt(e){var{text:r,submitDisabled:t,appearance:i,onSubmit:n,link:o,onPreviewSubmit:a,skipSubmitButton:u}=e,{isPreviewMode:s}=re(Lr);return Or("div",{className:"bottom-section",children:[!u&&Or("button",{className:"form-submit",disabled:t,"aria-label":"Submit survey",type:"button",onClick:()=>{o&&(null==be||be.open(o)),s?null==a||a():n()},children:r}),!i.whiteLabel&&Or(et,{})]})}function tt(e){var{question:r,forceDisableHtml:t,htmlFor:i}=e;return Or("div",{class:"question-header",children:[Or(r.type===de.Open?"label":"h3",{className:"survey-question",htmlFor:i,children:r.question}),r.description&&Dr({component:h("p",{className:"survey-question-description"}),children:r.description,renderAsHtml:!t&&"text"!==r.descriptionContentType})]})}function it(e){var{onClick:r}=e,{isPreviewMode:t}=re(Lr);return Or("button",{className:"form-cancel",onClick:r,disabled:t,"aria-label":"Close survey",type:"button",children:Jr})}Or("svg",{width:"16",height:"12",viewBox:"0 0 16 12",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:Or("path",{d:"M5.30769 10.6923L4.77736 11.2226C4.91801 11.3633 5.10878 11.4423 5.30769 11.4423C5.5066 11.4423 5.69737 11.3633 5.83802 11.2226L5.30769 10.6923ZM15.5303 1.53033C15.8232 1.23744 15.8232 0.762563 15.5303 0.46967C15.2374 0.176777 14.7626 0.176777 14.4697 0.46967L15.5303 1.53033ZM1.53033 5.85429C1.23744 5.56139 0.762563 5.56139 0.46967 5.85429C0.176777 6.14718 0.176777 6.62205 0.46967 6.91495L1.53033 5.85429ZM5.83802 11.2226L15.5303 1.53033L14.4697 0.46967L4.77736 10.162L5.83802 11.2226ZM0.46967 6.91495L4.77736 11.2226L5.83802 10.162L1.53033 5.85429L0.46967 6.91495Z",fill:"currentColor"})});var nt=be;function ot(e){var{header:r,description:t,contentType:i,forceDisableHtml:n,appearance:o,onClose:a}=e,{isPopup:u}=re(Lr);return J((()=>{var e=e=>{"Enter"!==e.key&&"Escape"!==e.key||(e.preventDefault(),a())};return Ze(nt,"keydown",e),()=>{nt.removeEventListener("keydown",e)}}),[a]),Or("div",{className:"thank-you-message",role:"status",tabIndex:0,"aria-atomic":"true",children:[u&&Or(it,{onClick:()=>a()}),Or("h3",{className:"thank-you-message-header",children:r}),t&&Dr({component:h("p",{className:"thank-you-message-body"}),children:t,renderAsHtml:!n&&"text"!==i}),u&&Or(rt,{text:o.thankYouMessageCloseButtonText||"Close",submitDisabled:!1,appearance:o,onSubmit:()=>a()})]})}var at=e=>qe(e)&&e.every((e=>Te(e)));function ut(e){var{question:r,forceDisableHtml:t,appearance:i,onSubmit:n,onPreviewSubmit:o,displayQuestionIndex:a,initialValue:u}=e,{isPreviewMode:s}=Nr(),l=X(null),[c,p]=G((()=>Te(u)?u:""));J((()=>{setTimeout((()=>{var e;s||(null==(e=l.current)||e.focus())}),100)}),[s]);var d="surveyQuestion"+a;return Or(b,{children:[Or("div",{className:"question-container",children:[Or(tt,{question:r,forceDisableHtml:t,htmlFor:d}),Or("textarea",{ref:l,id:d,rows:4,placeholder:null==i?void 0:i.placeholder,onInput:e=>{p(e.currentTarget.value),e.stopPropagation()},onKeyDown:e=>{e.stopPropagation()},value:c})]}),Or(rt,{text:r.buttonText||"Submit",submitDisabled:!c&&!r.optional,appearance:i,onSubmit:()=>n(c),onPreviewSubmit:()=>o(c)})]})}function st(e){var{question:r,forceDisableHtml:t,appearance:i,onSubmit:n,onPreviewSubmit:o}=e;return Or(b,{children:[Or("div",{className:"question-container",children:Or(tt,{question:r,forceDisableHtml:t})}),Or(rt,{text:r.buttonText||"Submit",submitDisabled:!1,link:r.link,appearance:i,onSubmit:()=>n("link clicked"),onPreviewSubmit:()=>o("link clicked")})]})}function lt(e){var{question:r,forceDisableHtml:t,displayQuestionIndex:i,appearance:n,onSubmit:o,onPreviewSubmit:a,initialValue:u}=e,s=r.scale,l=10===r.scale?0:1,[c,p]=G((()=>He(u)?u:qe(u)&&u.length>0&&He(parseInt(u[0]))?parseInt(u[0]):Te(u)&&He(parseInt(u))?parseInt(u):null)),{isPreviewMode:d}=Nr(),v=e=>d?a(e):o(e);return Or(b,{children:[Or("div",{className:"question-container",children:[Or(tt,{question:r,forceDisableHtml:t}),Or("div",{className:"rating-section",children:[Or("div",{className:"rating-options",children:["emoji"===r.display&&Or("div",{className:"rating-options-emoji",children:(3===r.scale?dt:vt).map(((e,t)=>Or("button",{"aria-label":"Rate "+(t+1),className:"ratings-emoji question-"+i+"-rating-"+t+" "+(t+1===c?"rating-active":""),value:t+1,type:"button",onClick:()=>{var e=t+1;p(e),r.skipSubmitButton&&v(e)},children:e},t)))}),"number"===r.display&&Or("div",{className:"rating-options-number",style:{gridTemplateColumns:"repeat("+(s-l+1)+", minmax(0, 1fr))"},children:bt(r.scale).map(((e,t)=>Or(ct,{displayQuestionIndex:i,active:c===e,appearance:n,num:e,setActiveNumber:e=>{p(e),r.skipSubmitButton&&v(e)}},t)))})]}),Or("div",{className:"rating-text",children:[Or("div",{children:r.lowerBoundLabel}),Or("div",{children:r.upperBoundLabel})]})]})]}),Or(rt,{text:r.buttonText||(null==n?void 0:n.submitButtonText)||"Submit",submitDisabled:Me(c)&&!r.optional,appearance:n,onSubmit:()=>o(c),onPreviewSubmit:()=>a(c),skipSubmitButton:r.skipSubmitButton})]})}function ct(e){var{num:r,active:t,displayQuestionIndex:i,setActiveNumber:n}=e;return Or("button",{"aria-label":"Rate "+r,className:"ratings-number question-"+i+"-rating-"+r+" "+(t?"rating-active":""),type:"button",onClick:()=>{n(r)},children:r})}function pt(r){var{question:t,forceDisableHtml:i,displayQuestionIndex:n,appearance:o,onSubmit:a,onPreviewSubmit:u,initialValue:s}=r,l=X(null),c=ee((()=>(e=>{if(!e.shuffleOptions)return e.choices;var r=e.choices,t="";e.hasOpenChoice&&(t=r.pop());var i=Hr(r,Pr(r));return e.hasOpenChoice&&(e.choices.push(t),i.push(t)),i})(t)),[t]),[p,d]=G((()=>((e,r)=>Te(e)||at(e)?e:r===de.SingleChoice?null:[])(s,t.type))),[v,f]=G((()=>((e,r)=>{if(Te(e)&&!r.includes(e))return{isSelected:!0,inputValue:e};if(at(e)){var t=e.find((e=>!r.includes(e)));if(t)return{isSelected:!0,inputValue:t}}return{isSelected:!1,inputValue:""}})(s,c))),{isPreviewMode:h}=Nr(),y=t.type===de.SingleChoice,m=t.type===de.MultipleChoice,g=t.skipSubmitButton&&y&&!t.hasOpenChoice,x=(r,t)=>{if(t){var i=!v.isSelected;return f((r=>e({},r,{isSelected:i,inputValue:i?r.inputValue:""}))),y&&d(""),void(i&&setTimeout((()=>{var e;return null==(e=l.current)?void 0:e.focus()}),75))}if(y)return d(r),f((r=>e({},r,{isSelected:!1,inputValue:""}))),void(g&&(a(r),h&&u(r)));m&&qe(p)&&(p.includes(r)?d(p.filter((e=>e!==r))):d([...p,r]))},w=r=>{r.stopPropagation();var t=r.currentTarget.value;f((r=>e({},r,{inputValue:t}))),y&&d(t)},k=r=>{r.stopPropagation(),"Enter"!==r.key||C()||(r.preventDefault(),S()),"Escape"===r.key&&(r.preventDefault(),f((r=>e({},r,{isSelected:!1,inputValue:""}))),y&&d(null))},C=()=>!t.optional&&(!!Me(p)||(!(!qe(p)||v.isSelected||0!==p.length)||!(!v.isSelected||""!==v.inputValue.trim()))),S=()=>{v.isSelected&&m?qe(p)&&(h?u([...p,v.inputValue]):a([...p,v.inputValue])):h?u(p):a(p)};return Or(b,{children:[Or("div",{className:"question-container",children:[Or(tt,{question:t,forceDisableHtml:i}),Or("fieldset",{className:"multiple-choice-options limit-height",children:[Or("legend",{className:"sr-only",children:m?" Select all that apply":" Select one"}),c.map(((e,r)=>{var i=!!t.hasOpenChoice&&r===t.choices.length-1,o="surveyQuestion"+n+"Choice"+r,a=o+"Open",u=i?v.isSelected:y?p===e:qe(p)&&p.includes(e);return Or("label",{className:i?"choice-option-open":"",children:[Or("div",{className:"response-choice",children:[Or("input",{type:y?"radio":"checkbox",name:o,checked:u,onChange:()=>x(e,i),id:o,"aria-controls":a}),Or("span",{children:i?e+":":e})]}),i&&Or("input",{type:"text",ref:l,id:a,name:"question"+n+"Open",value:v.inputValue,onKeyDown:k,onInput:w,onClick:r=>{v.isSelected||x(e,!0),r.stopPropagation()},"aria-label":e+" - please specify"})]},r)}))]})]}),Or(rt,{text:t.buttonText||"Submit",submitDisabled:C(),appearance:o,onSubmit:S,onPreviewSubmit:S,skipSubmitButton:g})]})}var dt=[Kr,Wr,$r],vt=[Yr,Kr,Wr,$r,Gr],ft=[1,2,3,4,5],ht=[1,2,3,4,5,6,7],yt=[0,1,2,3,4,5,6,7,8,9,10];function bt(e){switch(e){case 5:default:return ft;case 7:return ht;case 10:return yt}}var mt=be,gt=xe,xt="ph:show_survey_widget",wt="PHWidgetSurveyClickListener";function kt(e,r,t){var i,n=e.questions[r],o=r+1;if(null==(i=n.branching)||!i.type)return r===e.questions.length-1?ve.End:o;if(n.branching.type===ve.End)return ve.End;if(n.branching.type===ve.SpecificQuestion){if(Number.isInteger(n.branching.index))return n.branching.index}else if(n.branching.type===ve.ResponseBased){if(n.type===de.SingleChoice){var a,u=n.choices.indexOf(""+t);if(-1===u&&n.hasOpenChoice&&(u=n.choices.length-1),null!=(a=n.branching)&&null!=(a=a.responseValues)&&a.hasOwnProperty(u)){var s=n.branching.responseValues[u];return Number.isInteger(s)?s:s===ve.End?ve.End:o}}else if(n.type===de.Rating){var l;if("number"!=typeof t||!Number.isInteger(t))throw new Error("The response type must be an integer");var c=function(e,r){if(3===r){if(e<1||e>3)throw new Error("The response must be in range 1-3");return 1===e?"negative":2===e?"neutral":"positive"}if(5===r){if(e<1||e>5)throw new Error("The response must be in range 1-5");return e<=2?"negative":3===e?"neutral":"positive"}if(7===r){if(e<1||e>7)throw new Error("The response must be in range 1-7");return e<=3?"negative":4===e?"neutral":"positive"}if(10===r){if(e<0||e>10)throw new Error("The response must be in range 0-10");return e<=6?"detractors":e<=8?"passives":"promoters"}throw new Error("The scale must be one of: 3, 5, 7, 10")}(t,n.scale);if(null!=(l=n.branching)&&null!=(l=l.responseValues)&&l.hasOwnProperty(c)){var p=n.branching.responseValues[c];return Number.isInteger(p)?p:p===ve.End?ve.End:o}}return o}return Le.warn("Falling back to next question index due to unexpected branching type"),o}var Ct=250,St=20,qt=12;class _t{constructor(r){var t=this;this.S=new Map,this.q=new Map,this.T=r=>{var t;this.M(r.id),this.P(r);var i=(null==(t=r.appearance)?void 0:t.surveyPopupDelaySeconds)||0,{shadow:n}=qr(r,this._posthog);if(i<=0)return D(Or(Ht,{posthog:this._posthog,survey:r,removeSurveyFromFocus:this.H}),n);var o=setTimeout((()=>{if(!Fr(r))return this.H(r);D(Or(Ht,{posthog:this._posthog,survey:e({},r,{appearance:e({},r.appearance,{surveyPopupDelaySeconds:0})}),removeSurveyFromFocus:this.H}),n)}),1e3*i);this.S.set(r.id,o)},this.I=e=>{var{shadow:r,isNewlyCreated:t}=qr(e,this._posthog);t&&D(Or(Vt,{posthog:this._posthog,survey:e},e.id),r)},this.V=e=>{this.Z(e);var r=this.q.get(e.id);r&&(r.element.removeEventListener("click",r.listener),r.element.removeAttribute(wt),this.q.delete(e.id),Le.info("Removed click listener for survey "+e.id))},this.L=(e,r)=>{var t=gt.querySelector(r),i=this.q.get(e.id);if(t){if(this.I(e),i){if(t===i.element)return;Le.info("Selector element changed for survey "+e.id+". Re-attaching listener."),this.V(e)}if(!t.hasAttribute(wt)){var n=r=>{var t,i;r.stopPropagation();var n=(null==(t=e.appearance)?void 0:t.position)===ce.NextToTrigger?function(e,r){try{var t=e.getBoundingClientRect(),i=mt.innerHeight,n=mt.innerWidth,o=Ct,a=t.left+t.width/2-r/2;a+r>n-St&&(a=n-r-St),a<St&&(a=St);var u=qt,s=i-t.bottom,l=t.top,c=s<o&&l>s;return{position:"fixed",top:c?"auto":t.bottom+u+"px",left:a+"px",right:"auto",bottom:c?i-t.top+u+"px":"auto",zIndex:wr.zIndex}}catch(e){return Le.warn("Failed to calculate trigger position:",e),null}}(r.currentTarget,parseInt((null==(i=e.appearance)?void 0:i.maxWidth)||wr.maxWidth)):{};mt.dispatchEvent(new CustomEvent(xt,{detail:{surveyId:e.id,position:n}}))};Ze(t,"click",n),t.setAttribute(wt,"true"),this.q.set(e.id,{element:t,listener:n,survey:e}),Le.info("Attached click listener for feedback button survey "+e.id)}}else i&&this.V(e)},this.renderSurvey=(e,r)=>{D(Or(Ht,{posthog:this._posthog,survey:e,removeSurveyFromFocus:this.H,isPopup:!1}),r)},this.getActiveMatchingSurveys=function(e,r){var i;void 0===r&&(r=!1),null==(i=t._posthog)||i.surveys.getSurveys((r=>{var i=r.filter((e=>t.checkSurveyEligibility(e).eligible&&t.N(e)&&t.D(e)&&t.F(e)));e(i)}),r)},this.callSurveysAndEvaluateDisplayLogic=function(e){void 0===e&&(e=!1),t.getActiveMatchingSurveys((e=>{var r=e.filter((e=>e.type===pe.Popover||e.type===pe.Widget)),i=t.A(r),n=new Set;i.forEach((e=>{if(e.type===pe.Widget){var r,i,o,a;if((null==(r=e.appearance)?void 0:r.widgetType)===le.Tab)return void t.I(e);if((null==(i=e.appearance)?void 0:i.widgetType)===le.Selector&&null!=(o=e.appearance)&&o.widgetSelector)n.add(e.id),t.L(e,null==(a=e.appearance)?void 0:a.widgetSelector)}Me(t.B)&&e.type===pe.Popover&&t.T(e)})),t.q.forEach((e=>{var{survey:r}=e;n.has(r.id)||t.V(r)}))}),e)},this.P=e=>{Me(this.B)||Le.error("Survey "+[...this.B]+" already in focus. Cannot add survey "+e.id+"."),this.B=e.id},this.H=e=>{this.B!==e.id&&Le.error("Survey "+e.id+" is not in focus. Cannot remove survey "+e.id+"."),this.M(e.id),this.B=null,this.Z(e)},this._posthog=r,this.B=null}M(e){var r=this.S.get(e);r&&(clearTimeout(r),this.S.delete(e))}A(e){return e.sort(((e,r)=>{var t,i,n=jr(e),o=jr(r);if(n&&!o)return-1;if(!n&&o)return 1;var a=e.schedule===fe.Always,u=r.schedule===fe.Always;return a&&!u?1:!a&&u?-1:((null==(t=e.appearance)?void 0:t.surveyPopupDelaySeconds)||0)-((null==(i=r.appearance)?void 0:i.surveyPopupDelaySeconds)||0)}))}j(e){return!e||!!this._posthog.featureFlags.isFeatureEnabled(e,{send_event:!e.startsWith("survey-targeting-")})}N(e){return!e.conditions||Fr(e)&&function(e){var r,t,i;if(null==(r=e.conditions)||!r.deviceTypes||0===(null==(t=e.conditions)?void 0:t.deviceTypes.length))return!0;if(!we)return!1;var n=dr(we);return fr[Er(null==(i=e.conditions)?void 0:i.deviceTypesMatchType)](e.conditions.deviceTypes,[n])}(e)&&function(e){var r;return null==(r=e.conditions)||!r.selector||!(null==mr||!mr.querySelector(e.conditions.selector))}(e)}R(e){return Vr(e)||this.j(e.internal_targeting_flag_key)||jr(e)}checkSurveyEligibility(e){var r,t={eligible:!0,reason:void 0};return function(e){return!(!e.start_date||e.end_date)}(e)?Ee.includes(e.type)?this.j(e.linked_flag_key)?this.j(e.targeting_flag_key)?this.R(e)?(e=>{var r=localStorage.getItem("lastSeenSurveyDate");if(!e||!r)return!0;var t=new Date,i=Math.abs(t.getTime()-new Date(r).getTime());return Math.ceil(i/864e5)>e})(null==(r=e.conditions)?void 0:r.seenSurveyWaitPeriodInDays)?(e=>!!localStorage.getItem(Zr(e))&&!Vr(e))(e)?(t.eligible=!1,t.reason="Survey has already been seen and it can't be activated again",t):t:(t.eligible=!1,t.reason="Survey wait period has not passed",t):(t.eligible=!1,t.reason="Survey internal targeting flag is not enabled and survey cannot activate repeatedly and survey is not in progress",t):(t.eligible=!1,t.reason="Survey targeting feature flag is not enabled",t):(t.eligible=!1,t.reason="Survey linked feature flag is not enabled",t):(t.eligible=!1,t.reason="Surveys of type "+e.type+" are never eligible to be shown in the app",t):(t.eligible=!1,t.reason="Survey is not running. It was completed on "+e.end_date,t)}D(e){var r;if(!function(e){var r;return!(null==(r=e.conditions)||null==(r=r.events)||null==(r=r.values)||!r.length)}(e)&&!function(e){var r;return!(null==(r=e.conditions)||null==(r=r.actions)||null==(r=r.values)||!r.length)}(e))return!0;var t=null==(r=this._posthog.surveys._surveyEventReceiver)?void 0:r.getSurveys();return!(null==t||!t.includes(e.id))}F(e){var r;return null==(r=e.feature_flag_keys)||!r.length||e.feature_flag_keys.every((e=>{var{key:r,value:t}=e;return!r||!t||this.j(t)}))}Z(e){try{var r=gt.querySelector(Rr(e,!0));null!=r&&r.shadowRoot&&D(null,r.shadowRoot),null==r||r.remove()}catch(r){Le.warn("Failed to remove survey "+e.id+" from DOM:",r)}}getTestAPI(){return{addSurveyToFocus:this.P,removeSurveyFromFocus:this.H,surveyInFocus:this.B,surveyTimeouts:this.S,handleWidget:this.I,handlePopoverSurvey:this.T,manageWidgetSelectorListener:this.L,sortSurveysByAppearanceDelay:this.A,checkFlags:this.F.bind(this),isSurveyFeatureFlagEnabled:this.j.bind(this)}}}function Tt(e,r){if(gt&&mt){var t=new _t(e);return e.config.disable_surveys_automatic_display?(Le.info("Surveys automatic display is disabled. Skipping call surveys and evaluate display logic."),t):!1===r?(Le.info("There are no surveys to load or Surveys is disabled in the project settings."),t):(t.callSurveysAndEvaluateDisplayLogic(!0),setInterval((()=>{t.callSurveysAndEvaluateDisplayLogic(!1)}),1e3),t)}}function Mt(e){var{survey:r,removeSurveyFromFocus:t=(()=>{}),setSurveyVisible:i,isPreviewMode:n=!1}=e;J((()=>{var e;if(!n&&null!=(e=r.conditions)&&e.url){var o=()=>{var e,n=r.type===pe.Widget,o=Fr(r),a=(null==(e=r.appearance)?void 0:e.widgetType)===le.Tab&&n;if(!o)return Le.info("Hiding survey "+r.id+" because URL does not match"),i(!1),t(r);a&&(Le.info("Showing survey "+r.id+" because it is a feedback button tab and URL matches"),i(!0))};Ze(mt,"popstate",o),Ze(mt,"hashchange",o);var a=mt.history.pushState,u=mt.history.replaceState;return mt.history.pushState=function(){for(var e=arguments.length,r=new Array(e),t=0;t<e;t++)r[t]=arguments[t];a.apply(this,r),o()},mt.history.replaceState=function(){for(var e=arguments.length,r=new Array(e),t=0;t<e;t++)r[t]=arguments[t];u.apply(this,r),o()},()=>{mt.removeEventListener("popstate",o),mt.removeEventListener("hashchange",o),mt.history.pushState=a,mt.history.replaceState=u}}}),[n,r,t,i])}function Pt(e,r,t){switch(void 0===r&&(r=ce.Right),r){case ce.TopLeft:return{top:"0",left:"0",transform:"translate(30px, 30px)"};case ce.TopRight:return{top:"0",right:"0",transform:"translate(-30px, 30px)"};case ce.TopCenter:return{top:"0",left:"50%",transform:"translate(-50%, 30px)"};case ce.MiddleLeft:return{top:"50%",left:"0",transform:"translate(30px, -50%)"};case ce.MiddleRight:return{top:"50%",right:"0",transform:"translate(-30px, -50%)"};case ce.MiddleCenter:return{top:"50%",left:"50%",transform:"translate(-50%, -50%)"};case ce.Left:return{left:"30px"};case ce.Center:return{left:"50%",transform:"translateX(-50%)"};default:case ce.Right:return{right:e===pe.Widget&&t===le.Tab?"60px":"30px"}}}function Ht(r){var t,i,n,o,a,u,{survey:s,forceDisableHtml:l,posthog:c,style:p={},previewPageIndex:d,removeSurveyFromFocus:v=(()=>{}),isPopup:f=!0,onPreviewSubmit:h=(()=>{}),onPopupSurveyDismissed:y=(()=>{}),onCloseConfirmationMessage:b=(()=>{})}=r,m=X(null),g=Number.isInteger(d),x=null!=(t=s.appearance)&&t.surveyPopupDelaySeconds?1e3*s.appearance.surveyPopupDelaySeconds:0,{isPopupVisible:w,isSurveySent:k,hidePopupWithViewTransition:C}=function(e,r,t,i,n,o){var[a,u]=G(i||0===t),[s,l]=G(!1),c=()=>{var r=()=>{e.type===pe.Popover&&n(e),u(!1)};gt.startViewTransition?gt.startViewTransition((()=>{var e;null==o||null==(e=o.current)||e.remove()})).finished.then((()=>{setTimeout((()=>{r()}),100)})):r()},p=r=>{r.detail.surveyId===e.id&&c()};return J((()=>{if(r){if(!i){var n=r=>{var t,i;if(r.detail.surveyId===e.id){if(null==(t=e.appearance)||!t.displayThankYouMessage)return c();l(!0),null!=(i=e.appearance)&&i.autoDisappear&&setTimeout((()=>{c()}),5e3)}},o=()=>{Fr(e)&&(u(!0),mt.dispatchEvent(new Event("PHSurveyShown")),r.capture(he.SHOWN,{[ye.SURVEY_NAME]:e.name,[ye.SURVEY_ID]:e.id,[ye.SURVEY_ITERATION]:e.current_iteration,[ye.SURVEY_ITERATION_START_DATE]:e.current_iteration_start_date,sessionRecordingUrl:null==r.get_session_replay_url?void 0:r.get_session_replay_url()}),localStorage.setItem("lastSeenSurveyDate",(new Date).toISOString()))};if(Ze(mt,"PHSurveyClosed",p),Ze(mt,"PHSurveySent",n),t>0){var a=setTimeout(o,t);return()=>{clearTimeout(a),mt.removeEventListener("PHSurveyClosed",p),mt.removeEventListener("PHSurveySent",n)}}return o(),()=>{mt.removeEventListener("PHSurveyClosed",p),mt.removeEventListener("PHSurveySent",n)}}}else Le.error("usePopupVisibility hook called without a PostHog instance.")}),[]),Mt({survey:e,removeSurveyFromFocus:n,setSurveyVisible:u,isPreviewMode:i}),{isPopupVisible:a,isSurveySent:s,setIsPopupVisible:u,hidePopupWithViewTransition:c}}(s,c,x,g,v,m),S=k||d===s.questions.length,q=ee((()=>{var e=Br(s);return{isPreviewMode:g,previewPageIndex:d,onPopupSurveyDismissed:()=>{Mr(s,c,g),y()},isPopup:f||!1,surveySubmissionId:(null==e?void 0:e.surveySubmissionId)||Re(),onPreviewSubmit:h,posthog:c}}),[g,d,f,c,s,y,h]);return w?Or(Lr.Provider,{value:q,children:Or("div",{className:"ph-survey",style:e({},Pt(s.type,null==(i=s.appearance)?void 0:i.position,null==(n=s.appearance)?void 0:n.widgetType),p),ref:m,children:S?Or(ot,{header:(null==(o=s.appearance)?void 0:o.thankYouMessageHeader)||"Thank you!",description:(null==(a=s.appearance)?void 0:a.thankYouMessageDescription)||"",forceDisableHtml:!!l,contentType:null==(u=s.appearance)?void 0:u.thankYouMessageDescriptionContentType,appearance:s.appearance||wr,onClose:()=>{C(),b()}}):Or(It,{survey:s,forceDisableHtml:!!l,posthog:c})})}):null}function It(r){var{survey:t,forceDisableHtml:i,posthog:n}=r,[o,a]=G((()=>{var e=Br(t);return null!=e&&e.responses&&Le.info("Survey is already in progress, filling in initial responses"),(null==e?void 0:e.responses)||{}})),{previewPageIndex:u,onPopupSurveyDismissed:s,isPopup:l,onPreviewSubmit:c,surveySubmissionId:p,isPreviewMode:d}=re(Lr),[v,f]=G((()=>{var e=Br(t);return u||(null==e?void 0:e.lastQuestionIndex)||0})),h=ee((()=>Ir(t)),[t]);J((()=>{d&&!_e(u)&&f(u)}),[u,d]);var y=h.at(v);return y?Or("form",{className:"survey-form",name:"surveyForm",children:[l&&Or(it,{onClick:()=>{s()}}),Or("div",{className:"survey-box",children:Zt({question:y,forceDisableHtml:i,displayQuestionIndex:v,appearance:t.appearance||wr,onSubmit:r=>(r=>{var{res:i,displayQuestionIndex:u,questionId:s}=r;if(n)if(s){var l=gr(s),c=e({},o,{[l]:i});a(c);var d=kt(t,u,i),v=d===ve.End;v||(f(d),Qr(t,{surveySubmissionId:p,responses:c,lastQuestionIndex:d})),(t.enable_partial_responses||v)&&Tr({responses:c,survey:t,surveySubmissionId:p,isSurveyCompleted:v,posthog:n})}else Le.error("onNextButtonClick called without a questionId.");else Le.error("onNextButtonClick called without a PostHog instance.")})({res:r,displayQuestionIndex:v,questionId:y.id}),onPreviewSubmit:c,initialValue:y.id?o[gr(y.id)]:void 0})})]}):null}function Vt(e){var r,t,i,n,o,{survey:a,forceDisableHtml:u,posthog:s,readOnly:l}=e,[c,p]=G(!0),[d,v]=G(!1),[f,h]=G({}),y=()=>{v(!d)};if(J((()=>{var e;if(s){if(!l){"tab"===(null==(e=a.appearance)?void 0:e.widgetType)&&h({top:"50%",bottom:"auto"});var r=e=>{var r,t=e;(null==(r=t.detail)?void 0:r.surveyId)===a.id&&(Le.info("Received show event for feedback button survey "+a.id),h(t.detail.position||{}),y())};return Ze(mt,xt,r),()=>{mt.removeEventListener(xt,r)}}}else Le.error("FeedbackWidget called without a PostHog instance.")}),[s,l,a.id,null==(r=a.appearance)?void 0:r.widgetType,null==(t=a.appearance)?void 0:t.widgetSelector,null==(i=a.appearance)?void 0:i.borderColor]),Mt({survey:a,setSurveyVisible:p}),!c)return null;var m=()=>{a.schedule!==fe.Always&&p(!1),setTimeout((()=>{v(!1)}),200)};return Or(b,{children:["tab"===(null==(n=a.appearance)?void 0:n.widgetType)&&Or("button",{className:"ph-survey-widget-tab",onClick:y,disabled:l,children:(null==(o=a.appearance)?void 0:o.widgetLabel)||""}),d&&Or(Ht,{posthog:s,survey:a,forceDisableHtml:u,style:f,onPopupSurveyDismissed:m,onCloseConfirmationMessage:m})]})}var Zt=r=>{var{question:t,forceDisableHtml:i,displayQuestionIndex:n,appearance:o,onSubmit:a,onPreviewSubmit:u,initialValue:s}=r,l={forceDisableHtml:i,appearance:o,onPreviewSubmit:e=>{u(e)},onSubmit:e=>{a(e)},initialValue:s,displayQuestionIndex:n};switch(t.type){case de.Open:return h(ut,e({},l,{question:t,key:t.id}));case de.Link:return h(st,e({},l,{question:t,key:t.id}));case de.Rating:return h(lt,e({},l,{question:t,key:t.id}));case de.SingleChoice:case de.MultipleChoice:return h(pt,e({},l,{question:t,key:t.id}));default:return Le.error("Unsupported question type: "+t.type),null}};ke.__PosthogExtensions__=ke.__PosthogExtensions__||{},ke.__PosthogExtensions__.generateSurveys=Tt,ke.extendPostHogWithSurveys=Tt}();
//# sourceMappingURL=surveys.js.map
