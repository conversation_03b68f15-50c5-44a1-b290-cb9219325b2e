(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6470],{26279:function(e,t,r){Promise.resolve().then(r.bind(r,36901))},36901:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return GlobalError}});var n=r(57437),o=r(40109),a=r(1375),u=r.n(a),i=r(2265);function GlobalError(e){var t=e.error;return(0,i.useEffect)(function(){o.Tb(t)},[t]),(0,n.jsx)("html",{lang:"en",children:(0,n.jsx)("body",{children:(0,n.jsx)(u(),{})})})}},55327:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(86335),o=r(40494),a=r(55688),u=r(21889);Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}});var i=r(70817),l=i._(r(2265)),d=i._(r(35793)),f={400:"Bad Request",404:"This page could not be found",405:"Method Not Allowed",500:"Internal Server Error"};function _getInitialProps(e){var t=e.res,r=e.err;return{statusCode:t&&t.statusCode?t.statusCode:r?r.statusCode:404}}var c={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},desc:{lineHeight:"48px"},h1:{display:"inline-block",margin:"0 20px 0 0",paddingRight:23,fontSize:24,fontWeight:500,verticalAlign:"top"},h2:{fontSize:14,fontWeight:400,lineHeight:"28px"},wrap:{display:"inline-block"}},s=function(e){a._(Error,e);var t=u._(Error);function Error(){return n._(this,Error),t.apply(this,arguments)}return o._(Error,[{key:"render",value:function(){var e=this.props,t=e.statusCode,r=e.withDarkMode,n=this.props.title||f[t]||"An unexpected error has occurred";return l.default.createElement("div",{style:c.error},l.default.createElement(d.default,null,l.default.createElement("title",null,t?t+": "+n:"Application error: a client-side exception has occurred")),l.default.createElement("div",{style:c.desc},l.default.createElement("style",{dangerouslySetInnerHTML:{__html:"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}"+(void 0===r||r?"@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}":"")}}),t?l.default.createElement("h1",{className:"next-error-h1",style:c.h1},t):null,l.default.createElement("div",{style:c.wrap},l.default.createElement("h2",{style:c.h2},this.props.title||t?n:l.default.createElement(l.default.Fragment,null,"Application error: a client-side exception has occurred (see the browser console for more information)"),"."))))}}]),Error}(l.default.Component);s.displayName="ErrorPage",s.getInitialProps=_getInitialProps,s.origGetInitialProps=_getInitialProps,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},28569:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AmpStateContext",{enumerable:!0,get:function(){return n}});var n=r(70817)._(r(2265)).default.createContext({})},94472:function(e,t){"use strict";function isInAmpMode(e){var t=void 0===e?{}:e,r=t.ampFirst,n=t.hybrid,o=t.hasQuery;return void 0!==r&&r||void 0!==n&&n&&void 0!==o&&o}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return isInAmpMode}})},35793:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(41369);Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{defaultHead:function(){return defaultHead},default:function(){return _default}});var o=r(70817),a=r(37401)._(r(2265)),u=o._(r(80110)),i=r(28569),l=r(61852),d=r(94472);function defaultHead(e){void 0===e&&(e=!1);var t=[a.default.createElement("meta",{charSet:"utf-8"})];return e||t.push(a.default.createElement("meta",{name:"viewport",content:"width=device-width"})),t}function onlyReactElement(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===a.default.Fragment?e.concat(a.default.Children.toArray(t.props.children).reduce(function(e,t){return"string"==typeof t||"number"==typeof t?e:e.concat(t)},[])):e.concat(t)}r(92637);var f=["name","httpEquiv","charSet","itemProp"];function reduceComponents(e,t){var r,o,u,i,l=t.inAmpMode;return e.reduce(onlyReactElement,[]).reverse().concat(defaultHead(l).reverse()).filter((r=new Set,o=new Set,u=new Set,i={},function(e){var t=!0,n=!1;if(e.key&&"number"!=typeof e.key&&e.key.indexOf("$")>0){n=!0;var a=e.key.slice(e.key.indexOf("$")+1);r.has(a)?t=!1:r.add(a)}switch(e.type){case"title":case"base":o.has(e.type)?t=!1:o.add(e.type);break;case"meta":for(var l=0,d=f.length;l<d;l++){var c=f[l];if(e.props.hasOwnProperty(c)){if("charSet"===c)u.has(c)?t=!1:u.add(c);else{var s=e.props[c],p=i[c]||new Set;("name"!==c||!n)&&p.has(s)?t=!1:(p.add(s),i[c]=p)}}}}return t})).reverse().map(function(e,t){var r=e.key||t;if(!l&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(function(t){return e.props.href.startsWith(t)})){var o=n._({},e.props||{});return o["data-href"]=o.href,o.href=void 0,o["data-optimized-fonts"]=!0,a.default.cloneElement(e,o)}return a.default.cloneElement(e,{key:r})})}var _default=function(e){var t=e.children,r=(0,a.useContext)(i.AmpStateContext),n=(0,a.useContext)(l.HeadManagerContext);return a.default.createElement(u.default,{reduceComponentsToState:reduceComponents,headManager:n,inAmpMode:(0,d.isInAmpMode)(r)},t)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},80110:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return SideEffect}});var n=r(2265),o=n.useLayoutEffect,a=n.useEffect;function SideEffect(e){var t=e.headManager,r=e.reduceComponentsToState;function emitChange(){if(t&&t.mountedInstances){var o=n.Children.toArray(Array.from(t.mountedInstances).filter(Boolean));t.updateHead(r(o,e))}}return o(function(){var r;return null==t||null==(r=t.mountedInstances)||r.add(e.children),function(){var r;null==t||null==(r=t.mountedInstances)||r.delete(e.children)}}),o(function(){return t&&(t._pendingUpdate=emitChange),function(){t&&(t._pendingUpdate=emitChange)}}),a(function(){return t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null),function(){t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null)}}),null}},92637:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return warnOnce}});var warnOnce=function(e){}},30622:function(e,t,r){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var n=r(2265),o=Symbol.for("react.element"),a=Symbol.for("react.fragment"),u=Object.prototype.hasOwnProperty,i=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function q(e,t,r){var n,a={},d=null,f=null;for(n in void 0!==r&&(d=""+r),void 0!==t.key&&(d=""+t.key),void 0!==t.ref&&(f=t.ref),t)u.call(t,n)&&!l.hasOwnProperty(n)&&(a[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps)void 0===a[n]&&(a[n]=t[n]);return{$$typeof:o,type:e,key:d,ref:f,props:a,_owner:i.current}}t.Fragment=a,t.jsx=q,t.jsxs=q},57437:function(e,t,r){"use strict";e.exports=r(30622)},1375:function(e,t,r){e.exports=r(55327)}},function(e){e.O(0,[2971,9119,8814,1744],function(){return e(e.s=26279)}),_N_E=e.O()}]);