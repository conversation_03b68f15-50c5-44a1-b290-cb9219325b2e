* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: "Open Sans", -apple-system, system-ui, -apple-system,
    BlinkMacSystemFont, sans-serif;
  line-height: 1.6;
  color: #2b2b2b;
  background-color: #faf7f2;
  overflow-x: hidden;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Poppins", sans-serif;
  font-weight: 700;
  line-height: 1.2;
  color: #2b2b2b;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-tag {
  display: inline-block;
  background: linear-gradient(135deg, #004aad 0%, #3cb878 100%);
  color: white;
  padding: 8px 20px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 16px;
  font-family: "Merriweather Sans", sans-serif;
}

.section-header h2 {
  font-size: 2.75rem;
  color: #2b2b2b;
  margin-bottom: 16px;
  font-weight: 700;
}

.section-description {
  font-size: 1.125rem;
  color: #2b2b2b;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.7;
}

.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: #ffffff;
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
  z-index: 1000;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar.scrolled {
  background: #ffffff;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-logo {
  display: flex;
  align-items: center;
  gap: 8px;
}
.logo-icon {
  width: 32px;
  height: 32px;
  background: #2563eb;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 16px;
}

.logo-text {
  font-family: "Poppins", sans-serif;
  font-size: 1.25rem;
  font-weight: 600;
  display: flex;
  flex-direction: column;
  line-height: 1.1;
}

.logo-avaya {
  color: #2563eb;
  font-weight: 700;
}

.logo-exports {
  color: #374151;
  font-weight: 400;
  font-size: 0.9em;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 32px;
  align-items: center;
}

.nav-menu a {
  text-decoration: none;
  color: #374151;
  font-weight: 500;
  font-size: 15px;
  transition: all 0.3s ease;
  position: relative;
}

.nav-menu a:hover {
  color: #2563eb;
}

.nav-menu a::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: #2563eb;
  transition: width 0.3s ease;
}

.nav-menu a:hover::after {
  width: 100%;
}

.nav-cta-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
}

.btn-distributor,
.btn-shop {
  padding: 12px 24px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  font-family: "Poppins", sans-serif;
}

.btn-distributor {
  background: #10b981;
  color: white;
  border-color: #10b981;
  border-radius: 8px;
  padding: 12px 24px;
}

.btn-distributor:hover {
  background: #059669;
  border-color: #059669;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.btn-shop {
  background: transparent;
  color: #2563eb;
  border-color: #2563eb;
  border-radius: 8px;
  padding: 12px 24px;
}

.btn-shop:hover {
  background: #2563eb;
  color: white;
  border-color: #2563eb;
}

.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 4px;
}

.mobile-menu-toggle span {
  width: 25px;
  height: 3px;
  background: white;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.mobile-menu-toggle.active span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-toggle.active span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* Hero Section */
.hero {
  position: relative;
  margin-top: 80px;
  min-height: 65vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-image: url("./image/Milk and Dairy.jpg");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: -2;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(37, 99, 235, 0.05) 0%,
    rgba(59, 130, 246, 0.1) 100%
  );
  z-index: -1;
}

.hero-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(
      circle at 25% 25%,
      rgba(37, 99, 235, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 75% 75%,
      rgba(59, 130, 246, 0.08) 0%,
      transparent 50%
    );
  object-fit: cover;
  z-index: -1;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 80px;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #ffc107, #3cb878);
  border-radius: 30px;
  padding: 12px 20px;
  box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 24px;
  color: white;
  font-family: "Merriweather Sans", sans-serif;
}

.hero-title {
  font-size: 4rem;
  font-weight: 800;
  color: white;
  line-height: 1.2;
  margin-bottom: 20px;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: whitesmoke;
  line-height: 1.6;
  margin-bottom: 40px;
  max-width: 500px;
}

.hero-actions {
  display: flex;
  gap: 20px;
  margin-bottom: 50px;
}

.btn-primary,
.btn-secondary {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 16px 32px;
  border-radius: 12px;
  text-decoration: none;
  font-weight: 600;
  font-size: 15px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  font-family: "Merriweather Sans", sans-serif;
}

.btn-primary {
  background: #3cb878;
  color: white;
  box-shadow: 0 8px 25px rgba(60, 184, 120, 0.3);
}

.btn-primary:hover {
  background: #ffc107;
  color: #2b2b2b;
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(255, 193, 7, 0.4);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.9);
  color: #2b2b2b;
  border-color: rgba(255, 255, 255, 0.9);
}

.btn-secondary:hover {
  background: #3cb878;
  color: white;
  border-color: #3cb878;
  transform: translateY(-2px);
}

/* Our Story Section */
.our-story {
  padding: 80px 0;
  background: #f8fafc;
}

.our-story-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  margin-bottom: 60px;
}

.our-story-text {
  max-width: 500px;
}

.section-badge {
  display: inline-block;
  background: #10b981;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 24px;
  font-family: "Poppins", sans-serif;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.2;
  margin-bottom: 24px;
  font-family: "Poppins", sans-serif;
}

.section-description {
  font-size: 16px;
  line-height: 1.7;
  color: #6b7280;
  margin-bottom: 20px;
  font-family: "Inter", sans-serif;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32px;
  margin-top: 40px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2563eb;
  margin-bottom: 8px;
  font-family: "Poppins", sans-serif;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
  font-family: "Inter", sans-serif;
}

.our-story-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.world-map {
  width: 100%;
  max-width: 400px;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Exclusive Distributor Section */
.exclusive-distributor {
  background: #2563eb;
  border-radius: 16px;
  padding: 40px;
  color: white;
}

.exclusive-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.exclusive-text h3 {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 8px;
  font-family: "Poppins", sans-serif;
}

.exclusive-text p {
  font-size: 16px;
  opacity: 0.9;
  font-family: "Inter", sans-serif;
}

.official-badge {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.1);
  padding: 16px 24px;
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.badge-icon {
  font-size: 24px;
  color: #10b981;
}

.badge-text {
  display: flex;
  flex-direction: column;
}

.badge-title {
  font-size: 14px;
  font-weight: 600;
  font-family: "Poppins", sans-serif;
}

.badge-subtitle {
  font-size: 12px;
  opacity: 0.8;
  font-family: "Inter", sans-serif;
}

/* Responsive Design for Our Story Section */
@media (max-width: 768px) {
  .our-story {
    padding: 60px 0;
  }

  .our-story-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .section-title {
    font-size: 2rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 24px;
    margin-top: 32px;
  }

  .exclusive-content {
    flex-direction: column;
    gap: 24px;
    text-align: center;
  }

  .exclusive-distributor {
    padding: 32px 24px;
  }

  .official-badge {
    align-self: center;
  }
}

/* Products Section */
.products {
  padding: 80px 0;
  background: #ffffff;
}

.products-header {
  text-align: center;
  margin-bottom: 48px;
}

.products-header .section-badge {
  display: inline-block;
  background: #10b981;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 24px;
  font-family: "Poppins", sans-serif;
}

.products-header .section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.2;
  margin-bottom: 16px;
  font-family: "Poppins", sans-serif;
}

.products-header .section-description {
  font-size: 16px;
  line-height: 1.7;
  color: #6b7280;
  max-width: 600px;
  margin: 0 auto;
  font-family: "Inter", sans-serif;
}

/* Product Tabs */
.product-tabs {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 48px;
}

.tab-button {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  font-family: "Poppins", sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f3f4f6;
  color: #6b7280;
}

.tab-button.active {
  background: #2563eb;
  color: white;
}

.tab-button:hover:not(.active) {
  background: #e5e7eb;
  color: #374151;
}

/* Product Grid */
.product-grid {
  margin-bottom: 48px;
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

.products-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 32px;
  max-width: 1200px;
  margin: 0 auto;
}

.product-card {
  background: #f8fafc;
  border-radius: 16px;
  padding: 24px;
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
}

.product-image {
  width: 100%;
  height: 200px;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 20px;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  text-align: left;
}

.product-name {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8px;
  font-family: "Poppins", sans-serif;
}

.product-description {
  font-size: 14px;
  line-height: 1.6;
  color: #6b7280;
  margin-bottom: 16px;
  font-family: "Inter", sans-serif;
}

.product-features {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

.feature {
  background: #dbeafe;
  color: #1e40af;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  font-family: "Inter", sans-serif;
}

.view-details {
  color: #2563eb;
  text-decoration: none;
  font-size: 14px;
  font-weight: 600;
  font-family: "Poppins", sans-serif;
  transition: color 0.3s ease;
}

.view-details:hover {
  color: #1d4ed8;
}

/* Products Footer */
.products-footer {
  text-align: center;
}

.btn-view-all {
  background: #2563eb;
  color: white;
  padding: 14px 32px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 16px;
  font-family: "Poppins", sans-serif;
  transition: all 0.3s ease;
  display: inline-block;
}

.btn-view-all:hover {
  background: #1d4ed8;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
}

/* Responsive Design for Products Section */
@media (max-width: 768px) {
  .products {
    padding: 60px 0;
  }

  .products-header .section-title {
    font-size: 2rem;
  }

  .product-tabs {
    flex-direction: column;
    align-items: center;
    gap: 12px;
  }

  .tab-button {
    width: 200px;
  }

  .products-container {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .product-card {
    padding: 20px;
  }

  .product-image {
    height: 180px;
  }
}
