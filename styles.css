* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: "Open Sans", -apple-system, system-ui, -apple-system,
    BlinkMacSystemFont, sans-serif;
  line-height: 1.6;
  color: #2b2b2b;
  background-color: #faf7f2;
  overflow-x: hidden;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Poppins", sans-serif;
  font-weight: 700;
  line-height: 1.2;
  color: #2b2b2b;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-tag {
  display: inline-block;
  background: linear-gradient(135deg, #004aad 0%, #3cb878 100%);
  color: white;
  padding: 8px 20px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 16px;
  font-family: "Merriweather Sans", sans-serif;
}

.section-header h2 {
  font-size: 2.75rem;
  color: #2b2b2b;
  margin-bottom: 16px;
  font-weight: 700;
}

.section-description {
  font-size: 1.125rem;
  color: #2b2b2b;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.7;
}

.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: #ffffff;
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
  z-index: 1000;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar.scrolled {
  background: #ffffff;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-logo {
  display: flex;
  align-items: center;
  gap: 8px;
}
.logo-icon {
  width: 32px;
  height: 32px;
  background: #2563eb;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 16px;
}

.logo-text {
  font-family: "Poppins", sans-serif;
  font-size: 1.4rem;
  font-weight: 600;
  display: flex;
  flex-direction: column;
  line-height: 1.1;
}

.logo-avaya {
  color: #2563eb;
  font-weight: 800;
}

.logo-exports {
  color: #374151;
  font-weight: 500;
  font-size: 0.9em;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 32px;
  align-items: center;
}

.nav-menu a {
  text-decoration: none;
  color: #374151;
  font-weight: 500;
  font-size: 15px;
  transition: all 0.3s ease;
  position: relative;
}

.nav-menu a:hover {
  color: #2563eb;
}

.nav-menu a::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: #2563eb;
  transition: width 0.3s ease;
}

.nav-menu a:hover::after {
  width: 100%;
}

.nav-cta-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
}

.btn-distributor,
.btn-shop {
  padding: 5px 10px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  font-family: "Poppins", sans-serif;
  margin: 10px;
}

.btn-distributor {
  background: #10b981;
  color: white;
  border-color: #10b981;
}

.btn-distributor:hover {
  background: #059669;
  border-color: #059669;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.btn-shop {
  background: transparent;
  color: #2563eb;
  border-color: #2563eb;
}

.btn-shop:hover {
  background: #2563eb;
  color: white;
  border-color: #2563eb;
}

.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 4px;
}

.mobile-menu-toggle span {
  width: 25px;
  height: 3px;
  background: white;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.mobile-menu-toggle.active span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-toggle.active span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* Hero Section */
.hero {
  position: relative;
  margin-top: 80px;
  min-height: 65vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-image: url("./image/Milk and Dairy.jpg");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: -2;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(37, 99, 235, 0.05) 0%,
    rgba(59, 130, 246, 0.1) 100%
  );
  z-index: -1;
}

.hero-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(
      circle at 25% 25%,
      rgba(37, 99, 235, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 75% 75%,
      rgba(59, 130, 246, 0.08) 0%,
      transparent 50%
    );
  object-fit: cover;
  z-index: -1;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 80px;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #ffc107, #3cb878);
  border-radius: 30px;
  padding: 12px 20px;
  box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 24px;
  color: white;
  font-family: "Merriweather Sans", sans-serif;
}

.hero-title {
  font-size: 4rem;
  font-weight: 800;
  color: white;
  line-height: 1.2;
  margin-bottom: 20px;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: whitesmoke;
  line-height: 1.6;
  margin-bottom: 40px;
  max-width: 500px;
}

.hero-actions {
  display: flex;
  gap: 20px;
  margin-bottom: 50px;
}

.btn-primary,
.btn-secondary {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 16px 32px;
  border-radius: 12px;
  text-decoration: none;
  font-weight: 600;
  font-size: 15px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  font-family: "Merriweather Sans", sans-serif;
}

.btn-primary {
  background: #3cb878;
  color: white;
  box-shadow: 0 8px 25px rgba(60, 184, 120, 0.3);
}

.btn-primary:hover {
  background: #ffc107;
  color: #2b2b2b;
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(255, 193, 7, 0.4);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.9);
  color: #2b2b2b;
  border-color: rgba(255, 255, 255, 0.9);
}

.btn-secondary:hover {
  background: #3cb878;
  color: white;
  border-color: #3cb878;
  transform: translateY(-2px);
}

/* Our Story Section */
.our-story {
  padding: 50px 0;
  background: #f8fafc;
}

.our-story-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  align-items: center;
  margin-bottom: 40px;
}

.our-story-text {
  max-width: 500px;
}

.section-badge {
  display: inline-block;
  background: #10b981;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 24px;
  font-family: "Poppins", sans-serif;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.2;
  margin-bottom: 24px;
  font-family: "Poppins", sans-serif;
}

.section-description {
  font-size: 16px;
  line-height: 1.7;
  color: #6b7280;
  margin-bottom: 20px;
  font-family: "Inter", sans-serif;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32px;
  margin-top: 40px;
}

.stat-item {
  text-align: center;
  position: relative;
}

.stat-item:not(:last-child)::after {
  content: "";
  position: absolute;
  right: -16px;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 60px;
  background: #d1d5db;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2563eb;
  margin-bottom: 8px;
  font-family: "Poppins", sans-serif;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
  font-family: "Inter", sans-serif;
}

.our-story-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.world-map {
  width: 100%;
  max-width: 700px;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Exclusive Distributor Section */
.exclusive-distributor {
  background: #2563eb;
  border-radius: 0;
  padding: 30px 0;
  color: white;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  position: relative;
}

.exclusive-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.exclusive-text h3 {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 8px;
  font-family: "Poppins", sans-serif;
  color: white;
}

.exclusive-text p {
  font-size: 16px;
  color: white;
  opacity: 0.9;
  font-family: "Inter", sans-serif;
}

.official-badge {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.1);
  padding: 16px 24px;
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.badge-icon {
  font-size: 24px;
  color: #10b981;
}

.badge-text {
  display: flex;
  flex-direction: column;
}

.badge-title {
  font-size: 14px;
  font-weight: 600;
  font-family: "Poppins", sans-serif;
}

.badge-subtitle {
  font-size: 12px;
  opacity: 0.8;
  font-family: "Inter", sans-serif;
}

/* Responsive Design for Our Story Section */
@media (max-width: 768px) {
  .our-story {
    padding: 40px 0;
  }

  .our-story-content {
    grid-template-columns: 1fr;
    gap: 30px;
    text-align: center;
    margin-bottom: 30px;
  }

  .section-title {
    font-size: 1.25rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 24px;
    margin-top: 32px;
  }

  .stat-item:not(:last-child)::after {
    display: none;
  }

  .exclusive-content {
    flex-direction: column;
    gap: 24px;
    text-align: center;
  }

  .exclusive-distributor {
    padding: 24px 0;
  }

  .official-badge {
    align-self: center;
  }
}

/* Products Section */
.products {
  padding: 80px 0;
  background: #ffffff;
}

.products-header {
  text-align: center;
  margin-bottom: 48px;
}

.products-header .section-badge {
  display: inline-block;
  background: #10b981;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 24px;
  font-family: "Poppins", sans-serif;
}

.products-header .section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.2;
  margin-bottom: 16px;
  font-family: "Poppins", sans-serif;
}

.products-header .section-description {
  font-size: 16px;
  line-height: 1.7;
  color: #6b7280;
  max-width: 600px;
  margin: 0 auto;
  font-family: "Inter", sans-serif;
}

/* Product Tabs */
.product-tabs {
  display: flex;
  justify-content: center;
  gap: 0;
  margin-bottom: 48px;
  background: #f3f4f6;
  border-radius: 25px;
  padding: 4px;
  width: fit-content;
  margin-left: auto;
  margin-right: auto;
}

.tab-button {
  padding: 12px 24px;
  border: none;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  font-family: "Poppins", sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  background: transparent;
  color: #6b7280;
}

.tab-button.active {
  background: #2563eb;
  color: white;
}

.tab-button:hover:not(.active) {
  background: #e5e7eb;
  color: #374151;
}

/* Product Grid */
.product-grid {
  margin-bottom: 48px;
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

.products-container {
  display: flex;
  justify-content: center;
  gap: 24px;
  max-width: 1200px;
  margin: 0 auto;
  flex-wrap: wrap;
}

/* Nandini Dairy Products - 4 columns */
#nandini .products-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  max-width: 1000px;
}

/* Makhana Products - Single centered card */
#makhana .products-container {
  display: flex;
  justify-content: center;
  max-width: 250px;
}

.product-card {
  background: #f8fafc;
  border-radius: 16px;
  padding: 20px;
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
  width: 100%;
  max-width: 250px;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
}

.product-image {
  width: 100%;
  height: 150px;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 16px;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  text-align: left;
}

.product-name {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8px;
  font-family: "Poppins", sans-serif;
}

.product-description {
  font-size: 14px;
  line-height: 1.6;
  color: #6b7280;
  margin-bottom: 16px;
  font-family: "Inter", sans-serif;
}

.product-features {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

.feature {
  background: #dbeafe;
  color: #1e40af;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  font-family: "Inter", sans-serif;
}

.view-details {
  color: #2563eb;
  text-decoration: none;
  font-size: 14px;
  font-weight: 600;
  font-family: "Poppins", sans-serif;
  transition: color 0.3s ease;
}

.view-details:hover {
  color: #1d4ed8;
}

/* Products Footer */
.products-footer {
  text-align: center;
}

.btn-view-all {
  background: #2563eb;
  color: white;
  padding: 14px 32px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 16px;
  font-family: "Poppins", sans-serif;
  transition: all 0.3s ease;
  display: inline-block;
}

.btn-view-all:hover {
  background: #1d4ed8;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
}

/* Responsive Design for Products Section */
@media (max-width: 768px) {
  .products {
    padding: 60px 0;
  }

  .products-header .section-title {
    font-size: 2rem;
  }

  .product-tabs {
    flex-direction: row;
    width: 90%;
    max-width: 400px;
  }

  .tab-button {
    flex: 1;
    padding: 10px 16px;
    font-size: 13px;
  }

  #nandini .products-container {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    max-width: 100%;
  }

  #makhana .products-container {
    max-width: 250px;
  }

  .product-card {
    padding: 16px;
    max-width: none;
  }

  .product-image {
    height: 120px;
  }
}

@media (max-width: 480px) {
  #nandini .products-container {
    grid-template-columns: 1fr;
    max-width: 280px;
  }

  .product-tabs {
    width: 95%;
  }
}

/* Our Process Section */
.process {
  padding: 80px 0;
  background: #f8fafc;
}

.process-header {
  text-align: center;
  margin-bottom: 64px;
}

.process-header .section-badge {
  display: inline-block;
  background: #10b981;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 24px;
  font-family: "Poppins", sans-serif;
}

.process-header .section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.2;
  margin-bottom: 16px;
  font-family: "Poppins", sans-serif;
}

.process-header .section-description {
  font-size: 16px;
  line-height: 1.7;
  color: #6b7280;
  max-width: 600px;
  margin: 0 auto;
  font-family: "Inter", sans-serif;
}

/* Process Steps */
.process-steps {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 48px;
  max-width: 1200px;
  margin: 0 auto 80px auto;
}

.process-step {
  background: #ffffff;
  border-radius: 16px;
  padding: 32px 24px;
  text-align: center;
  position: relative;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.process-step:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
}

.step-number {
  width: 48px;
  height: 48px;
  background: #2563eb;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 700;
  font-family: "Poppins", sans-serif;
  margin: 0 auto 24px auto;
}

.step-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 16px;
  font-family: "Poppins", sans-serif;
}

.step-description {
  font-size: 14px;
  line-height: 1.6;
  color: #6b7280;
  margin-bottom: 24px;
  font-family: "Inter", sans-serif;
}

.step-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 64px;
  height: 64px;
  background: #f1f5f9;
  border-radius: 50%;
  margin: 0 auto;
}

/* Partnership Section */
.partnership-section {
  background: #2563eb;
  border-radius: 20px;
  overflow: hidden;
  max-width: 1200px;
  margin: 0 auto;
}

.partnership-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: center;
  min-height: 400px;
}

.partnership-text {
  padding: 48px;
  color: white;
}

.partnership-title {
  font-size: 2rem;
  font-weight: 700;
  color: white;
  margin-bottom: 16px;
  font-family: "Poppins", sans-serif;
  line-height: 1.2;
}

.partnership-description {
  font-size: 16px;
  line-height: 1.7;
  color: #dbeafe;
  margin-bottom: 32px;
  font-family: "Inter", sans-serif;
}

.partnership-benefits {
  list-style: none;
  padding: 0;
  margin: 0 0 32px 0;
}

.partnership-benefits li {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 16px;
  color: white;
  font-family: "Inter", sans-serif;
}

.benefit-icon {
  color: #fbbf24;
  margin-right: 12px;
  font-size: 12px;
}

.btn-partnership {
  background: white;
  color: #2563eb;
  padding: 14px 32px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 16px;
  font-family: "Poppins", sans-serif;
  transition: all 0.3s ease;
  display: inline-block;
}

.btn-partnership:hover {
  background: #f1f5f9;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
}

.partnership-image {
  height: 400px;
  overflow: hidden;
}

.partnership-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Quality & Trust Section */
.quality-trust-section {
  padding: 100px 0;
  background: white;
}

.certifications-section {
  margin-bottom: 60px;
}

.certifications-section h3 {
  font-size: 1.5rem;
  color: #2b2b2b;
  margin-bottom: 30px;
  font-weight: 700;
  text-align: center;
}

.cert-grid-horizontal {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
}

.cert-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 12px;
  padding: 24px 16px;
  background: #faf7f2;
  border-radius: 16px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease;
  border-top: 4px solid #3cb878;
}

.cert-item:hover {
  transform: translateY(-6px);
  box-shadow: 0 8px 25px rgba(60, 184, 120, 0.15);
}

.cert-icon {
  width: 60px;
  height: 60px;
  /* background: linear-gradient(135deg, #004aad, #3cb878); */
  background: #3cb878;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.cert-info h4 {
  font-size: 1rem;
  color: #2b2b2b;
  margin-bottom: 4px;
  font-weight: 600;
}

.cert-info p {
  color: #2b2b2b;
  font-size: 12px;
}

.testimonials-section-horizontal h3 {
  font-size: 1.5rem;
  color: #2b2b2b;
  margin-bottom: 30px;
  font-weight: 700;
  text-align: center;
}

.testimonials-grid-horizontal {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 32px;
}

.testimonial-card {
  background: #faf7f2;
  padding: 30px;
  border-radius: 16px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
  border-left: 4px solid #004aad;
}

.testimonial-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 12px 35px rgba(0, 74, 173, 0.15);
}

.testimonial-content {
  margin-bottom: 20px;
}

.quote-icon {
  color: #004aad;
  font-size: 1.5rem;
  margin-bottom: 12px;
}

.testimonial-content p {
  font-style: italic;
  font-size: 1rem;
  color: #2b2b2b;
  line-height: 1.7;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 16px;
}

.author-avatar {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #004aad, #3cb878);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.author-info strong {
  color: #2b2b2b;
  font-size: 1rem;
  display: block;
  margin-bottom: 4px;
}

.author-info span {
  color: #2b2b2b;
  font-size: 14px;
  display: block;
  margin-bottom: 8px;
}

.rating {
  display: flex;
  gap: 2px;
}

.rating i {
  color: #ffc107;
  font-size: 14px;
}

/* Responsive Design for Process Section */
@media (max-width: 768px) {
  .process {
    padding: 60px 0;
  }

  .process-header .section-title {
    font-size: 2rem;
  }

  .process-steps {
    grid-template-columns: 1fr;
    gap: 32px;
    margin-bottom: 60px;
  }

  .process-step {
    padding: 24px 20px;
  }

  .partnership-content {
    grid-template-columns: 1fr;
  }

  .partnership-text {
    padding: 32px 24px;
  }

  .partnership-title {
    font-size: 1.5rem;
  }

  .partnership-image {
    height: 250px;
  }
}

@media (max-width: 480px) {
  .process-header {
    margin-bottom: 48px;
  }

  .process-steps {
    gap: 24px;
    margin-bottom: 48px;
  }

  .partnership-text {
    padding: 24px 20px;
  }

  .partnership-benefits li {
    font-size: 14px;
  }
}

/* Get In Touch Section */
.get-in-touch {
  padding: 80px 0;
  background: #f8fafc;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: start;
}

.contact-info .section-badge {
  display: inline-block;
  background: #10b981;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 24px;
  font-family: "Poppins", sans-serif;
}

.contact-info .section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.2;
  margin-bottom: 16px;
  font-family: "Poppins", sans-serif;
}

.contact-info .section-description {
  font-size: 16px;
  line-height: 1.7;
  color: #6b7280;
  margin-bottom: 48px;
  font-family: "Inter", sans-serif;
}

.contact-details {
  margin-bottom: 32px;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 32px;
}

.contact-icon {
  width: 48px;
  height: 48px;
  background: #2563eb;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  flex-shrink: 0;
}

.contact-icon i {
  color: white;
  font-size: 18px;
}

.contact-text h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
  font-family: "Poppins", sans-serif;
}

.contact-text p {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
  font-family: "Inter", sans-serif;
}

.social-links {
  display: flex;
  gap: 12px;
}

.social-link {
  width: 40px;
  height: 40px;
  background: #2563eb;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
}

.social-link:hover {
  background: #1d4ed8;
  transform: translateY(-2px);
}

.social-link i {
  font-size: 16px;
}

/* Contact Form */
.contact-form-container {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
}

.contact-form h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 24px;
  font-family: "Poppins", sans-serif;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  font-family: "Inter", sans-serif;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  font-family: "Inter", sans-serif;
  transition: border-color 0.3s ease;
  background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.btn-submit {
  width: 100%;
  background: #2563eb;
  color: white;
  padding: 14px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  font-family: "Poppins", sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-submit:hover {
  background: #1d4ed8;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
}

/* Footer Section */
.footer {
  background: #1f2937;
  color: #d1d5db;
  padding: 60px 0 0 0;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.5fr;
  gap: 48px;
  margin-bottom: 48px;
}

.footer-column h3,
.footer-column h4 {
  color: white;
  font-family: "Poppins", sans-serif;
  font-weight: 700;
  margin-bottom: 24px;
}

.footer-column h3 {
  font-size: 1.5rem;
}

.footer-column h4 {
  font-size: 1.125rem;
}

.footer-description {
  font-size: 14px;
  line-height: 1.7;
  color: #9ca3af;
  margin-bottom: 24px;
  font-family: "Inter", sans-serif;
}

.footer-social {
  display: flex;
  gap: 12px;
}

.footer-social .social-link {
  width: 36px;
  height: 36px;
  background: #374151;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #d1d5db;
  text-decoration: none;
  transition: all 0.3s ease;
}

.footer-social .social-link:hover {
  background: #2563eb;
  color: white;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 12px;
}

.footer-links a {
  color: #9ca3af;
  text-decoration: none;
  font-size: 14px;
  font-family: "Inter", sans-serif;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: white;
}

.footer-contact p {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  font-size: 14px;
  color: #9ca3af;
  font-family: "Inter", sans-serif;
}

.footer-contact i {
  margin-right: 8px;
  margin-top: 2px;
  color: #2563eb;
  width: 16px;
}

.footer-bottom {
  border-top: 1px solid #374151;
  padding: 24px 0;
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-bottom p {
  font-size: 14px;
  color: #9ca3af;
  margin: 0;
  font-family: "Inter", sans-serif;
}

.footer-bottom-links {
  display: flex;
  gap: 24px;
}

.footer-bottom-links a {
  color: #9ca3af;
  text-decoration: none;
  font-size: 14px;
  font-family: "Inter", sans-serif;
  transition: color 0.3s ease;
}

.footer-bottom-links a:hover {
  color: white;
}

/* Responsive Design for Get In Touch and Footer */
@media (max-width: 768px) {
  .get-in-touch {
    padding: 60px 0;
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: 48px;
  }

  .contact-info .section-title {
    font-size: 2rem;
  }

  .contact-form-container {
    padding: 24px;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 0;
  }

  .footer-content {
    grid-template-columns: 1fr 1fr;
    gap: 32px;
  }

  .footer-bottom-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .footer-bottom-links {
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .contact-info .section-title {
    font-size: 1.75rem;
  }

  .contact-item {
    margin-bottom: 24px;
  }

  .contact-icon {
    width: 40px;
    height: 40px;
  }

  .contact-form-container {
    padding: 20px;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .social-links {
    justify-content: center;
  }

  .footer-social {
    justify-content: center;
  }
}
