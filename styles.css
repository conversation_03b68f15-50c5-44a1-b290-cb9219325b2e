* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: "Open Sans", -apple-system, system-ui, -apple-system,
    BlinkMacSystemFont, sans-serif;
  line-height: 1.6;
  color: #2b2b2b;
  background-color: #faf7f2;
  overflow-x: hidden;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Poppins", sans-serif;
  font-weight: 700;
  line-height: 1.2;
  color: #2b2b2b;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-tag {
  display: inline-block;
  background: linear-gradient(135deg, #004aad 0%, #3cb878 100%);
  color: white;
  padding: 8px 20px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 16px;
  font-family: "Merriweather Sans", sans-serif;
}

.section-header h2 {
  font-size: 2.75rem;
  color: #2b2b2b;
  margin-bottom: 16px;
  font-weight: 700;
}

.section-description {
  font-size: 1.125rem;
  color: #2b2b2b;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.7;
}

.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: #004aad;
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
  z-index: 1000;
  transition: all 0.3s ease;
}

.navbar.scrolled {
  background: #004aad;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-logo {
  display: flex;
  align-items: center;
  gap: 12px;
}
.logo-icon {
  width: 40px;
  height: 40px;
}

.logo-text {
  font-family: "Poppins", sans-serif;
  font-size: 1.5rem;
  font-weight: 700;
  color: #fff;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 32px;
  align-items: center;
}

.nav-menu a {
  text-decoration: none;
  color: white;
  font-weight: 500;
  font-size: 15px;
  transition: all 0.3s ease;
  position: relative;
}

.nav-menu a:hover {
  color: #ffc107;
}

.nav-menu a::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: #ffc107;
  transition: width 0.3s ease;
}

.nav-menu a:hover::after {
  width: 100%;
}

.nav-cta-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
}

.btn-distributor,
.btn-shop {
  padding: 10px 20px;
  border-radius: 10px;
  text-decoration: none;
  font-weight: 600;
  font-size: 15px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  font-family: "Merriweather Sans", sans-serif;
}

.btn-distributor {
  background: #3cb878;
  color: white;
  border-color: #3cb878;
}

.btn-distributor:hover {
  background: #ffc107;
  border-color: #ffc107;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
}

.btn-shop {
  background: transparent;
  color: white;
  border-color: white;
}

.btn-shop:hover {
  background: #ffc107;
  color: #2b2b2b;
  border-color: #ffc107;
}

.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 4px;
}

.mobile-menu-toggle span {
  width: 25px;
  height: 3px;
  background: white;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.mobile-menu-toggle.active span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-toggle.active span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* Hero Section */
.hero {
  position: relative;
  margin-top: 80px;
  min-height: 90vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-image: url("./image/Milk and Dairy.jpg");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: -2;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(37, 99, 235, 0.05) 0%,
    rgba(59, 130, 246, 0.1) 100%
  );
  z-index: -1;
}

.hero-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(
      circle at 25% 25%,
      rgba(37, 99, 235, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 75% 75%,
      rgba(59, 130, 246, 0.08) 0%,
      transparent 50%
    );
  object-fit: cover;
  z-index: -1;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 80px;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #ffc107, #3cb878);
  border-radius: 30px;
  padding: 12px 20px;
  box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 24px;
  color: white;
  font-family: "Merriweather Sans", sans-serif;
}

.hero-title {
  font-size: 4rem;
  font-weight: 800;
  color: white;
  line-height: 1.2;
  margin-bottom: 20px;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: whitesmoke;
  line-height: 1.6;
  margin-bottom: 40px;
  max-width: 500px;
}

.hero-actions {
  display: flex;
  gap: 20px;
  margin-bottom: 50px;
}

.btn-primary,
.btn-secondary {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 16px 32px;
  border-radius: 12px;
  text-decoration: none;
  font-weight: 600;
  font-size: 15px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  font-family: "Merriweather Sans", sans-serif;
}

.btn-primary {
  background: #3cb878;
  color: white;
  box-shadow: 0 8px 25px rgba(60, 184, 120, 0.3);
}

.btn-primary:hover {
  background: #ffc107;
  color: #2b2b2b;
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(255, 193, 7, 0.4);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.9);
  color: #2b2b2b;
  border-color: rgba(255, 255, 255, 0.9);
}

.btn-secondary:hover {
  background: #3cb878;
  color: white;
  border-color: #3cb878;
  transform: translateY(-2px);
}
