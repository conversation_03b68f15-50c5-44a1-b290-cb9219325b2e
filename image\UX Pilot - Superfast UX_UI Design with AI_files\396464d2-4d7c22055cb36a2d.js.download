"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9119],{9069:function(e,t,n){n.d(t,{Li:function(){return replayIntegration$1}});var r,a,o,i,s,l,c,u,d,p,h,f,m,v,y=n(45610),g=n(86335),k=n(40494),S=n(55688),_=n(41369),b=n(43654),w=n(9),I=n(44313),E=n(86421),C=n(21889),T=n(50044),M=n(68463),x=n(83518),R=n(40109),A=n(89344),O=n(68017),D=n(5906),N=n(18041),L=n(30087),B=n(20607),F=n(43162),P=n(95550),W=n(55212),z=n(66630),U=n(1945),H=n(28718),j=n(16183),q=n(10927),$=n(28654),J=n(54495),K=n(93217),V=n(10469),Y=n(94168),X=n(54241),G=n(44036),Q=n(55568),Z=F.n2,ee="sentryReplaySession",et="Unable to send Replay";function _optionalChain$5(e){for(var t=void 0,n=e[0],r=1;r<e.length;){var a,o=e[r],i=e[r+1];if(r+=2,("optionalAccess"===o||"optionalCall"===o)&&null==n)return;"access"===o||"optionalAccess"===o?(t=n,n=i(n)):("call"===o||"optionalCall"===o)&&(n=i(function(){for(var e=arguments.length,r=Array(e),o=0;o<e;o++)r[o]=arguments[o];return(a=n).call.apply(a,[t].concat((0,I._)(r)))}),t=void 0)}return n}function isShadowRoot(e){var t=_optionalChain$5([e,"optionalAccess",function(e){return e.host}]);return _optionalChain$5([t,"optionalAccess",function(e){return e.shadowRoot}])===e}function isNativeShadowDom(e){return"[object ShadowRoot]"===Object.prototype.toString.call(e)}function stringifyStylesheet(e){try{var t,n=e.rules||e.cssRules;return n?((t=Array.from(n,stringifyRule).join("")).includes(" background-clip: text;")&&!t.includes(" -webkit-background-clip: text;")&&(t=t.replace(" background-clip: text;"," -webkit-background-clip: text; background-clip: text;")),t):null}catch(e){return null}}function stringifyRule(e){var t;if("styleSheet"in e)try{t=stringifyStylesheet(e.styleSheet)||function(e){var t=e.cssText;if(t.split('"').length<3)return t;var n=["@import","url(".concat(JSON.stringify(e.href),")")];return""===e.layerName?n.push("layer"):e.layerName&&n.push("layer(".concat(e.layerName,")")),e.supportsText&&n.push("supports(".concat(e.supportsText,")")),e.media.length&&n.push(e.media.mediaText),n.join(" ")+";"}(e)}catch(e){}else if("selectorText"in e&&e.selectorText.includes(":"))return e.cssText.replace(/(\[(?:[\w-]+)[^\\])(:(?:[\w-]+)\])/gm,"$1\\$2");return t||e.cssText}(r=c||(c={}))[r.Document=0]="Document",r[r.DocumentType=1]="DocumentType",r[r.Element=2]="Element",r[r.Text=3]="Text",r[r.CDATA=4]="CDATA",r[r.Comment=5]="Comment";var en=function(){function Mirror(){(0,g._)(this,Mirror),this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}return(0,k._)(Mirror,[{key:"getId",value:function(e){var t;return e&&null!=(t=_optionalChain$5([this,"access",function(e){return e.getMeta},"call",function(t){return t(e)},"optionalAccess",function(e){return e.id}]))?t:-1}},{key:"getNode",value:function(e){return this.idNodeMap.get(e)||null}},{key:"getIds",value:function(){return Array.from(this.idNodeMap.keys())}},{key:"getMeta",value:function(e){return this.nodeMetaMap.get(e)||null}},{key:"removeNodeFromMap",value:function(e){var t=this,n=this.getId(e);this.idNodeMap.delete(n),e.childNodes&&e.childNodes.forEach(function(e){return t.removeNodeFromMap(e)})}},{key:"has",value:function(e){return this.idNodeMap.has(e)}},{key:"hasNode",value:function(e){return this.nodeMetaMap.has(e)}},{key:"add",value:function(e,t){var n=t.id;this.idNodeMap.set(n,e),this.nodeMetaMap.set(e,t)}},{key:"replace",value:function(e,t){var n=this.getNode(e);if(n){var r=this.nodeMetaMap.get(n);r&&this.nodeMetaMap.set(t,r)}this.idNodeMap.set(e,t)}},{key:"reset",value:function(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}}]),Mirror}();function shouldMaskInput(e){var t=e.maskInputOptions,n=e.tagName,r=e.type;return"OPTION"===n&&(n="SELECT"),!!(t[n.toLowerCase()]||r&&t[r]||"password"===r||"INPUT"===n&&!r&&t.text)}function maskInputValue(e){var t=e.isMasked,n=e.element,r=e.value,a=e.maskInputFn,o=r||"";return t?(a&&(o=a(o,n)),"*".repeat(o.length)):o}function toLowerCase(e){return e.toLowerCase()}function toUpperCase(e){return e.toUpperCase()}var er="__rrweb_original__";function getInputType(e){var t=e.type;return e.hasAttribute("data-rr-is-password")?"password":t?toLowerCase(t):null}function getInputValue(e,t,n){return"INPUT"===t&&("radio"===n||"checkbox"===n)?e.getAttribute("value")||"":e.value}var ea=1,eo=RegExp("[^a-z0-9-_:]");function genId(){return ea++}var ei=/url\((?:(')([^']*)'|(")(.*?)"|([^)]*))\)/gm,es=/^(?:[a-z+]+:)?\/\//i,el=/^www\..*/i,ec=/^(data:)([^,]*),(.*)/i;function absoluteToStylesheet(e,t){return(e||"").replace(ei,function(e,n,r,a,o,i){var s=r||o||i,l=n||a||"";if(!s)return e;if(es.test(s)||el.test(s)||ec.test(s))return"url(".concat(l).concat(s).concat(l,")");if("/"===s[0])return"url(".concat(l).concat((t.indexOf("//")>-1?t.split("/").slice(0,3).join("/"):t.split("/")[0]).split("?")[0]+s).concat(l,")");var c=t.split("/"),u=s.split("/");c.pop();var d=!0,p=!1,h=void 0;try{for(var f,m=u[Symbol.iterator]();!(d=(f=m.next()).done);d=!0){var v=f.value;"."!==v&&(".."===v?c.pop():c.push(v))}}catch(e){p=!0,h=e}finally{try{d||null==m.return||m.return()}finally{if(p)throw h}}return"url(".concat(l).concat(c.join("/")).concat(l,")")})}var eu=/^[^ \t\n\r\u000c]+/,ed=/^[, \t\n\r\u000c]+/;function absoluteToDoc(e,t){if(!t||""===t.trim())return t;var n=e.createElement("a");return n.href=t,n.href}function getHref(){var e=document.createElement("a");return e.href="",e.href}function transformAttribute(e,t,n,r,a,o){return r?"src"!==n&&("href"!==n||"use"===t&&"#"===r[0])&&("xlink:href"!==n||"#"===r[0])&&("background"!==n||"table"!==t&&"td"!==t&&"th"!==t)?"srcset"===n?function(e,t){if(""===t.trim())return t;var n=0;function collectCharacters(e){var r,a=e.exec(t.substring(n));return a?(r=a[0],n+=r.length,r):""}for(var r=[];collectCharacters(ed),!(n>=t.length);){var a=collectCharacters(eu);if(","===a.slice(-1))a=absoluteToDoc(e,a.substring(0,a.length-1)),r.push(a);else{var o="";a=absoluteToDoc(e,a);for(var i=!1;;){var s=t.charAt(n);if(""===s){r.push((a+o).trim());break}if(i)")"===s&&(i=!1);else{if(","===s){n+=1,r.push((a+o).trim());break}"("===s&&(i=!0)}o+=s,n+=1}}}return r.join(", ")}(e,r):"style"===n?absoluteToStylesheet(r,getHref()):"object"===t&&"data"===n?absoluteToDoc(e,r):"function"==typeof o?o(n,r,a):r:absoluteToDoc(e,r):r}function ignoreAttribute(e,t,n){return("video"===e||"audio"===e)&&"autoplay"===t}function distanceToMatch(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1/0,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0;return!e||e.nodeType!==e.ELEMENT_NODE||r>n?-1:t(e)?r:distanceToMatch(e.parentNode,t,n,r+1)}function createMatchPredicate(e,t){return function(n){if(null===n)return!1;try{if(e){if("string"==typeof e){if(n.matches(".".concat(e)))return!0}else if(function(e,t){for(var n=e.classList.length;n--;){var r=e.classList[n];if(t.test(r))return!0}return!1}(n,e))return!0}if(t&&n.matches(t))return!0;return!1}catch(e){return!1}}}function needMaskingText(e,t,n,r,a,o){try{var i=e.nodeType===e.ELEMENT_NODE?e:e.parentElement;if(null===i)return!1;if("INPUT"===i.tagName){var s=i.getAttribute("autocomplete");if(["current-password","new-password","cc-number","cc-exp","cc-exp-month","cc-exp-year","cc-csc"].includes(s))return!0}var l=-1,c=-1;if(o){if((c=distanceToMatch(i,createMatchPredicate(r,a)))<0)return!0;l=distanceToMatch(i,createMatchPredicate(t,n),c>=0?c:1/0)}else{if((l=distanceToMatch(i,createMatchPredicate(t,n)))<0)return!1;c=distanceToMatch(i,createMatchPredicate(r,a),l>=0?l:1/0)}return l>=0?!(c>=0)||l<=c:!(c>=0)&&!!o}catch(e){}return!!o}function lowerIfExists(e){return null==e?"":e.toLowerCase()}function serializeNodeWithId(e,t){var n=t.doc,r=t.mirror,a=t.blockClass,o=t.blockSelector,i=t.unblockSelector,s=t.maskAllText,l=t.maskTextClass,p=t.unmaskTextClass,h=t.maskTextSelector,f=t.unmaskTextSelector,m=t.skipChild,v=void 0!==m&&m,y=t.inlineStylesheet,g=void 0===y||y,k=t.maskInputOptions,S=void 0===k?{}:k,_=t.maskAttributeFn,b=t.maskTextFn,w=t.maskInputFn,I=t.slimDOMOptions,E=t.dataURLOptions,C=void 0===E?{}:E,T=t.inlineImages,M=void 0!==T&&T,x=t.recordCanvas,R=void 0!==x&&x,A=t.onSerialize,O=t.onIframeLoad,D=t.iframeLoadTimeout,N=void 0===D?5e3:D,L=t.onStylesheetLoad,B=t.stylesheetLoadTimeout,F=void 0===B?5e3:B,P=t.keepIframeSrcFn,W=void 0===P?function(){return!1}:P,z=t.newlyAddedElement,U=t.preserveWhiteSpace,H=void 0===U||U,j=function(e,t){var n=t.doc,r=t.mirror,a=t.blockClass,o=t.blockSelector,i=t.unblockSelector,s=t.maskAllText,l=t.maskAttributeFn,p=t.maskTextClass,h=t.unmaskTextClass,f=t.maskTextSelector,m=t.unmaskTextSelector,v=t.inlineStylesheet,y=t.maskInputOptions,g=void 0===y?{}:y,k=t.maskTextFn,S=t.maskInputFn,_=t.dataURLOptions,b=t.inlineImages,w=t.recordCanvas,I=t.keepIframeSrcFn,E=t.newlyAddedElement,C=function(e,t){if(t.hasNode(e)){var n=t.getId(e);return 1===n?void 0:n}}(n,r);switch(e.nodeType){case e.DOCUMENT_NODE:if("CSS1Compat"!==e.compatMode)return{type:c.Document,childNodes:[],compatMode:e.compatMode};return{type:c.Document,childNodes:[]};case e.DOCUMENT_TYPE_NODE:return{type:c.DocumentType,name:e.name,publicId:e.publicId,systemId:e.systemId,rootId:C};case e.ELEMENT_NODE:return function(e,t){for(var n,r=t.doc,a=t.blockClass,o=t.blockSelector,i=t.unblockSelector,s=t.inlineStylesheet,l=t.maskInputOptions,p=void 0===l?{}:l,h=t.maskAttributeFn,f=t.maskInputFn,m=t.dataURLOptions,v=void 0===m?{}:m,y=t.inlineImages,g=t.recordCanvas,k=t.keepIframeSrcFn,S=t.newlyAddedElement,_=t.rootId,b=(t.maskAllText,t.maskTextClass),w=t.unmaskTextClass,I=t.maskTextSelector,E=t.unmaskTextSelector,C=function(e,t,n,r){try{if(r&&e.matches(r))return!1;if("string"==typeof t){if(e.classList.contains(t))return!0}else for(var a=e.classList.length;a--;){var o=e.classList[a];if(t.test(o))return!0}if(n)return e.matches(n)}catch(e){}return!1}(e,a,o,i),T=function(e){if(e instanceof HTMLFormElement)return"form";var t=toLowerCase(e.tagName);return eo.test(t)?"div":t}(e),M={},x=e.attributes.length,R=0;R<x;R++){var A=e.attributes[R];A.name&&!ignoreAttribute(T,A.name,A.value)&&(M[A.name]=transformAttribute(r,T,toLowerCase(A.name),A.value,e,h))}if("link"===T&&s){var O=Array.from(r.styleSheets).find(function(t){return t.href===e.href}),D=null;O&&(D=stringifyStylesheet(O)),D&&(delete M.rel,delete M.href,M._cssText=absoluteToStylesheet(D,O.href))}if("style"===T&&e.sheet&&!(e.innerText||e.textContent||"").trim().length){var N=stringifyStylesheet(e.sheet);N&&(M._cssText=absoluteToStylesheet(N,getHref()))}if("input"===T||"textarea"===T||"select"===T||"option"===T){var L=getInputType(e),B=getInputValue(e,toUpperCase(T),L),F=e.checked;if("submit"!==L&&"button"!==L&&B){var P=needMaskingText(e,b,I,w,E,shouldMaskInput({type:L,tagName:toUpperCase(T),maskInputOptions:p}));M.value=maskInputValue({isMasked:P,element:e,value:B,maskInputFn:f})}F&&(M.checked=F)}if("option"===T&&(e.selected&&!p.select?M.selected=!0:delete M.selected),"canvas"===T&&g){if("2d"===e.__context)!function(e){var t=e.getContext("2d");if(!t)return!0;for(var n=0;n<e.width;n+=50)for(var r=0;r<e.height;r+=50){var a=t.getImageData,o=er in a?a[er]:a;if(new Uint32Array(o.call(t,n,r,Math.min(50,e.width-n),Math.min(50,e.height-r)).data.buffer).some(function(e){return 0!==e}))return!1}return!0}(e)&&(M.rr_dataURL=e.toDataURL(v.type,v.quality));else if(!("__context"in e)){var W=e.toDataURL(v.type,v.quality),z=document.createElement("canvas");z.width=e.width,z.height=e.height,W!==z.toDataURL(v.type,v.quality)&&(M.rr_dataURL=W)}}if("img"===T&&y){u||(d=(u=r.createElement("canvas")).getContext("2d"));var U=e.crossOrigin;e.crossOrigin="anonymous";var recordInlineImage=function(){e.removeEventListener("load",recordInlineImage);try{u.width=e.naturalWidth,u.height=e.naturalHeight,d.drawImage(e,0,0),M.rr_dataURL=u.toDataURL(v.type,v.quality)}catch(t){console.warn("Cannot inline img src=".concat(e.currentSrc,"! Error: ").concat(t))}U?M.crossOrigin=U:e.removeAttribute("crossorigin")};e.complete&&0!==e.naturalWidth?recordInlineImage():e.addEventListener("load",recordInlineImage)}if(("audio"===T||"video"===T)&&(M.rr_mediaState=e.paused?"paused":"played",M.rr_mediaCurrentTime=e.currentTime),!(void 0!==S&&S)&&(e.scrollLeft&&(M.rr_scrollLeft=e.scrollLeft),e.scrollTop&&(M.rr_scrollTop=e.scrollTop)),C){var H=e.getBoundingClientRect(),j=H.width,q=H.height;M={class:M.class,rr_width:"".concat(j,"px"),rr_height:"".concat(q,"px")}}"iframe"!==T||k(M.src)||(e.contentDocument||(M.rr_src=M.src),delete M.src);try{customElements.get(T)&&(n=!0)}catch(e){}return{type:c.Element,tagName:T,attributes:M,childNodes:[],isSVG:!!("svg"===e.tagName||e.ownerSVGElement)||void 0,needBlock:C,rootId:_,isCustom:n}}(e,{doc:n,blockClass:a,blockSelector:o,unblockSelector:i,inlineStylesheet:v,maskAttributeFn:l,maskInputOptions:g,maskInputFn:S,dataURLOptions:void 0===_?{}:_,inlineImages:b,recordCanvas:w,keepIframeSrcFn:I,newlyAddedElement:void 0!==E&&E,rootId:C,maskAllText:s,maskTextClass:p,unmaskTextClass:h,maskTextSelector:f,unmaskTextSelector:m});case e.TEXT_NODE:return function(e,t){var n=t.maskAllText,r=t.maskTextClass,a=t.unmaskTextClass,o=t.maskTextSelector,i=t.unmaskTextSelector,s=t.maskTextFn,l=t.maskInputOptions,u=t.maskInputFn,d=t.rootId,p=e.parentNode&&e.parentNode.tagName,h=e.textContent,f="STYLE"===p||void 0,m="SCRIPT"===p||void 0,v="TEXTAREA"===p||void 0;if(f&&h){try{e.nextSibling||e.previousSibling||_optionalChain$5([e,"access",function(e){return e.parentNode},"access",function(e){return e.sheet},"optionalAccess",function(e){return e.cssRules}])&&(h=stringifyStylesheet(e.parentNode.sheet))}catch(t){console.warn("Cannot get CSS styles from text's parentNode. Error: ".concat(t),e)}h=absoluteToStylesheet(h,getHref())}m&&(h="SCRIPT_PLACEHOLDER");var y=needMaskingText(e,r,o,a,i,n);return f||m||v||!h||!y||(h=s?s(h,e.parentElement):h.replace(/[\S]/g,"*")),v&&h&&(l.textarea||y)&&(h=u?u(h,e.parentNode):h.replace(/[\S]/g,"*")),"OPTION"===p&&h&&(h=maskInputValue({isMasked:needMaskingText(e,r,o,a,i,shouldMaskInput({type:null,tagName:p,maskInputOptions:l})),element:e,value:h,maskInputFn:u})),{type:c.Text,textContent:h||"",isStyle:f,rootId:d}}(e,{maskAllText:s,maskTextClass:p,unmaskTextClass:h,maskTextSelector:f,unmaskTextSelector:m,maskTextFn:k,maskInputOptions:g,maskInputFn:S,rootId:C});case e.CDATA_SECTION_NODE:return{type:c.CDATA,textContent:"",rootId:C};case e.COMMENT_NODE:return{type:c.Comment,textContent:e.textContent||"",rootId:C};default:return!1}}(e,{doc:n,mirror:r,blockClass:a,blockSelector:o,maskAllText:s,unblockSelector:i,maskTextClass:l,unmaskTextClass:p,maskTextSelector:h,unmaskTextSelector:f,inlineStylesheet:g,maskInputOptions:S,maskAttributeFn:_,maskTextFn:b,maskInputFn:w,dataURLOptions:C,inlineImages:M,recordCanvas:R,keepIframeSrcFn:W,newlyAddedElement:void 0!==z&&z});if(!j)return console.warn(e,"not serialized"),null;G=r.hasNode(e)?r.getId(e):!function(e,t){if(t.comment&&e.type===c.Comment)return!0;if(e.type===c.Element){if(t.script&&("script"===e.tagName||"link"===e.tagName&&("preload"===e.attributes.rel||"modulepreload"===e.attributes.rel)&&"script"===e.attributes.as||"link"===e.tagName&&"prefetch"===e.attributes.rel&&"string"==typeof e.attributes.href&&e.attributes.href.endsWith(".js"))||t.headFavicon&&("link"===e.tagName&&"shortcut icon"===e.attributes.rel||"meta"===e.tagName&&(lowerIfExists(e.attributes.name).match(/^msapplication-tile(image|color)$/)||"application-name"===lowerIfExists(e.attributes.name)||"icon"===lowerIfExists(e.attributes.rel)||"apple-touch-icon"===lowerIfExists(e.attributes.rel)||"shortcut icon"===lowerIfExists(e.attributes.rel))))return!0;if("meta"===e.tagName){if(t.headMetaDescKeywords&&lowerIfExists(e.attributes.name).match(/^description|keywords$/)||t.headMetaSocial&&(lowerIfExists(e.attributes.property).match(/^(og|twitter|fb):/)||lowerIfExists(e.attributes.name).match(/^(og|twitter):/)||"pinterest"===lowerIfExists(e.attributes.name)))return!0;if(t.headMetaRobots&&("robots"===lowerIfExists(e.attributes.name)||"googlebot"===lowerIfExists(e.attributes.name)||"bingbot"===lowerIfExists(e.attributes.name)))return!0;if(t.headMetaHttpEquiv&&void 0!==e.attributes["http-equiv"])return!0;else if(t.headMetaAuthorship&&("author"===lowerIfExists(e.attributes.name)||"generator"===lowerIfExists(e.attributes.name)||"framework"===lowerIfExists(e.attributes.name)||"publisher"===lowerIfExists(e.attributes.name)||"progid"===lowerIfExists(e.attributes.name)||lowerIfExists(e.attributes.property).match(/^article:/)||lowerIfExists(e.attributes.property).match(/^product:/)))return!0;else if(t.headMetaVerification&&("google-site-verification"===lowerIfExists(e.attributes.name)||"yandex-verification"===lowerIfExists(e.attributes.name)||"csrf-token"===lowerIfExists(e.attributes.name)||"p:domain_verify"===lowerIfExists(e.attributes.name)||"verify-v1"===lowerIfExists(e.attributes.name)||"verification"===lowerIfExists(e.attributes.name)||"shopify-checkout-api-token"===lowerIfExists(e.attributes.name)))return!0}}return!1}(j,I)&&(H||j.type!==c.Text||j.isStyle||j.textContent.replace(/^\s+|\s+$/gm,"").length)?genId():-2;var q=Object.assign(j,{id:G});if(r.add(e,q),-2===G)return null;A&&A(e);var $=!v;if(q.type===c.Element){$=$&&!q.needBlock,delete q.needBlock;var J=e.shadowRoot;J&&isNativeShadowDom(J)&&(q.isShadowHost=!0)}if((q.type===c.Document||q.type===c.Element)&&$){I.headWhitespace&&q.type===c.Element&&"head"===q.tagName&&(H=!1);var K={doc:n,mirror:r,blockClass:a,blockSelector:o,maskAllText:s,unblockSelector:i,maskTextClass:l,unmaskTextClass:p,maskTextSelector:h,unmaskTextSelector:f,skipChild:v,inlineStylesheet:g,maskInputOptions:S,maskAttributeFn:_,maskTextFn:b,maskInputFn:w,slimDOMOptions:I,dataURLOptions:C,inlineImages:M,recordCanvas:R,preserveWhiteSpace:H,onSerialize:A,onIframeLoad:O,iframeLoadTimeout:N,onStylesheetLoad:L,stylesheetLoadTimeout:F,keepIframeSrcFn:W},V=!0,Y=!1,X=void 0;try{for(var G,Q,Z=Array.from(e.childNodes)[Symbol.iterator]();!(V=(Q=Z.next()).done);V=!0){var ee=Q.value,et=serializeNodeWithId(ee,K);et&&q.childNodes.push(et)}}catch(e){Y=!0,X=e}finally{try{V||null==Z.return||Z.return()}finally{if(Y)throw X}}if(e.nodeType===e.ELEMENT_NODE&&e.shadowRoot){var en=!0,ea=!1,ei=void 0;try{for(var es,el=Array.from(e.shadowRoot.childNodes)[Symbol.iterator]();!(en=(es=el.next()).done);en=!0){var ec=es.value,eu=serializeNodeWithId(ec,K);eu&&(isNativeShadowDom(e.shadowRoot)&&(eu.isShadow=!0),q.childNodes.push(eu))}}catch(e){ea=!0,ei=e}finally{try{en||null==el.return||el.return()}finally{if(ea)throw ei}}}}return e.parentNode&&isShadowRoot(e.parentNode)&&isNativeShadowDom(e.parentNode)&&(q.isShadow=!0),q.type===c.Element&&"iframe"===q.tagName&&function(e,t,n){var r,a=e.contentWindow;if(a){var o=!1;try{r=a.document.readyState}catch(e){return}if("complete"!==r){var i=setTimeout(function(){o||(t(),o=!0)},n);e.addEventListener("load",function(){clearTimeout(i),o=!0,t()});return}var s="about:blank";if(a.location.href!==s||e.src===s||""===e.src)return setTimeout(t,0),e.addEventListener("load",t);e.addEventListener("load",t)}}(e,function(){var t=e.contentDocument;if(t&&O){var n=serializeNodeWithId(t,{doc:t,mirror:r,blockClass:a,blockSelector:o,unblockSelector:i,maskAllText:s,maskTextClass:l,unmaskTextClass:p,maskTextSelector:h,unmaskTextSelector:f,skipChild:!1,inlineStylesheet:g,maskInputOptions:S,maskAttributeFn:_,maskTextFn:b,maskInputFn:w,slimDOMOptions:I,dataURLOptions:C,inlineImages:M,recordCanvas:R,preserveWhiteSpace:H,onSerialize:A,onIframeLoad:O,iframeLoadTimeout:N,onStylesheetLoad:L,stylesheetLoadTimeout:F,keepIframeSrcFn:W});n&&O(e,n)}},N),q.type===c.Element&&"link"===q.tagName&&"stylesheet"===q.attributes.rel&&function(e,t,n){var r,a=!1;try{r=e.sheet}catch(e){return}if(!r){var o=setTimeout(function(){a||(t(),a=!0)},n);e.addEventListener("load",function(){clearTimeout(o),a=!0,t()})}}(e,function(){if(L){var t=serializeNodeWithId(e,{doc:n,mirror:r,blockClass:a,blockSelector:o,unblockSelector:i,maskAllText:s,maskTextClass:l,unmaskTextClass:p,maskTextSelector:h,unmaskTextSelector:f,skipChild:!1,inlineStylesheet:g,maskInputOptions:S,maskAttributeFn:_,maskTextFn:b,maskInputFn:w,slimDOMOptions:I,dataURLOptions:C,inlineImages:M,recordCanvas:R,preserveWhiteSpace:H,onSerialize:A,onIframeLoad:O,iframeLoadTimeout:N,onStylesheetLoad:L,stylesheetLoadTimeout:F,keepIframeSrcFn:W});t&&L(e,t)}},F),q}function _optionalChain$4(e){for(var t=void 0,n=e[0],r=1;r<e.length;){var a,o=e[r],i=e[r+1];if(r+=2,("optionalAccess"===o||"optionalCall"===o)&&null==n)return;"access"===o||"optionalAccess"===o?(t=n,n=i(n)):("call"===o||"optionalCall"===o)&&(n=i(function(){for(var e=arguments.length,r=Array(e),o=0;o<e;o++)r[o]=arguments[o];return(a=n).call.apply(a,[t].concat((0,I._)(r)))}),t=void 0)}return n}function on(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:document,r={capture:!0,passive:!0};return n.addEventListener(e,t,r),function(){return n.removeEventListener(e,t,r)}}var ep="Please stop import mirror directly. Instead of that,\r\nnow you can use replayer.getMirror() to access the mirror instance of a replayer,\r\nor you can use record.mirror to access the mirror instance during recording.",eh={map:{},getId:function(){return console.error(ep),-1},getNode:function(){return console.error(ep),null},removeNodeFromMap:function(){console.error(ep)},has:function(){return console.error(ep),!1},reset:function(){console.error(ep)}};function throttle$1(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=null,a=0;return function(){for(var o=arguments.length,i=Array(o),s=0;s<o;s++)i[s]=arguments[s];var l=Date.now();a||!1!==n.leading||(a=l);var c=t-(l-a),u=this;c<=0||c>t?(r&&(function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];getImplementation("clearTimeout").apply(this,(0,I._)(t))}(r),r=null),a=l,e.apply(u,i)):r||!1===n.trailing||(r=setTimeout$1(function(){a=!1===n.leading?0:Date.now(),r=null,e.apply(u,i)},c))}}function patch(e,t,n){try{if(!(t in e))return function(){};var r=e[t],a=n(r);return"function"==typeof a&&(a.prototype=a.prototype||{},Object.defineProperties(a,{__rrweb_original__:{enumerable:!1,value:r}})),e[t]=a,function(){e[t]=r}}catch(e){return function(){}}}window.Proxy&&window.Reflect&&(eh=new Proxy(eh,{get:function(e,t,n){return"map"===t&&console.error(ep),Reflect.get(e,t,n)}}));var ef=Date.now;function getWindowScroll(e){var t=e.document;return{left:t.scrollingElement?t.scrollingElement.scrollLeft:void 0!==e.pageXOffset?e.pageXOffset:_optionalChain$4([t,"optionalAccess",function(e){return e.documentElement},"access",function(e){return e.scrollLeft}])||_optionalChain$4([t,"optionalAccess",function(e){return e.body},"optionalAccess",function(e){return e.parentElement},"optionalAccess",function(e){return e.scrollLeft}])||_optionalChain$4([t,"optionalAccess",function(e){return e.body},"optionalAccess",function(e){return e.scrollLeft}])||0,top:t.scrollingElement?t.scrollingElement.scrollTop:void 0!==e.pageYOffset?e.pageYOffset:_optionalChain$4([t,"optionalAccess",function(e){return e.documentElement},"access",function(e){return e.scrollTop}])||_optionalChain$4([t,"optionalAccess",function(e){return e.body},"optionalAccess",function(e){return e.parentElement},"optionalAccess",function(e){return e.scrollTop}])||_optionalChain$4([t,"optionalAccess",function(e){return e.body},"optionalAccess",function(e){return e.scrollTop}])||0}}function getWindowHeight(){return window.innerHeight||document.documentElement&&document.documentElement.clientHeight||document.body&&document.body.clientHeight}function getWindowWidth(){return window.innerWidth||document.documentElement&&document.documentElement.clientWidth||document.body&&document.body.clientWidth}function closestElementOfNode(e){return e?e.nodeType===e.ELEMENT_NODE?e:e.parentElement:null}function isBlocked(e,t,n,r,a){if(!e)return!1;var o=closestElementOfNode(e);if(!o)return!1;var i=createMatchPredicate(t,n);if(!a){var s=r&&o.matches(r);return i(o)&&!s}var l=distanceToMatch(o,i),c=-1;return!(l<0)&&(r&&(c=distanceToMatch(o,createMatchPredicate(null,r))),l>-1&&c<0||l<c)}function isIgnored(e,t){return -2===t.getId(e)}function legacy_isTouchEvent(e){return!!e.changedTouches}function isSerializedIframe(e,t){return!!("IFRAME"===e.nodeName&&t.getMeta(e))}function isSerializedStylesheet(e,t){return!!("LINK"===e.nodeName&&e.nodeType===e.ELEMENT_NODE&&e.getAttribute&&"stylesheet"===e.getAttribute("rel")&&t.getMeta(e))}function hasShadowRoot(e){return!!_optionalChain$4([e,"optionalAccess",function(e){return e.shadowRoot}])}/[1-9][0-9]{12}/.test(Date.now().toString())||(ef=function(){return new Date().getTime()});var em=function(){function StyleSheetMirror(){(0,g._)(this,StyleSheetMirror),this.id=1,this.styleIDMap=new WeakMap,this.idStyleMap=new Map}return(0,k._)(StyleSheetMirror,[{key:"getId",value:function(e){return(0,M.h)(this.styleIDMap.get(e),function(){return -1})}},{key:"has",value:function(e){return this.styleIDMap.has(e)}},{key:"add",value:function(e,t){var n;return this.has(e)?this.getId(e):(n=void 0===t?this.id++:t,this.styleIDMap.set(e,n),this.idStyleMap.set(n,e),n)}},{key:"getStyle",value:function(e){return this.idStyleMap.get(e)||null}},{key:"reset",value:function(){this.styleIDMap=new WeakMap,this.idStyleMap=new Map,this.id=1}},{key:"generateId",value:function(){return this.id++}}]),StyleSheetMirror}();function getShadowHost(e){var t=null;return _optionalChain$4([e,"access",function(e){return e.getRootNode},"optionalCall",function(e){return e()},"optionalAccess",function(e){return e.nodeType}])===Node.DOCUMENT_FRAGMENT_NODE&&e.getRootNode().host&&(t=e.getRootNode().host),t}function inDom(e){var t=e.ownerDocument;return!!t&&(t.contains(e)||function(e){var t=e.ownerDocument;if(!t)return!1;var n=function(e){for(var t,n=e;t=getShadowHost(n);)n=t;return n}(e);return t.contains(n)}(e))}var ev={};function getImplementation(e){var t=ev[e];if(t)return t;var n=window.document,r=window[e];if(n&&"function"==typeof n.createElement)try{var a=n.createElement("iframe");a.hidden=!0,n.head.appendChild(a);var o=a.contentWindow;o&&o[e]&&(r=o[e]),n.head.removeChild(a)}catch(e){}return ev[e]=r.bind(window)}function setTimeout$1(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return getImplementation("setTimeout").apply(this,(0,I._)(t))}var ey=((a=ey||{})[a.DomContentLoaded=0]="DomContentLoaded",a[a.Load=1]="Load",a[a.FullSnapshot=2]="FullSnapshot",a[a.IncrementalSnapshot=3]="IncrementalSnapshot",a[a.Meta=4]="Meta",a[a.Custom=5]="Custom",a[a.Plugin=6]="Plugin",a),eg=((o=eg||{})[o.Mutation=0]="Mutation",o[o.MouseMove=1]="MouseMove",o[o.MouseInteraction=2]="MouseInteraction",o[o.Scroll=3]="Scroll",o[o.ViewportResize=4]="ViewportResize",o[o.Input=5]="Input",o[o.TouchMove=6]="TouchMove",o[o.MediaInteraction=7]="MediaInteraction",o[o.StyleSheetRule=8]="StyleSheetRule",o[o.CanvasMutation=9]="CanvasMutation",o[o.Font=10]="Font",o[o.Log=11]="Log",o[o.Drag=12]="Drag",o[o.StyleDeclaration=13]="StyleDeclaration",o[o.Selection=14]="Selection",o[o.AdoptedStyleSheet=15]="AdoptedStyleSheet",o[o.CustomElement=16]="CustomElement",o),ek=((i=ek||{})[i.MouseUp=0]="MouseUp",i[i.MouseDown=1]="MouseDown",i[i.Click=2]="Click",i[i.ContextMenu=3]="ContextMenu",i[i.DblClick=4]="DblClick",i[i.Focus=5]="Focus",i[i.Blur=6]="Blur",i[i.TouchStart=7]="TouchStart",i[i.TouchMove_Departed=8]="TouchMove_Departed",i[i.TouchEnd=9]="TouchEnd",i[i.TouchCancel=10]="TouchCancel",i),eS=((s=eS||{})[s.Mouse=0]="Mouse",s[s.Pen=1]="Pen",s[s.Touch=2]="Touch",s),e_=function(){function DoubleLinkedList(){(0,g._)(this,DoubleLinkedList),this.length=0,this.head=null,this.tail=null}return(0,k._)(DoubleLinkedList,[{key:"get",value:function(e){if(e>=this.length)throw Error("Position outside of list range");for(var t=this.head,n=0;n<e;n++)t=function(e){for(var t=void 0,n=e[0],r=1;r<e.length;){var a,o=e[r],i=e[r+1];if(r+=2,("optionalAccess"===o||"optionalCall"===o)&&null==n)return;"access"===o||"optionalAccess"===o?(t=n,n=i(n)):("call"===o||"optionalCall"===o)&&(n=i(function(){for(var e=arguments.length,r=Array(e),o=0;o<e;o++)r[o]=arguments[o];return(a=n).call.apply(a,[t].concat((0,I._)(r)))}),t=void 0)}return n}([t,"optionalAccess",function(e){return e.next}])||null;return t}},{key:"addNode",value:function(e){var t={value:e,previous:null,next:null};if(e.__ln=t,e.previousSibling&&"__ln"in e.previousSibling){var n=e.previousSibling.__ln.next;t.next=n,t.previous=e.previousSibling.__ln,e.previousSibling.__ln.next=t,n&&(n.previous=t)}else if(e.nextSibling&&"__ln"in e.nextSibling&&e.nextSibling.__ln.previous){var r=e.nextSibling.__ln.previous;t.previous=r,t.next=e.nextSibling.__ln,e.nextSibling.__ln.previous=t,r&&(r.next=t)}else this.head&&(this.head.previous=t),t.next=this.head,this.head=t;null===t.next&&(this.tail=t),this.length++}},{key:"removeNode",value:function(e){var t=e.__ln;this.head&&(t.previous?(t.previous.next=t.next,t.next?t.next.previous=t.previous:this.tail=t.previous):(this.head=t.next,this.head?this.head.previous=null:this.tail=null),e.__ln&&delete e.__ln,this.length--)}}]),DoubleLinkedList}(),moveKey=function(e,t){return"".concat(e,"@").concat(t)},eb=function(){function MutationBuffer(){var e=this;(0,g._)(this,MutationBuffer),this.frozen=!1,this.locked=!1,this.texts=[],this.attributes=[],this.attributeMap=new WeakMap,this.removes=[],this.mapRemoves=[],this.movedMap={},this.addedSet=new Set,this.movedSet=new Set,this.droppedSet=new Set,this.processMutations=function(t){t.forEach(e.processMutation),e.emit()},this.emit=function(){if(!e.frozen&&!e.locked){for(var t=[],n=new Set,r=new e_,getNextId=function(t){for(var n=t,r=-2;-2===r;)r=(n=n&&n.nextSibling)&&e.mirror.getId(n);return r},pushAdd=function(a){if(a.parentNode&&inDom(a)){var o=isShadowRoot(a.parentNode)?e.mirror.getId(getShadowHost(a)):e.mirror.getId(a.parentNode),i=getNextId(a);if(-1===o||-1===i)return r.addNode(a);var s=serializeNodeWithId(a,{doc:e.doc,mirror:e.mirror,blockClass:e.blockClass,blockSelector:e.blockSelector,maskAllText:e.maskAllText,unblockSelector:e.unblockSelector,maskTextClass:e.maskTextClass,unmaskTextClass:e.unmaskTextClass,maskTextSelector:e.maskTextSelector,unmaskTextSelector:e.unmaskTextSelector,skipChild:!0,newlyAddedElement:!0,inlineStylesheet:e.inlineStylesheet,maskInputOptions:e.maskInputOptions,maskAttributeFn:e.maskAttributeFn,maskTextFn:e.maskTextFn,maskInputFn:e.maskInputFn,slimDOMOptions:e.slimDOMOptions,dataURLOptions:e.dataURLOptions,recordCanvas:e.recordCanvas,inlineImages:e.inlineImages,onSerialize:function(t){isSerializedIframe(t,e.mirror)&&e.iframeManager.addIframe(t),isSerializedStylesheet(t,e.mirror)&&e.stylesheetManager.trackLinkElement(t),hasShadowRoot(a)&&e.shadowDomManager.addShadowRoot(a.shadowRoot,e.doc)},onIframeLoad:function(t,n){e.iframeManager.attachIframe(t,n),e.shadowDomManager.observeAttachShadow(t)},onStylesheetLoad:function(t,n){e.stylesheetManager.attachLinkElement(t,n)}});s&&(t.push({parentId:o,nextId:i,node:s}),n.add(s.id))}};e.mapRemoves.length;)e.mirror.removeNodeFromMap(e.mapRemoves.shift());var a=!0,o=!1,i=void 0;try{for(var s,l=e.movedSet[Symbol.iterator]();!(a=(s=l.next()).done);a=!0){var c=s.value;(!isParentRemoved(e.removes,c,e.mirror)||e.movedSet.has(c.parentNode))&&pushAdd(c)}}catch(e){o=!0,i=e}finally{try{a||null==l.return||l.return()}finally{if(o)throw i}}var u=!0,d=!1,p=void 0;try{for(var h,f=e.addedSet[Symbol.iterator]();!(u=(h=f.next()).done);u=!0){var m=h.value;isAncestorInSet(e.droppedSet,m)||isParentRemoved(e.removes,m,e.mirror)?isAncestorInSet(e.movedSet,m)?pushAdd(m):e.droppedSet.add(m):pushAdd(m)}}catch(e){d=!0,p=e}finally{try{u||null==f.return||f.return()}finally{if(d)throw p}}for(var v=null;r.length;){var y=null;if(v){var g=e.mirror.getId(v.value.parentNode),k=getNextId(v.value);-1!==g&&-1!==k&&(y=v)}if(!y)for(var S=r.tail;S;){var _=S;if(S=S.previous,_){var b=e.mirror.getId(_.value.parentNode);if(-1===getNextId(_.value))continue;if(-1!==b){y=_;break}var w=_.value;if(w.parentNode&&w.parentNode.nodeType===Node.DOCUMENT_FRAGMENT_NODE){var I=w.parentNode.host;if(-1!==e.mirror.getId(I)){y=_;break}}}}if(!y){for(;r.head;)r.removeNode(r.head.value);break}v=y.previous,r.removeNode(y.value),pushAdd(y.value)}var E={texts:e.texts.map(function(t){return{id:e.mirror.getId(t.node),value:t.value}}).filter(function(e){return!n.has(e.id)}).filter(function(t){return e.mirror.has(t.id)}),attributes:e.attributes.map(function(t){var n=t.attributes;if("string"==typeof n.style){var r=JSON.stringify(t.styleDiff),a=JSON.stringify(t._unchangedStyles);r.length<n.style.length&&(r+a).split("var(").length===n.style.split("var(").length&&(n.style=t.styleDiff)}return{id:e.mirror.getId(t.node),attributes:n}}).filter(function(e){return!n.has(e.id)}).filter(function(t){return e.mirror.has(t.id)}),removes:e.removes,adds:t};(E.texts.length||E.attributes.length||E.removes.length||E.adds.length)&&(e.texts=[],e.attributes=[],e.attributeMap=new WeakMap,e.removes=[],e.addedSet=new Set,e.movedSet=new Set,e.droppedSet=new Set,e.movedMap={},e.mutationCb(E))}},this.processMutation=function(t){if(!isIgnored(t.target,e.mirror))switch(t.type){case"characterData":var n=t.target.textContent;isBlocked(t.target,e.blockClass,e.blockSelector,e.unblockSelector,!1)||n===t.oldValue||e.texts.push({value:needMaskingText(t.target,e.maskTextClass,e.maskTextSelector,e.unmaskTextClass,e.unmaskTextSelector,e.maskAllText)&&n?e.maskTextFn?e.maskTextFn(n,closestElementOfNode(t.target)):n.replace(/[\S]/g,"*"):n,node:t.target});break;case"attributes":var r=t.target,a=t.attributeName,o=t.target.getAttribute(a);if("value"===a){var i=getInputType(r),s=r.tagName;o=getInputValue(r,s,i);var l=shouldMaskInput({maskInputOptions:e.maskInputOptions,tagName:s,type:i});o=maskInputValue({isMasked:needMaskingText(t.target,e.maskTextClass,e.maskTextSelector,e.unmaskTextClass,e.unmaskTextSelector,l),element:r,value:o,maskInputFn:e.maskInputFn})}if(isBlocked(t.target,e.blockClass,e.blockSelector,e.unblockSelector,!1)||o===t.oldValue)return;var c=e.attributeMap.get(t.target);if("IFRAME"===r.tagName&&"src"===a&&!e.keepIframeSrcFn(o)){if(r.contentDocument)return;a="rr_src"}if(c||(c={node:t.target,attributes:{},styleDiff:{},_unchangedStyles:{}},e.attributes.push(c),e.attributeMap.set(t.target,c)),"type"===a&&"INPUT"===r.tagName&&"password"===(t.oldValue||"").toLowerCase()&&r.setAttribute("data-rr-is-password","true"),!ignoreAttribute(r.tagName,a)&&(c.attributes[a]=transformAttribute(e.doc,toLowerCase(r.tagName),toLowerCase(a),o,r,e.maskAttributeFn),"style"===a)){if(!e.unattachedDoc)try{e.unattachedDoc=document.implementation.createHTMLDocument()}catch(t){e.unattachedDoc=e.doc}var u=e.unattachedDoc.createElement("span");t.oldValue&&u.setAttribute("style",t.oldValue);var d=!0,p=!1,h=void 0;try{for(var f,m=Array.from(r.style)[Symbol.iterator]();!(d=(f=m.next()).done);d=!0){var v=f.value,y=r.style.getPropertyValue(v),g=r.style.getPropertyPriority(v);y!==u.style.getPropertyValue(v)||g!==u.style.getPropertyPriority(v)?""===g?c.styleDiff[v]=y:c.styleDiff[v]=[y,g]:c._unchangedStyles[v]=[y,g]}}catch(e){p=!0,h=e}finally{try{d||null==m.return||m.return()}finally{if(p)throw h}}var k=!0,S=!1,_=void 0;try{for(var b,w=Array.from(u.style)[Symbol.iterator]();!(k=(b=w.next()).done);k=!0){var I=b.value;""===r.style.getPropertyValue(I)&&(c.styleDiff[I]=!1)}}catch(e){S=!0,_=e}finally{try{k||null==w.return||w.return()}finally{if(S)throw _}}}break;case"childList":if(isBlocked(t.target,e.blockClass,e.blockSelector,e.unblockSelector,!0))return;t.addedNodes.forEach(function(n){return e.genAdds(n,t.target)}),t.removedNodes.forEach(function(n){var r=e.mirror.getId(n),a=isShadowRoot(t.target)?e.mirror.getId(t.target.host):e.mirror.getId(t.target);isBlocked(t.target,e.blockClass,e.blockSelector,e.unblockSelector,!1)||isIgnored(n,e.mirror)||-1===e.mirror.getId(n)||(e.addedSet.has(n)?(deepDelete(e.addedSet,n),e.droppedSet.add(n)):e.addedSet.has(t.target)&&-1===r||function isAncestorRemoved(e,t){if(isShadowRoot(e))return!1;var n=t.getId(e);return!t.has(n)||(!e.parentNode||e.parentNode.nodeType!==e.DOCUMENT_NODE)&&(!e.parentNode||isAncestorRemoved(e.parentNode,t))}(t.target,e.mirror)||(e.movedSet.has(n)&&e.movedMap[moveKey(r,a)]?deepDelete(e.movedSet,n):e.removes.push({parentId:a,id:r,isShadow:!!(isShadowRoot(t.target)&&isNativeShadowDom(t.target))||void 0})),e.mapRemoves.push(n))})}},this.genAdds=function(t,n){if(!e.processedNodeManager.inOtherBuffer(t,e)&&!(e.addedSet.has(t)||e.movedSet.has(t))){if(e.mirror.hasNode(t)){if(isIgnored(t,e.mirror))return;e.movedSet.add(t);var r=null;n&&e.mirror.hasNode(n)&&(r=e.mirror.getId(n)),r&&-1!==r&&(e.movedMap[moveKey(e.mirror.getId(t),r)]=!0)}else e.addedSet.add(t),e.droppedSet.delete(t);!isBlocked(t,e.blockClass,e.blockSelector,e.unblockSelector,!1)&&(t.childNodes.forEach(function(t){return e.genAdds(t)}),hasShadowRoot(t)&&t.shadowRoot.childNodes.forEach(function(n){e.processedNodeManager.add(n,e),e.genAdds(n,t)}))}}}return(0,k._)(MutationBuffer,[{key:"init",value:function(e){var t=this;["mutationCb","blockClass","blockSelector","unblockSelector","maskAllText","maskTextClass","unmaskTextClass","maskTextSelector","unmaskTextSelector","inlineStylesheet","maskInputOptions","maskAttributeFn","maskTextFn","maskInputFn","keepIframeSrcFn","recordCanvas","inlineImages","slimDOMOptions","dataURLOptions","doc","mirror","iframeManager","stylesheetManager","shadowDomManager","canvasManager","processedNodeManager"].forEach(function(n){t[n]=e[n]})}},{key:"freeze",value:function(){this.frozen=!0,this.canvasManager.freeze()}},{key:"unfreeze",value:function(){this.frozen=!1,this.canvasManager.unfreeze(),this.emit()}},{key:"isFrozen",value:function(){return this.frozen}},{key:"lock",value:function(){this.locked=!0,this.canvasManager.lock()}},{key:"unlock",value:function(){this.locked=!1,this.canvasManager.unlock(),this.emit()}},{key:"reset",value:function(){this.shadowDomManager.reset(),this.canvasManager.reset()}}]),MutationBuffer}();function deepDelete(e,t){e.delete(t),t.childNodes.forEach(function(t){return deepDelete(e,t)})}function isParentRemoved(e,t,n){return 0!==e.length&&function _isParentRemoved(e,t,n){var r=t.parentNode;if(!r)return!1;var a=n.getId(r);return!!e.some(function(e){return e.id===a})||_isParentRemoved(e,r,n)}(e,t,n)}function isAncestorInSet(e,t){return 0!==e.size&&function _isAncestorInSet(e,t){var n=t.parentNode;return!!n&&(!!e.has(n)||_isAncestorInSet(e,n))}(e,t)}var callbackWrapper=function(e){return p?function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];try{return e.apply(void 0,(0,I._)(n))}catch(e){if(p&&!0===p(e))return function(){};throw e}}:e};function _optionalChain$2(e){for(var t=void 0,n=e[0],r=1;r<e.length;){var a,o=e[r],i=e[r+1];if(r+=2,("optionalAccess"===o||"optionalCall"===o)&&null==n)return;"access"===o||"optionalAccess"===o?(t=n,n=i(n)):("call"===o||"optionalCall"===o)&&(n=i(function(){for(var e=arguments.length,r=Array(e),o=0;o<e;o++)r[o]=arguments[o];return(a=n).call.apply(a,[t].concat((0,I._)(r)))}),t=void 0)}return n}var ew=[];function getEventTarget(e){try{if("composedPath"in e){var t=e.composedPath();if(t.length)return t[0]}else if("path"in e&&e.path.length)return e.path[0]}catch(e){}return e&&e.target}function initMutationObserver(e,t){var n=new eb;ew.push(n),n.init(e);var r=window.MutationObserver||window.__rrMutationObserver,a=_optionalChain$2([window,"optionalAccess",function(e){return e.Zone},"optionalAccess",function(e){return e.__symbol__},"optionalCall",function(e){return e("MutationObserver")}]);a&&window[a]&&(r=window[a]);var o=new r(callbackWrapper(function(t){e.onMutation&&!1===e.onMutation(t)||n.processMutations.bind(n)(t)}));return o.observe(t,{attributes:!0,attributeOldValue:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0}),o}function initScrollObserver(e){var t=e.scrollCb,n=e.doc,r=e.mirror,a=e.blockClass,o=e.blockSelector,i=e.unblockSelector,s=e.sampling;return on("scroll",callbackWrapper(throttle$1(callbackWrapper(function(e){var s=getEventTarget(e);if(!(!s||isBlocked(s,a,o,i,!0))){var l=r.getId(s);if(s===n&&n.defaultView){var c=getWindowScroll(n.defaultView);t({id:l,x:c.left,y:c.top})}else t({id:l,x:s.scrollLeft,y:s.scrollTop})}}),s.scroll||100)),n)}var eI=["INPUT","TEXTAREA","SELECT"],eE=new WeakMap;function getNestedCSSRulePositions(e){var t;return t=[],hasNestedCSSRule("CSSGroupingRule")&&e.parentRule instanceof CSSGroupingRule||hasNestedCSSRule("CSSMediaRule")&&e.parentRule instanceof CSSMediaRule||hasNestedCSSRule("CSSSupportsRule")&&e.parentRule instanceof CSSSupportsRule||hasNestedCSSRule("CSSConditionRule")&&e.parentRule instanceof CSSConditionRule?t.unshift(Array.from(e.parentRule.cssRules).indexOf(e)):e.parentStyleSheet&&t.unshift(Array.from(e.parentStyleSheet.cssRules).indexOf(e)),t}function getIdAndStyleId(e,t,n){var r,a;return e?(e.ownerNode?r=t.getId(e.ownerNode):a=n.getId(e),{styleId:a,id:r}):{}}function initAdoptedStyleSheetObserver(e,t){var n=e.mirror,r=e.stylesheetManager,a=null;a="#document"===t.nodeName?n.getId(t):n.getId(t.host);var o="#document"===t.nodeName?_optionalChain$2([t,"access",function(e){return e.defaultView},"optionalAccess",function(e){return e.Document}]):_optionalChain$2([t,"access",function(e){return e.ownerDocument},"optionalAccess",function(e){return e.defaultView},"optionalAccess",function(e){return e.ShadowRoot}]),i=_optionalChain$2([o,"optionalAccess",function(e){return e.prototype}])?Object.getOwnPropertyDescriptor(_optionalChain$2([o,"optionalAccess",function(e){return e.prototype}]),"adoptedStyleSheets"):void 0;return null!==a&&-1!==a&&o&&i?(Object.defineProperty(t,"adoptedStyleSheets",{configurable:i.configurable,enumerable:i.enumerable,get:function(){var e=this;return _optionalChain$2([i,"access",function(e){return e.get},"optionalAccess",function(e){return e.call},"call",function(t){return t(e)}])},set:function(e){var t=this,n=_optionalChain$2([i,"access",function(e){return e.set},"optionalAccess",function(e){return e.call},"call",function(n){return n(t,e)}]);if(null!==a&&-1!==a)try{r.adoptStyleSheets(e,a)}catch(e){}return n}}),callbackWrapper(function(){Object.defineProperty(t,"adoptedStyleSheets",{configurable:i.configurable,enumerable:i.enumerable,get:i.get,set:i.set})})):function(){}}function initObservers(e){arguments.length>1&&void 0!==arguments[1]&&arguments[1];var t=e.doc.defaultView;if(!t)return function(){};var n=initMutationObserver(e,e.doc),r=function(e){var t,n=e.mousemoveCb,r=e.sampling,a=e.doc,o=e.mirror;if(!1===r.mousemove)return function(){};var i="number"==typeof r.mousemove?r.mousemove:50,s="number"==typeof r.mousemoveCallback?r.mousemoveCallback:500,l=[],c=throttle$1(callbackWrapper(function(e){var r=Date.now()-t;n(l.map(function(e){return e.timeOffset-=r,e}),e),l=[],t=null}),s),u=callbackWrapper(throttle$1(callbackWrapper(function(e){var n=getEventTarget(e),r=legacy_isTouchEvent(e)?e.changedTouches[0]:e,a=r.clientX,i=r.clientY;t||(t=ef()),l.push({x:a,y:i,id:o.getId(n),timeOffset:ef()-t}),c("undefined"!=typeof DragEvent&&e instanceof DragEvent?eg.Drag:e instanceof MouseEvent?eg.MouseMove:eg.TouchMove)}),i,{trailing:!1})),d=[on("mousemove",u,a),on("touchmove",u,a),on("drag",u,a)];return callbackWrapper(function(){d.forEach(function(e){return e()})})}(e),a=function(e){var t=e.mouseInteractionCb,n=e.doc,r=e.mirror,a=e.blockClass,o=e.blockSelector,i=e.unblockSelector,s=e.sampling;if(!1===s.mouseInteraction)return function(){};var l=!0===s.mouseInteraction||void 0===s.mouseInteraction?{}:s.mouseInteraction,c=[],u=null;return Object.keys(ek).filter(function(e){return Number.isNaN(Number(e))&&!e.endsWith("_Departed")&&!1!==l[e]}).forEach(function(e){var s=toLowerCase(e);if(window.PointerEvent)switch(ek[e]){case ek.MouseDown:case ek.MouseUp:s=s.replace("mouse","pointer");break;case ek.TouchStart:case ek.TouchEnd:return}c.push(on(s,function(n){var s=getEventTarget(n);if(!isBlocked(s,a,o,i,!0)){var l=null,c=e;if("pointerType"in n){switch(n.pointerType){case"mouse":l=eS.Mouse;break;case"touch":l=eS.Touch;break;case"pen":l=eS.Pen}l===eS.Touch?ek[e]===ek.MouseDown?c="TouchStart":ek[e]===ek.MouseUp&&(c="TouchEnd"):eS.Pen}else legacy_isTouchEvent(n)&&(l=eS.Touch);null!==l?(u=l,(c.startsWith("Touch")&&l===eS.Touch||c.startsWith("Mouse")&&l===eS.Mouse)&&(l=null)):ek[e]===ek.Click&&(l=u,u=null);var d=legacy_isTouchEvent(n)?n.changedTouches[0]:n;if(d){var p=r.getId(s),h=d.clientX,f=d.clientY;callbackWrapper(t)((0,_._)({type:ek[c],id:p,x:h,y:f},null!==l&&{pointerType:l}))}}},n))}),callbackWrapper(function(){c.forEach(function(e){return e()})})}(e),o=initScrollObserver(e),i=(k=e.viewportResizeCb,S=-1,E=-1,on("resize",callbackWrapper(throttle$1(callbackWrapper(function(){var e=getWindowHeight(),t=getWindowWidth();(S!==e||E!==t)&&(k({width:Number(t),height:Number(e)}),S=e,E=t)}),200)),t)),s=function(e){var t=e.inputCb,n=e.doc,r=e.mirror,a=e.blockClass,o=e.blockSelector,i=e.unblockSelector,s=e.ignoreClass,l=e.ignoreSelector,c=e.maskInputOptions,u=e.maskInputFn,d=e.sampling,p=e.userTriggeredOnInput,h=e.maskTextClass,f=e.unmaskTextClass,m=e.maskTextSelector,v=e.unmaskTextSelector;function eventHandler(e){var t=getEventTarget(e),r=e.isTrusted,d=t&&toUpperCase(t.tagName);if("OPTION"===d&&(t=t.parentElement),!(!t||!d||0>eI.indexOf(d)||isBlocked(t,a,o,i,!0))){var y=t;if(!(y.classList.contains(s)||l&&y.matches(l))){var g=getInputType(t),k=getInputValue(y,d,g),S=!1,_=shouldMaskInput({maskInputOptions:c,tagName:d,type:g}),b=needMaskingText(t,h,m,f,v,_);("radio"===g||"checkbox"===g)&&(S=t.checked),k=maskInputValue({isMasked:b,element:t,value:k,maskInputFn:u}),cbWithDedup(t,p?{text:k,isChecked:S,userTriggered:r}:{text:k,isChecked:S});var w=t.name;"radio"===g&&w&&S&&n.querySelectorAll('input[type="radio"][name="'.concat(w,'"]')).forEach(function(e){if(e!==t){var n=maskInputValue({isMasked:b,element:e,value:getInputValue(e,d,g),maskInputFn:u});cbWithDedup(e,p?{text:n,isChecked:!S,userTriggered:!1}:{text:n,isChecked:!S})}})}}}function cbWithDedup(e,n){var a=eE.get(e);if(!a||a.text!==n.text||a.isChecked!==n.isChecked){eE.set(e,n);var o=r.getId(e);callbackWrapper(t)((0,b._)((0,_._)({},n),{id:o}))}}var y=("last"===d.input?["change"]:["input","change"]).map(function(e){return on(e,callbackWrapper(eventHandler),n)}),g=n.defaultView;if(!g)return function(){y.forEach(function(e){return e()})};var k=g.Object.getOwnPropertyDescriptor(g.HTMLInputElement.prototype,"value"),S=[[g.HTMLInputElement.prototype,"value"],[g.HTMLInputElement.prototype,"checked"],[g.HTMLSelectElement.prototype,"value"],[g.HTMLTextAreaElement.prototype,"value"],[g.HTMLSelectElement.prototype,"selectedIndex"],[g.HTMLOptionElement.prototype,"selected"]];return k&&k.set&&y.push.apply(y,(0,I._)(S.map(function(e){return function hookSetter(e,t,n,r){var a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:window,o=a.Object.getOwnPropertyDescriptor(e,t);return a.Object.defineProperty(e,t,r?n:{set:function(e){var t=this;setTimeout$1(function(){n.set.call(t,e)},0),o&&o.set&&o.set.call(this,e)}}),function(){return hookSetter(e,t,o||{},!0)}}(e[0],e[1],{set:function(){callbackWrapper(eventHandler)({target:this,isTrusted:!1})}},!1,g)}))),callbackWrapper(function(){y.forEach(function(e){return e()})})}(e),l=(C=e.mediaInteractionCb,T=e.blockClass,M=e.blockSelector,x=e.unblockSelector,R=e.mirror,A=e.sampling,O=e.doc,N=[on("play",(D=callbackWrapper(function(e){return throttle$1(callbackWrapper(function(t){var n=getEventTarget(t);if(!(!n||isBlocked(n,T,M,x,!0))){var r=n.currentTime,a=n.volume,o=n.muted,i=n.playbackRate;C({type:e,id:R.getId(n),currentTime:r,volume:a,muted:o,playbackRate:i})}}),A.media||500)}))(0),O),on("pause",D(1),O),on("seeked",D(2),O),on("volumechange",D(3),O),on("ratechange",D(4),O)],callbackWrapper(function(){N.forEach(function(e){return e()})})),c=function(e,t){var n,r,a=e.styleSheetRuleCb,o=e.mirror,i=e.stylesheetManager,s=t.win;if(!s.CSSStyleSheet||!s.CSSStyleSheet.prototype)return function(){};var l=s.CSSStyleSheet.prototype.insertRule;s.CSSStyleSheet.prototype.insertRule=new Proxy(l,{apply:callbackWrapper(function(e,t,n){var r=(0,w._)(n,2),s=r[0],l=r[1],c=getIdAndStyleId(t,o,i.styleMirror),u=c.id,d=c.styleId;return(u&&-1!==u||d&&-1!==d)&&a({id:u,styleId:d,adds:[{rule:s,index:l}]}),e.apply(t,n)})});var c=s.CSSStyleSheet.prototype.deleteRule;s.CSSStyleSheet.prototype.deleteRule=new Proxy(c,{apply:callbackWrapper(function(e,t,n){var r=(0,w._)(n,1)[0],s=getIdAndStyleId(t,o,i.styleMirror),l=s.id,c=s.styleId;return(l&&-1!==l||c&&-1!==c)&&a({id:l,styleId:c,removes:[{index:r}]}),e.apply(t,n)})}),s.CSSStyleSheet.prototype.replace&&(n=s.CSSStyleSheet.prototype.replace,s.CSSStyleSheet.prototype.replace=new Proxy(n,{apply:callbackWrapper(function(e,t,n){var r=(0,w._)(n,1)[0],s=getIdAndStyleId(t,o,i.styleMirror),l=s.id,c=s.styleId;return(l&&-1!==l||c&&-1!==c)&&a({id:l,styleId:c,replace:r}),e.apply(t,n)})})),s.CSSStyleSheet.prototype.replaceSync&&(r=s.CSSStyleSheet.prototype.replaceSync,s.CSSStyleSheet.prototype.replaceSync=new Proxy(r,{apply:callbackWrapper(function(e,t,n){var r=(0,w._)(n,1)[0],s=getIdAndStyleId(t,o,i.styleMirror),l=s.id,c=s.styleId;return(l&&-1!==l||c&&-1!==c)&&a({id:l,styleId:c,replaceSync:r}),e.apply(t,n)})}));var u={};canMonkeyPatchNestedCSSRule("CSSGroupingRule")?u.CSSGroupingRule=s.CSSGroupingRule:(canMonkeyPatchNestedCSSRule("CSSMediaRule")&&(u.CSSMediaRule=s.CSSMediaRule),canMonkeyPatchNestedCSSRule("CSSConditionRule")&&(u.CSSConditionRule=s.CSSConditionRule),canMonkeyPatchNestedCSSRule("CSSSupportsRule")&&(u.CSSSupportsRule=s.CSSSupportsRule));var d={};return Object.entries(u).forEach(function(e){var t=(0,w._)(e,2),n=t[0],r=t[1];d[n]={insertRule:r.prototype.insertRule,deleteRule:r.prototype.deleteRule},r.prototype.insertRule=new Proxy(d[n].insertRule,{apply:callbackWrapper(function(e,t,n){var r=(0,w._)(n,2),s=r[0],l=r[1],c=getIdAndStyleId(t.parentStyleSheet,o,i.styleMirror),u=c.id,d=c.styleId;return(u&&-1!==u||d&&-1!==d)&&a({id:u,styleId:d,adds:[{rule:s,index:(0,I._)(getNestedCSSRulePositions(t)).concat([l||0])}]}),e.apply(t,n)})}),r.prototype.deleteRule=new Proxy(d[n].deleteRule,{apply:callbackWrapper(function(e,t,n){var r=(0,w._)(n,1)[0],s=getIdAndStyleId(t.parentStyleSheet,o,i.styleMirror),l=s.id,c=s.styleId;return(l&&-1!==l||c&&-1!==c)&&a({id:l,styleId:c,removes:[{index:(0,I._)(getNestedCSSRulePositions(t)).concat([r])}]}),e.apply(t,n)})})}),callbackWrapper(function(){s.CSSStyleSheet.prototype.insertRule=l,s.CSSStyleSheet.prototype.deleteRule=c,n&&(s.CSSStyleSheet.prototype.replace=n),r&&(s.CSSStyleSheet.prototype.replaceSync=r),Object.entries(u).forEach(function(e){var t=(0,w._)(e,2),n=t[0],r=t[1];r.prototype.insertRule=d[n].insertRule,r.prototype.deleteRule=d[n].deleteRule})})}(e,{win:t}),u=initAdoptedStyleSheetObserver(e,e.doc),d=(L=e.styleDeclarationCb,B=e.mirror,F=e.ignoreCSSAttributes,P=e.stylesheetManager,W=t.CSSStyleDeclaration.prototype.setProperty,t.CSSStyleDeclaration.prototype.setProperty=new Proxy(W,{apply:callbackWrapper(function(e,t,n){var r=(0,w._)(n,3),a=r[0],o=r[1],i=r[2];if(F.has(a))return W.apply(t,[a,o,i]);var s=getIdAndStyleId(_optionalChain$2([t,"access",function(e){return e.parentRule},"optionalAccess",function(e){return e.parentStyleSheet}]),B,P.styleMirror),l=s.id,c=s.styleId;return(l&&-1!==l||c&&-1!==c)&&L({id:l,styleId:c,set:{property:a,value:o,priority:i},index:getNestedCSSRulePositions(t.parentRule)}),e.apply(t,n)})}),z=t.CSSStyleDeclaration.prototype.removeProperty,t.CSSStyleDeclaration.prototype.removeProperty=new Proxy(z,{apply:callbackWrapper(function(e,t,n){var r=(0,w._)(n,1)[0];if(F.has(r))return z.apply(t,[r]);var a=getIdAndStyleId(_optionalChain$2([t,"access",function(e){return e.parentRule},"optionalAccess",function(e){return e.parentStyleSheet}]),B,P.styleMirror),o=a.id,i=a.styleId;return(o&&-1!==o||i&&-1!==i)&&L({id:o,styleId:i,remove:{property:r},index:getNestedCSSRulePositions(t.parentRule)}),e.apply(t,n)})}),callbackWrapper(function(){t.CSSStyleDeclaration.prototype.setProperty=W,t.CSSStyleDeclaration.prototype.removeProperty=z})),p=e.collectFonts?function(e){var t=e.fontCb,n=e.doc,r=n.defaultView;if(!r)return function(){};var a=[],o=new WeakMap,i=r.FontFace;r.FontFace=function(e,t,n){var r=new i(e,t,n);return o.set(r,{family:e,buffer:"string"!=typeof t,descriptors:n,fontSource:"string"==typeof t?t:JSON.stringify(Array.from(new Uint8Array(t)))}),r};var s=patch(n.fonts,"add",function(e){return function(n){return setTimeout$1(callbackWrapper(function(){var e=o.get(n);e&&(t(e),o.delete(n))}),0),e.apply(this,[n])}});return a.push(function(){r.FontFace=i}),a.push(s),callbackWrapper(function(){a.forEach(function(e){return e()})})}(e):function(){},h=(U=e.doc,H=e.mirror,j=e.blockClass,q=e.blockSelector,$=e.unblockSelector,J=e.selectionCb,K=!0,(V=callbackWrapper(function(){var e=U.getSelection();if(!(!e||K&&_optionalChain$2([e,"optionalAccess",function(e){return e.isCollapsed}]))){K=e.isCollapsed||!1;for(var t=[],n=e.rangeCount||0,r=0;r<n;r++){var a=e.getRangeAt(r),o=a.startContainer,i=a.startOffset,s=a.endContainer,l=a.endOffset;isBlocked(o,j,q,$,!0)||isBlocked(s,j,q,$,!0)||t.push({start:H.getId(o),startOffset:i,end:H.getId(s),endOffset:l})}J({ranges:t})}}))(),on("selectionchange",V)),f=(Y=e.doc,X=e.customElementCb,(G=Y.defaultView)&&G.customElements?patch(G.customElements,"define",function(e){return function(t,n,r){try{X({define:{name:t}})}catch(e){}return e.apply(this,[t,n,r])}}):function(){}),m=[],v=!0,y=!1,g=void 0;try{for(var k,S,E,C,T,M,x,R,A,O,D,N,L,B,F,P,W,z,U,H,j,q,$,J,K,V,Y,X,G,Q,Z=e.plugins[Symbol.iterator]();!(v=(Q=Z.next()).done);v=!0){var ee=Q.value;m.push(ee.observer(ee.callback,t,ee.options))}}catch(e){y=!0,g=e}finally{try{v||null==Z.return||Z.return()}finally{if(y)throw g}}return callbackWrapper(function(){ew.forEach(function(e){return e.reset()}),n.disconnect(),r(),a(),o(),i(),s(),l(),c(),u(),d(),p(),h(),f(),m.forEach(function(e){return e()})})}function hasNestedCSSRule(e){return void 0!==window[e]}function canMonkeyPatchNestedCSSRule(e){return!!(void 0!==window[e]&&window[e].prototype&&"insertRule"in window[e].prototype&&"deleteRule"in window[e].prototype)}var eC=function(){function CrossOriginIframeMirror(e){(0,g._)(this,CrossOriginIframeMirror),this.generateIdFn=e,this.iframeIdToRemoteIdMap=new WeakMap,this.iframeRemoteIdToIdMap=new WeakMap}return(0,k._)(CrossOriginIframeMirror,[{key:"getId",value:function(e,t,n,r){var a=n||this.getIdToRemoteIdMap(e),o=r||this.getRemoteIdToIdMap(e),i=a.get(t);return i||(i=this.generateIdFn(),a.set(t,i),o.set(i,t)),i}},{key:"getIds",value:function(e,t){var n=this,r=this.getIdToRemoteIdMap(e),a=this.getRemoteIdToIdMap(e);return t.map(function(t){return n.getId(e,t,r,a)})}},{key:"getRemoteId",value:function(e,t,n){var r=n||this.getRemoteIdToIdMap(e);return"number"!=typeof t?t:r.get(t)||-1}},{key:"getRemoteIds",value:function(e,t){var n=this,r=this.getRemoteIdToIdMap(e);return t.map(function(t){return n.getRemoteId(e,t,r)})}},{key:"reset",value:function(e){if(!e){this.iframeIdToRemoteIdMap=new WeakMap,this.iframeRemoteIdToIdMap=new WeakMap;return}this.iframeIdToRemoteIdMap.delete(e),this.iframeRemoteIdToIdMap.delete(e)}},{key:"getIdToRemoteIdMap",value:function(e){var t=this.iframeIdToRemoteIdMap.get(e);return t||(t=new Map,this.iframeIdToRemoteIdMap.set(e,t)),t}},{key:"getRemoteIdToIdMap",value:function(e){var t=this.iframeRemoteIdToIdMap.get(e);return t||(t=new Map,this.iframeRemoteIdToIdMap.set(e,t)),t}}]),CrossOriginIframeMirror}();function _optionalChain$1(e){for(var t=void 0,n=e[0],r=1;r<e.length;){var a,o=e[r],i=e[r+1];if(r+=2,("optionalAccess"===o||"optionalCall"===o)&&null==n)return;"access"===o||"optionalAccess"===o?(t=n,n=i(n)):("call"===o||"optionalCall"===o)&&(n=i(function(){for(var e=arguments.length,r=Array(e),o=0;o<e;o++)r[o]=arguments[o];return(a=n).call.apply(a,[t].concat((0,I._)(r)))}),t=void 0)}return n}var eT=function(){function IframeManagerNoop(){(0,g._)(this,IframeManagerNoop),this.crossOriginIframeMirror=new eC(genId),this.crossOriginIframeRootIdMap=new WeakMap}return(0,k._)(IframeManagerNoop,[{key:"addIframe",value:function(){}},{key:"addLoadListener",value:function(){}},{key:"attachIframe",value:function(){}}]),IframeManagerNoop}(),eM=function(){function IframeManager(e){(0,g._)(this,IframeManager),this.iframes=new WeakMap,this.crossOriginIframeMap=new WeakMap,this.crossOriginIframeMirror=new eC(genId),this.crossOriginIframeRootIdMap=new WeakMap,this.mutationCb=e.mutationCb,this.wrappedEmit=e.wrappedEmit,this.stylesheetManager=e.stylesheetManager,this.recordCrossOriginIframes=e.recordCrossOriginIframes,this.crossOriginIframeStyleMirror=new eC(this.stylesheetManager.styleMirror.generateId.bind(this.stylesheetManager.styleMirror)),this.mirror=e.mirror,this.recordCrossOriginIframes&&window.addEventListener("message",this.handleMessage.bind(this))}return(0,k._)(IframeManager,[{key:"addIframe",value:function(e){this.iframes.set(e,!0),e.contentWindow&&this.crossOriginIframeMap.set(e.contentWindow,e)}},{key:"addLoadListener",value:function(e){this.loadListener=e}},{key:"attachIframe",value:function(e,t){this.mutationCb({adds:[{parentId:this.mirror.getId(e),nextId:null,node:t}],removes:[],texts:[],attributes:[],isAttachIframe:!0}),_optionalChain$1([this,"access",function(e){return e.loadListener},"optionalCall",function(t){return t(e)}]),e.contentDocument&&e.contentDocument.adoptedStyleSheets&&e.contentDocument.adoptedStyleSheets.length>0&&this.stylesheetManager.adoptStyleSheets(e.contentDocument.adoptedStyleSheets,this.mirror.getId(e.contentDocument))}},{key:"handleMessage",value:function(e){if("rrweb"===e.data.type&&e.origin===e.data.origin&&e.source){var t=this.crossOriginIframeMap.get(e.source);if(t){var n=this.transformCrossOriginEvent(t,e.data.event);n&&this.wrappedEmit(n,e.data.isCheckout)}}}},{key:"transformCrossOriginEvent",value:function(e,t){var n=this;switch(t.type){case ey.FullSnapshot:this.crossOriginIframeMirror.reset(e),this.crossOriginIframeStyleMirror.reset(e),this.replaceIdOnNode(t.data.node,e);var r=t.data.node.id;return this.crossOriginIframeRootIdMap.set(e,r),this.patchRootIdOnNode(t.data.node,r),{timestamp:t.timestamp,type:ey.IncrementalSnapshot,data:{source:eg.Mutation,adds:[{parentId:this.mirror.getId(e),nextId:null,node:t.data.node}],removes:[],texts:[],attributes:[],isAttachIframe:!0}};case ey.Meta:case ey.Load:case ey.DomContentLoaded:break;case ey.Plugin:return t;case ey.Custom:return this.replaceIds(t.data.payload,e,["id","parentId","previousId","nextId"]),t;case ey.IncrementalSnapshot:switch(t.data.source){case eg.Mutation:return t.data.adds.forEach(function(t){n.replaceIds(t,e,["parentId","nextId","previousId"]),n.replaceIdOnNode(t.node,e);var r=n.crossOriginIframeRootIdMap.get(e);r&&n.patchRootIdOnNode(t.node,r)}),t.data.removes.forEach(function(t){n.replaceIds(t,e,["parentId","id"])}),t.data.attributes.forEach(function(t){n.replaceIds(t,e,["id"])}),t.data.texts.forEach(function(t){n.replaceIds(t,e,["id"])}),t;case eg.Drag:case eg.TouchMove:case eg.MouseMove:return t.data.positions.forEach(function(t){n.replaceIds(t,e,["id"])}),t;case eg.ViewportResize:return!1;case eg.MediaInteraction:case eg.MouseInteraction:case eg.Scroll:case eg.CanvasMutation:case eg.Input:return this.replaceIds(t.data,e,["id"]),t;case eg.StyleSheetRule:case eg.StyleDeclaration:return this.replaceIds(t.data,e,["id"]),this.replaceStyleIds(t.data,e,["styleId"]),t;case eg.Font:return t;case eg.Selection:return t.data.ranges.forEach(function(t){n.replaceIds(t,e,["start","end"])}),t;case eg.AdoptedStyleSheet:return this.replaceIds(t.data,e,["id"]),this.replaceStyleIds(t.data,e,["styleIds"]),_optionalChain$1([t,"access",function(e){return e.data},"access",function(e){return e.styles},"optionalAccess",function(e){return e.forEach},"call",function(t){return t(function(t){n.replaceStyleIds(t,e,["styleId"])})}]),t}}return!1}},{key:"replace",value:function(e,t,n,r){var a=!0,o=!1,i=void 0;try{for(var s,l=r[Symbol.iterator]();!(a=(s=l.next()).done);a=!0){var c=s.value;(Array.isArray(t[c])||"number"==typeof t[c])&&(Array.isArray(t[c])?t[c]=e.getIds(n,t[c]):t[c]=e.getId(n,t[c]))}}catch(e){o=!0,i=e}finally{try{a||null==l.return||l.return()}finally{if(o)throw i}}return t}},{key:"replaceIds",value:function(e,t,n){return this.replace(this.crossOriginIframeMirror,e,t,n)}},{key:"replaceStyleIds",value:function(e,t,n){return this.replace(this.crossOriginIframeStyleMirror,e,t,n)}},{key:"replaceIdOnNode",value:function(e,t){var n=this;this.replaceIds(e,t,["id","rootId"]),"childNodes"in e&&e.childNodes.forEach(function(e){n.replaceIdOnNode(e,t)})}},{key:"patchRootIdOnNode",value:function(e,t){var n=this;e.type===c.Document||e.rootId||(e.rootId=t),"childNodes"in e&&e.childNodes.forEach(function(e){n.patchRootIdOnNode(e,t)})}}]),IframeManager}(),ex=function(){function ShadowDomManagerNoop(){(0,g._)(this,ShadowDomManagerNoop)}return(0,k._)(ShadowDomManagerNoop,[{key:"init",value:function(){}},{key:"addShadowRoot",value:function(){}},{key:"observeAttachShadow",value:function(){}},{key:"reset",value:function(){}}]),ShadowDomManagerNoop}(),eR=function(){function ShadowDomManager(e){(0,g._)(this,ShadowDomManager),this.shadowDoms=new WeakSet,this.restoreHandlers=[],this.mutationCb=e.mutationCb,this.scrollCb=e.scrollCb,this.bypassOptions=e.bypassOptions,this.mirror=e.mirror,this.init()}return(0,k._)(ShadowDomManager,[{key:"init",value:function(){this.reset(),this.patchAttachShadow(Element,document)}},{key:"addShadowRoot",value:function(e,t){var n=this;if(isNativeShadowDom(e)&&!this.shadowDoms.has(e)){this.shadowDoms.add(e);var r=initMutationObserver((0,b._)((0,_._)({},this.bypassOptions),{doc:t,mutationCb:this.mutationCb,mirror:this.mirror,shadowDomManager:this}),e);this.restoreHandlers.push(function(){return r.disconnect()}),this.restoreHandlers.push(initScrollObserver((0,b._)((0,_._)({},this.bypassOptions),{scrollCb:this.scrollCb,doc:e,mirror:this.mirror}))),setTimeout$1(function(){e.adoptedStyleSheets&&e.adoptedStyleSheets.length>0&&n.bypassOptions.stylesheetManager.adoptStyleSheets(e.adoptedStyleSheets,n.mirror.getId(e.host)),n.restoreHandlers.push(initAdoptedStyleSheetObserver({mirror:n.mirror,stylesheetManager:n.bypassOptions.stylesheetManager},e))},0)}}},{key:"observeAttachShadow",value:function(e){e.contentWindow&&e.contentDocument&&this.patchAttachShadow(e.contentWindow.Element,e.contentDocument)}},{key:"patchAttachShadow",value:function(e,t){var n=this;this.restoreHandlers.push(patch(e.prototype,"attachShadow",function(e){return function(r){var a=e.call(this,r);return this.shadowRoot&&inDom(this)&&n.addShadowRoot(this.shadowRoot,t),a}}))}},{key:"reset",value:function(){this.restoreHandlers.forEach(function(e){try{e()}catch(e){}}),this.restoreHandlers=[],this.shadowDoms=new WeakSet}}]),ShadowDomManager}(),eA=function(){function CanvasManagerNoop(){(0,g._)(this,CanvasManagerNoop)}return(0,k._)(CanvasManagerNoop,[{key:"reset",value:function(){}},{key:"freeze",value:function(){}},{key:"unfreeze",value:function(){}},{key:"lock",value:function(){}},{key:"unlock",value:function(){}},{key:"snapshot",value:function(){}}]),CanvasManagerNoop}(),eO=function(){function StylesheetManager(e){(0,g._)(this,StylesheetManager),this.trackedLinkElements=new WeakSet,this.styleMirror=new em,this.mutationCb=e.mutationCb,this.adoptedStyleSheetCb=e.adoptedStyleSheetCb}return(0,k._)(StylesheetManager,[{key:"attachLinkElement",value:function(e,t){"_cssText"in t.attributes&&this.mutationCb({adds:[],removes:[],texts:[],attributes:[{id:t.id,attributes:t.attributes}]}),this.trackLinkElement(e)}},{key:"trackLinkElement",value:function(e){this.trackedLinkElements.has(e)||(this.trackedLinkElements.add(e),this.trackStylesheetInLinkElement(e))}},{key:"adoptStyleSheets",value:function(e,t){if(0!==e.length){var n={id:t,styleIds:[]},r=[],a=!0,o=!1,i=void 0;try{for(var s,l=e[Symbol.iterator]();!(a=(s=l.next()).done);a=!0){var c=s.value,u=void 0;this.styleMirror.has(c)?u=this.styleMirror.getId(c):(u=this.styleMirror.add(c),r.push({styleId:u,rules:Array.from(c.rules||CSSRule,function(e,t){return{rule:stringifyRule(e),index:t}})})),n.styleIds.push(u)}}catch(e){o=!0,i=e}finally{try{a||null==l.return||l.return()}finally{if(o)throw i}}r.length>0&&(n.styles=r),this.adoptedStyleSheetCb(n)}}},{key:"reset",value:function(){this.styleMirror.reset(),this.trackedLinkElements=new WeakSet}},{key:"trackStylesheetInLinkElement",value:function(e){}}]),StylesheetManager}(),eD=function(){function ProcessedNodeManager(){(0,g._)(this,ProcessedNodeManager),this.nodeMap=new WeakMap,this.loop=!0,this.periodicallyClear()}return(0,k._)(ProcessedNodeManager,[{key:"periodicallyClear",value:function(){var e=this;!function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];getImplementation("requestAnimationFrame").apply(this,(0,I._)(t))}(function(){e.clear(),e.loop&&e.periodicallyClear()})}},{key:"inOtherBuffer",value:function(e,t){var n=this.nodeMap.get(e);return n&&Array.from(n).some(function(e){return e!==t})}},{key:"add",value:function(e,t){this.nodeMap.set(e,(this.nodeMap.get(e)||new Set).add(t))}},{key:"clear",value:function(){this.nodeMap=new WeakMap}},{key:"destroy",value:function(){this.loop=!1}}]),ProcessedNodeManager}(),eN=new en;function record(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.emit,n=e.checkoutEveryNms,r=e.checkoutEveryNth,a=e.blockClass,o=void 0===a?"rr-block":a,i=e.blockSelector,s=void 0===i?null:i,l=e.unblockSelector,c=void 0===l?null:l,u=e.ignoreClass,d=void 0===u?"rr-ignore":u,m=e.ignoreSelector,v=void 0===m?null:m,y=e.maskAllText,g=void 0!==y&&y,k=e.maskTextClass,S=void 0===k?"rr-mask":k,b=e.unmaskTextClass,w=void 0===b?null:b,I=e.maskTextSelector,E=void 0===I?null:I,C=e.unmaskTextSelector,T=void 0===C?null:C,M=e.inlineStylesheet,R=void 0===M||M,A=e.maskAllInputs,O=e.maskInputOptions,D=e.slimDOMOptions,N=e.maskAttributeFn,L=e.maskInputFn,B=e.maskTextFn,F=e.maxCanvasSize,P=e.packFn,W=e.sampling,z=void 0===W?{}:W,U=e.dataURLOptions,H=void 0===U?{}:U,j=e.mousemoveWait,q=e.recordCanvas,$=void 0!==q&&q,J=e.recordCrossOriginIframes,K=void 0!==J&&J,V=e.recordAfter,Y=void 0===V?"DOMContentLoaded"===e.recordAfter?e.recordAfter:"load":V,X=e.userTriggeredOnInput,G=void 0!==X&&X,Q=e.collectFonts,Z=void 0!==Q&&Q,ee=e.inlineImages,et=void 0!==ee&&ee,er=e.plugins,ea=e.keepIframeSrcFn,eo=void 0===ea?function(){return!1}:ea,ei=e.ignoreCSSAttributes,es=void 0===ei?new Set([]):ei,el=e.errorHandler,ec=e.onMutation,eu=e.getCanvasManager;p=el;var ed=!K||window.parent===window,ep=!1;if(!ed)try{window.parent.document&&(ep=!1)}catch(e){ep=!0}if(ed&&!t)throw Error("emit function is required");void 0!==j&&void 0===z.mousemove&&(z.mousemove=j),eN.reset();var eh=!0===A?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0,radio:!0,checkbox:!0}:void 0!==O?O:{},em=!0===D||"all"===D?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaVerification:!0,headMetaAuthorship:"all"===D,headMetaDescKeywords:"all"===D}:D||{};!function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window,t=this;"NodeList"in e&&!e.NodeList.prototype.forEach&&(e.NodeList.prototype.forEach=Array.prototype.forEach),"DOMTokenList"in e&&!e.DOMTokenList.prototype.forEach&&(e.DOMTokenList.prototype.forEach=Array.prototype.forEach),Node.prototype.contains||(Node.prototype.contains=function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];var a=n[0];if(!(0 in n))throw TypeError("1 argument is required");do if(t===a)return!0;while(a=a&&a.parentNode);return!1})}();var ev=0,eventProcessor=function(e){var t=!0,n=!1,r=void 0;try{for(var a,o=(er||[])[Symbol.iterator]();!(t=(a=o.next()).done);t=!0){var i=a.value;i.eventProcessor&&(e=i.eventProcessor(e))}}catch(e){n=!0,r=e}finally{try{t||null==o.return||o.return()}finally{if(n)throw r}}return P&&!ep&&(e=P(e)),e};h=function(e,a){if(e.timestamp=ef(),(0,x.x)([ew,"access",function(e){return e[0]},"optionalAccess",function(e){return e.isFrozen},"call",function(e){return e()}])&&e.type!==ey.FullSnapshot&&!(e.type===ey.IncrementalSnapshot&&e.data.source===eg.Mutation)&&ew.forEach(function(e){return e.unfreeze()}),ed)(0,x.x)([t,"optionalCall",function(t){return t(eventProcessor(e),a)}]);else if(ep){var o={type:"rrweb",event:eventProcessor(e),origin:window.location.origin,isCheckout:a};window.parent.postMessage(o,"*")}if(e.type===ey.FullSnapshot)eE=e,ev=0;else if(e.type===ey.IncrementalSnapshot){if(e.data.source===eg.Mutation&&e.data.isAttachIframe)return;ev++;var i=r&&ev>=r,s=n&&eE&&e.timestamp-eE.timestamp>n;(i||s)&&_$takeFullSnapshot(!0)}};var wrappedMutationEmit=function(e){h({type:ey.IncrementalSnapshot,data:(0,_._)({source:eg.Mutation},e)})},wrappedScrollEmit=function(e){return h({type:ey.IncrementalSnapshot,data:(0,_._)({source:eg.Scroll},e)})},wrappedCanvasMutationEmit=function(e){return h({type:ey.IncrementalSnapshot,data:(0,_._)({source:eg.CanvasMutation},e)})},ek=new eO({mutationCb:wrappedMutationEmit,adoptedStyleSheetCb:function(e){return h({type:ey.IncrementalSnapshot,data:(0,_._)({source:eg.AdoptedStyleSheet},e)})}}),eS="boolean"==typeof __RRWEB_EXCLUDE_IFRAME__&&__RRWEB_EXCLUDE_IFRAME__?new eT:new eM({mirror:eN,mutationCb:wrappedMutationEmit,stylesheetManager:ek,recordCrossOriginIframes:K,wrappedEmit:h}),e_=!0,eb=!1,eI=void 0;try{for(var eE,eC,eL=(er||[])[Symbol.iterator]();!(e_=(eC=eL.next()).done);e_=!0){var eB=eC.value;eB.getMirror&&eB.getMirror({nodeMirror:eN,crossOriginIframeMirror:eS.crossOriginIframeMirror,crossOriginIframeStyleMirror:eS.crossOriginIframeStyleMirror})}}catch(e){eb=!0,eI=e}finally{try{e_||null==eL.return||eL.return()}finally{if(eb)throw eI}}var eF=new eD,eP=function(e,t){try{return e?e(t):new eA}catch(e){return console.warn("Unable to initialize CanvasManager"),new eA}}(eu,{mirror:eN,win:window,mutationCb:function(e){return h({type:ey.IncrementalSnapshot,data:(0,_._)({source:eg.CanvasMutation},e)})},recordCanvas:$,blockClass:o,blockSelector:s,unblockSelector:c,maxCanvasSize:void 0===F?null:F,sampling:z.canvas,dataURLOptions:H,errorHandler:el}),eW="boolean"==typeof __RRWEB_EXCLUDE_SHADOW_DOM__&&__RRWEB_EXCLUDE_SHADOW_DOM__?new ex:new eR({mutationCb:wrappedMutationEmit,scrollCb:wrappedScrollEmit,bypassOptions:{onMutation:ec,blockClass:o,blockSelector:s,unblockSelector:c,maskAllText:g,maskTextClass:S,unmaskTextClass:w,maskTextSelector:E,unmaskTextSelector:T,inlineStylesheet:R,maskInputOptions:eh,dataURLOptions:H,maskAttributeFn:N,maskTextFn:B,maskInputFn:L,recordCanvas:$,inlineImages:et,sampling:z,slimDOMOptions:em,iframeManager:eS,stylesheetManager:ek,canvasManager:eP,keepIframeSrcFn:eo,processedNodeManager:eF},mirror:eN}),_$takeFullSnapshot=function(){var e,t,n,r,a,i,l,u,d,p,f,m,v,y,k,_,b,I,C,M,x,A,O,D,F,P,W,z,U=arguments.length>0&&void 0!==arguments[0]&&arguments[0];h({type:ey.Meta,data:{href:window.location.href,width:getWindowWidth(),height:getWindowHeight()}},U),ek.reset(),eW.init(),ew.forEach(function(e){return e.lock()});var j=(e=document,r=void 0===(n=(t={mirror:eN,blockClass:o,blockSelector:s,unblockSelector:c,maskAllText:g,maskTextClass:S,unmaskTextClass:w,maskTextSelector:E,unmaskTextSelector:T,inlineStylesheet:R,maskAllInputs:eh,maskAttributeFn:N,maskInputFn:L,maskTextFn:B,slimDOM:em,dataURLOptions:H,recordCanvas:$,inlineImages:et,onSerialize:function(e){isSerializedIframe(e,eN)&&eS.addIframe(e),isSerializedStylesheet(e,eN)&&ek.trackLinkElement(e),hasShadowRoot(e)&&eW.addShadowRoot(e.shadowRoot,document)},onIframeLoad:function(e,t){eS.attachIframe(e,t),eW.observeAttachShadow(e)},onStylesheetLoad:function(e,t){ek.attachLinkElement(e,t)},keepIframeSrcFn:eo}).mirror)?new en:n,a=t.blockClass,i=t.blockSelector,l=t.unblockSelector,u=t.maskAllText,d=t.maskTextClass,p=t.unmaskTextClass,f=t.maskTextSelector,m=t.unmaskTextSelector,v=t.inlineStylesheet,y=t.inlineImages,k=t.recordCanvas,b=void 0!==(_=t.maskAllInputs)&&_,I=t.maskAttributeFn,C=t.maskTextFn,M=t.maskInputFn,A=void 0!==(x=t.slimDOM)&&x,O=t.dataURLOptions,D=t.preserveWhiteSpace,F=t.onSerialize,P=t.onIframeLoad,W=t.iframeLoadTimeout,serializeNodeWithId(e,{doc:e,mirror:r,blockClass:void 0===a?"rr-block":a,blockSelector:void 0===i?null:i,unblockSelector:void 0===l?null:l,maskAllText:void 0!==u&&u,maskTextClass:void 0===d?"rr-mask":d,unmaskTextClass:void 0===p?null:p,maskTextSelector:void 0===f?null:f,unmaskTextSelector:void 0===m?null:m,skipChild:!1,inlineStylesheet:void 0===v||v,maskInputOptions:!0===b?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0}:!1===b?{}:b,maskAttributeFn:I,maskTextFn:C,maskInputFn:M,slimDOMOptions:!0===A||"all"===A?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaDescKeywords:"all"===A,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaAuthorship:!0,headMetaVerification:!0}:!1===A?{}:A,dataURLOptions:O,inlineImages:void 0!==y&&y,recordCanvas:void 0!==k&&k,preserveWhiteSpace:D,onSerialize:F,onIframeLoad:P,iframeLoadTimeout:W,onStylesheetLoad:t.onStylesheetLoad,stylesheetLoadTimeout:t.stylesheetLoadTimeout,keepIframeSrcFn:void 0===(z=t.keepIframeSrcFn)?function(){return!1}:z,newlyAddedElement:!1}));if(!j)return console.warn("Failed to snapshot the document");h({type:ey.FullSnapshot,data:{node:j,initialOffset:getWindowScroll(window)}}),ew.forEach(function(e){return e.unlock()}),document.adoptedStyleSheets&&document.adoptedStyleSheets.length>0&&ek.adoptStyleSheets(document.adoptedStyleSheets,eN.getId(document))};f=_$takeFullSnapshot;try{var ez=[],observe=function(e){return callbackWrapper(initObservers)({onMutation:ec,mutationCb:wrappedMutationEmit,mousemoveCb:function(e,t){return h({type:ey.IncrementalSnapshot,data:{source:t,positions:e}})},mouseInteractionCb:function(e){return h({type:ey.IncrementalSnapshot,data:(0,_._)({source:eg.MouseInteraction},e)})},scrollCb:wrappedScrollEmit,viewportResizeCb:function(e){return h({type:ey.IncrementalSnapshot,data:(0,_._)({source:eg.ViewportResize},e)})},inputCb:function(e){return h({type:ey.IncrementalSnapshot,data:(0,_._)({source:eg.Input},e)})},mediaInteractionCb:function(e){return h({type:ey.IncrementalSnapshot,data:(0,_._)({source:eg.MediaInteraction},e)})},styleSheetRuleCb:function(e){return h({type:ey.IncrementalSnapshot,data:(0,_._)({source:eg.StyleSheetRule},e)})},styleDeclarationCb:function(e){return h({type:ey.IncrementalSnapshot,data:(0,_._)({source:eg.StyleDeclaration},e)})},canvasMutationCb:wrappedCanvasMutationEmit,fontCb:function(e){return h({type:ey.IncrementalSnapshot,data:(0,_._)({source:eg.Font},e)})},selectionCb:function(e){h({type:ey.IncrementalSnapshot,data:(0,_._)({source:eg.Selection},e)})},customElementCb:function(e){h({type:ey.IncrementalSnapshot,data:(0,_._)({source:eg.CustomElement},e)})},blockClass:o,ignoreClass:d,ignoreSelector:v,maskAllText:g,maskTextClass:S,unmaskTextClass:w,maskTextSelector:E,unmaskTextSelector:T,maskInputOptions:eh,inlineStylesheet:R,sampling:z,recordCanvas:$,inlineImages:et,userTriggeredOnInput:G,collectFonts:Z,doc:e,maskAttributeFn:N,maskInputFn:L,maskTextFn:B,keepIframeSrcFn:eo,blockSelector:s,unblockSelector:c,slimDOMOptions:em,dataURLOptions:H,mirror:eN,iframeManager:eS,stylesheetManager:ek,shadowDomManager:eW,processedNodeManager:eF,canvasManager:eP,ignoreCSSAttributes:es,plugins:(0,x.x)([er,"optionalAccess",function(e){return e.filter},"call",function(e){return e(function(e){return e.observer})},"optionalAccess",function(e){return e.map},"call",function(e){return e(function(e){return{observer:e.observer,options:e.options,callback:function(t){return h({type:ey.Plugin,data:{plugin:e.name,payload:t}})}}})}])||[]},{})};eS.addLoadListener(function(e){try{ez.push(observe(e.contentDocument))}catch(e){console.warn(e)}});var init=function(){_$takeFullSnapshot(),ez.push(observe(document))};return"interactive"===document.readyState||"complete"===document.readyState?init():(ez.push(on("DOMContentLoaded",function(){h({type:ey.DomContentLoaded,data:{}}),"DOMContentLoaded"===Y&&init()})),ez.push(on("load",function(){h({type:ey.Load,data:{}}),"load"===Y&&init()},window))),function(){ez.forEach(function(e){return e()}),eF.destroy(),f=void 0,p=void 0}}catch(e){console.warn(e)}}function timestampToMs(e){return e>9999999999?e:1e3*e}function timestampToS(e){return e>9999999999?e/1e3:e}function addBreadcrumbEvent(e,t){"sentry.transaction"!==t.category&&(["ui.click","ui.input"].includes(t.category)?e.triggerUserActivity():e.checkAndHandleExpiredSession(),e.addUpdate(function(){return e.throttledAddEvent({type:ey.Custom,timestamp:1e3*(t.timestamp||0),data:{tag:"breadcrumb",payload:(0,P.Fv)(t,10,1e3)}}),"console"===t.category}))}function getClosestInteractive(e){return e.closest("button,a")||e}function getClickTargetNode(e){var t=getTargetNode(e);return t&&t instanceof Element?getClosestInteractive(t):t}function getTargetNode(e){return"object"==typeof e&&e&&"target"in e?e.target:e}record.mirror=eN,record.takeFullSnapshot=function(e){if(!f)throw Error("please take full snapshot after start recording");f(e)};var eL=function(){function ClickDetector(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:addBreadcrumbEvent;(0,g._)(this,ClickDetector),this._lastMutation=0,this._lastScroll=0,this._clicks=[],this._timeout=t.timeout/1e3,this._threshold=t.threshold/1e3,this._scollTimeout=t.scrollTimeout/1e3,this._replay=e,this._ignoreSelector=t.ignoreSelector,this._addBreadcrumbEvent=n}return(0,k._)(ClickDetector,[{key:"addListeners",value:function(){var e,t=this,n=(e=function(){t._lastMutation=nowInSeconds()},m||(m=[],(0,W.hl)(Z,"open",function(e){return function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];if(m)try{m.forEach(function(e){return e()})}catch(e){}return e.apply(Z,n)}})),m.push(e),function(){var t=m?m.indexOf(e):-1;t>-1&&m.splice(t,1)});this._teardown=function(){n(),t._clicks=[],t._lastMutation=0,t._lastScroll=0}}},{key:"removeListeners",value:function(){this._teardown&&this._teardown(),this._checkClickTimeout&&clearTimeout(this._checkClickTimeout)}},{key:"handleClick",value:function(e,t){if(n=this._ignoreSelector,!(!eB.includes(t.tagName)||"INPUT"===t.tagName&&!["submit","button"].includes(t.getAttribute("type")||"")||"A"===t.tagName&&(t.hasAttribute("download")||t.hasAttribute("target")&&"_self"!==t.getAttribute("target"))||n&&t.matches(n))&&e.data&&"number"==typeof e.data.nodeId&&e.timestamp){var n,r={timestamp:timestampToS(e.timestamp),clickBreadcrumb:e,clickCount:0,node:t};this._clicks.some(function(e){return e.node===r.node&&1>Math.abs(e.timestamp-r.timestamp)})||(this._clicks.push(r),1===this._clicks.length&&this._scheduleCheckClicks())}}},{key:"registerMutation",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Date.now();this._lastMutation=timestampToS(e)}},{key:"registerScroll",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Date.now();this._lastScroll=timestampToS(e)}},{key:"registerClick",value:function(e){var t=getClosestInteractive(e);this._handleMultiClick(t)}},{key:"_handleMultiClick",value:function(e){this._getClicks(e).forEach(function(e){e.clickCount++})}},{key:"_getClicks",value:function(e){return this._clicks.filter(function(t){return t.node===e})}},{key:"_checkClicks",value:function(){var e=this,t=[],n=nowInSeconds();this._clicks.forEach(function(r){!r.mutationAfter&&e._lastMutation&&(r.mutationAfter=r.timestamp<=e._lastMutation?e._lastMutation-r.timestamp:void 0),!r.scrollAfter&&e._lastScroll&&(r.scrollAfter=r.timestamp<=e._lastScroll?e._lastScroll-r.timestamp:void 0),r.timestamp+e._timeout<=n&&t.push(r)});var r=!0,a=!1,o=void 0;try{for(var i,s=t[Symbol.iterator]();!(r=(i=s.next()).done);r=!0){var l=i.value,c=this._clicks.indexOf(l);c>-1&&(this._generateBreadcrumbs(l),this._clicks.splice(c,1))}}catch(e){a=!0,o=e}finally{try{r||null==s.return||s.return()}finally{if(a)throw o}}this._clicks.length&&this._scheduleCheckClicks()}},{key:"_generateBreadcrumbs",value:function(e){var t=this._replay,n=e.scrollAfter&&e.scrollAfter<=this._scollTimeout,r=e.mutationAfter&&e.mutationAfter<=this._threshold,a=e.clickCount,o=e.clickBreadcrumb;if(!n&&!r){var i=1e3*Math.min(e.mutationAfter||this._timeout,this._timeout),s=i<1e3*this._timeout?"mutation":"timeout",l={type:"default",message:o.message,timestamp:o.timestamp,category:"ui.slowClickDetected",data:(0,b._)((0,_._)({},o.data),{url:Z.location.href,route:t.getCurrentRoute(),timeAfterClickMs:i,endReason:s,clickCount:a||1})};this._addBreadcrumbEvent(t,l);return}if(a>1){var c={type:"default",message:o.message,timestamp:o.timestamp,category:"ui.multiClick",data:(0,b._)((0,_._)({},o.data),{url:Z.location.href,route:t.getCurrentRoute(),clickCount:a,metric:!0})};this._addBreadcrumbEvent(t,c)}}},{key:"_scheduleCheckClicks",value:function(){var e=this;this._checkClickTimeout&&clearTimeout(this._checkClickTimeout),this._checkClickTimeout=setTimeout(function(){return e._checkClicks()},1e3)}}]),ClickDetector}(),eB=["A","BUTTON","INPUT"];function nowInSeconds(){return Date.now()/1e3}function createBreadcrumb(e){return(0,_._)({timestamp:Date.now()/1e3,type:"default"},e)}(l=v||(v={}))[l.Document=0]="Document",l[l.DocumentType=1]="DocumentType",l[l.Element=2]="Element",l[l.Text=3]="Text",l[l.CDATA=4]="CDATA",l[l.Comment=5]="Comment";var eF=new Set(["id","class","aria-label","role","name","alt","title","data-test-id","data-testid","disabled","aria-disabled","data-sentry-component"]);function getBaseDomBreadcrumb(e,t){var n=record.mirror.getId(e),r=n&&record.mirror.getNode(n),a=r&&record.mirror.getMeta(r),o=a&&a.type===v.Element?a:null;return{message:t,data:o?{nodeId:n,node:{id:n,tagName:o.tagName,textContent:Array.from(o.childNodes).map(function(e){return e.type===v.Text&&e.textContent}).filter(Boolean).map(function(e){return e.trim()}).join(""),attributes:function(e){var t={};for(var n in e)if(eF.has(n)){var r=n;("data-testid"===n||"data-test-id"===n)&&(r="testId"),t[r]=e[n]}return t}(o.attributes)}}:{}}}var eP={resource:function(e){var t=e.entryType,n=e.initiatorType,r=e.name,a=e.responseEnd,o=e.startTime,i=e.decodedBodySize,s=e.encodedBodySize,l=e.responseStatus,c=e.transferSize;return["fetch","xmlhttprequest"].includes(n)?null:{type:"".concat(t,".").concat(n),start:getAbsoluteTime(o),end:getAbsoluteTime(a),name:r,data:{size:c,statusCode:l,decodedBodySize:i,encodedBodySize:s}}},paint:function(e){var t=e.duration,n=e.entryType,r=e.name,a=getAbsoluteTime(e.startTime);return{type:n,name:r,start:a,end:a+t,data:void 0}},navigation:function(e){var t=e.entryType,n=e.name,r=e.decodedBodySize,a=e.duration,o=e.domComplete,i=e.encodedBodySize,s=e.domContentLoadedEventStart,l=e.domContentLoadedEventEnd,c=e.domInteractive,u=e.loadEventStart,d=e.loadEventEnd,p=e.redirectCount,h=e.startTime,f=e.transferSize,m=e.type;return 0===a?null:{type:"".concat(t,".").concat(m),start:getAbsoluteTime(h),end:getAbsoluteTime(o),name:n,data:{size:f,decodedBodySize:r,encodedBodySize:i,duration:a,domInteractive:c,domContentLoadedEventStart:s,domContentLoadedEventEnd:l,loadEventStart:u,loadEventEnd:d,domComplete:o,redirectCount:p}}}};function createPerformanceEntry(e){return eP[e.entryType]?eP[e.entryType](e):null}function getAbsoluteTime(e){return((U.Z1||Z.performance.timeOrigin)+e)/1e3}var eW=function(e){(0,S._)(EventBufferSizeExceededError,e);var t=(0,C._)(EventBufferSizeExceededError);function EventBufferSizeExceededError(){return(0,g._)(this,EventBufferSizeExceededError),t.call(this,"Event buffer exceeded maximum size of ".concat(2e7,"."))}return EventBufferSizeExceededError}((0,E._)(Error)),ez=function(){function EventBufferArray(){(0,g._)(this,EventBufferArray),this.events=[],this._totalSize=0,this.hasCheckout=!1}return(0,k._)(EventBufferArray,[{key:"hasEvents",get:function(){return this.events.length>0}},{key:"type",get:function(){return"sync"}},{key:"destroy",value:function(){this.events=[]}},{key:"addEvent",value:function(e){var t=this;return(0,y._)(function(){var n;return(0,T.Jh)(this,function(r){if(n=JSON.stringify(e).length,t._totalSize+=n,t._totalSize>2e7)throw new eW;return t.events.push(e),[2]})})()}},{key:"finish",value:function(){var e=this;return new Promise(function(t){var n=e.events;e.clear(),t(JSON.stringify(n))})}},{key:"clear",value:function(){this.events=[],this._totalSize=0,this.hasCheckout=!1}},{key:"getEarliestTimestamp",value:function(){var e=this.events.map(function(e){return e.timestamp}).sort()[0];return e?timestampToMs(e):null}}]),EventBufferArray}(),eU=function(){function WorkerHandler(e){(0,g._)(this,WorkerHandler),this._worker=e,this._id=0}return(0,k._)(WorkerHandler,[{key:"ensureReady",value:function(){var e=this;return this._ensureReadyPromise||(this._ensureReadyPromise=new Promise(function(t,n){e._worker.addEventListener("message",function(e){e.data.success?t():n()},{once:!0}),e._worker.addEventListener("error",function(e){n(e)},{once:!0})})),this._ensureReadyPromise}},{key:"destroy",value:function(){this._worker.terminate()}},{key:"postMessage",value:function(e,t){var n=this,r=this._getAndIncrementId();return new Promise(function(a,o){var listener=function(t){var i=t.data;if(i.method===e&&i.id===r){if(n._worker.removeEventListener("message",listener),!i.success){o(Error("Error in compression worker"));return}a(i.response)}};n._worker.addEventListener("message",listener),n._worker.postMessage({id:r,method:e,arg:t})})}},{key:"_getAndIncrementId",value:function(){return this._id++}}]),WorkerHandler}(),eH=function(){function EventBufferCompressionWorker(e){(0,g._)(this,EventBufferCompressionWorker),this._worker=new eU(e),this._earliestTimestamp=null,this._totalSize=0,this.hasCheckout=!1}return(0,k._)(EventBufferCompressionWorker,[{key:"hasEvents",get:function(){return!!this._earliestTimestamp}},{key:"type",get:function(){return"worker"}},{key:"ensureReady",value:function(){return this._worker.ensureReady()}},{key:"destroy",value:function(){this._worker.destroy()}},{key:"addEvent",value:function(e){var t=timestampToMs(e.timestamp);(!this._earliestTimestamp||t<this._earliestTimestamp)&&(this._earliestTimestamp=t);var n=JSON.stringify(e);return(this._totalSize+=n.length,this._totalSize>2e7)?Promise.reject(new eW):this._sendEventToWorker(n)}},{key:"finish",value:function(){return this._finishRequest()}},{key:"clear",value:function(){this._earliestTimestamp=null,this._totalSize=0,this.hasCheckout=!1,this._worker.postMessage("clear").then(null,function(e){})}},{key:"getEarliestTimestamp",value:function(){return this._earliestTimestamp}},{key:"_sendEventToWorker",value:function(e){return this._worker.postMessage("addEvent",e)}},{key:"_finishRequest",value:function(){var e=this;return(0,y._)(function(){var t;return(0,T.Jh)(this,function(n){switch(n.label){case 0:return[4,e._worker.postMessage("finish")];case 1:return t=n.sent(),e._earliestTimestamp=null,e._totalSize=0,[2,t]}})})()}}]),EventBufferCompressionWorker}(),ej=function(){function EventBufferProxy(e){(0,g._)(this,EventBufferProxy),this._fallback=new ez,this._compression=new eH(e),this._used=this._fallback,this._ensureWorkerIsLoadedPromise=this._ensureWorkerIsLoaded()}return(0,k._)(EventBufferProxy,[{key:"type",get:function(){return this._used.type}},{key:"hasEvents",get:function(){return this._used.hasEvents}},{key:"hasCheckout",get:function(){return this._used.hasCheckout},set:function(e){this._used.hasCheckout=e}},{key:"destroy",value:function(){this._fallback.destroy(),this._compression.destroy()}},{key:"clear",value:function(){return this._used.clear()}},{key:"getEarliestTimestamp",value:function(){return this._used.getEarliestTimestamp()}},{key:"addEvent",value:function(e){return this._used.addEvent(e)}},{key:"finish",value:function(){var e=this;return(0,y._)(function(){return(0,T.Jh)(this,function(t){switch(t.label){case 0:return[4,e.ensureWorkerIsLoaded()];case 1:return t.sent(),[2,e._used.finish()]}})})()}},{key:"ensureWorkerIsLoaded",value:function(){return this._ensureWorkerIsLoadedPromise}},{key:"_ensureWorkerIsLoaded",value:function(){var e=this;return(0,y._)(function(){return(0,T.Jh)(this,function(t){switch(t.label){case 0:return t.trys.push([0,2,,3]),[4,e._compression.ensureReady()];case 1:return t.sent(),[3,3];case 2:case 4:return t.sent(),[2];case 3:return[4,e._switchToCompressionWorker()]}})})()}},{key:"_switchToCompressionWorker",value:function(){var e=this;return(0,y._)(function(){var t,n,r,a,o,i,s,l,c,u;return(0,T.Jh)(this,function(d){switch(d.label){case 0:n=(t=e._fallback).events,r=t.hasCheckout,a=[],o=!0,i=!1,s=void 0;try{for(l=n[Symbol.iterator]();!(o=(c=l.next()).done);o=!0)u=c.value,a.push(e._compression.addEvent(u))}catch(e){i=!0,s=e}finally{try{o||null==l.return||l.return()}finally{if(i)throw s}}e._compression.hasCheckout=r,e._used=e._compression,d.label=1;case 1:return d.trys.push([1,3,,4]),[4,Promise.all(a)];case 2:case 3:return d.sent(),[3,4];case 4:return[2]}})})()}}]),EventBufferProxy}();function hasSessionStorage(){try{return"sessionStorage"in Z&&!!Z.sessionStorage}catch(e){return!1}}function isSampled(e){return void 0!==e&&Math.random()<e}function makeSession(e){var t=Date.now();return{id:e.id||(0,j.DM)(),started:e.started||t,lastActivity:e.lastActivity||t,segmentId:e.segmentId||0,sampled:e.sampled,previousSessionId:e.previousSessionId}}function saveSession(e){if(hasSessionStorage())try{Z.sessionStorage.setItem(ee,JSON.stringify(e))}catch(e){}}function createSession(e){var t=e.sessionSampleRate,n=e.allowBuffering,r=e.stickySession,a=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).previousSessionId,o=makeSession({sampled:isSampled(t)?"session":!!n&&"buffer",previousSessionId:a});return void 0!==r&&r&&saveSession(o),o}function isExpired(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:+new Date;return null===e||void 0===t||t<0||0!==t&&e+t<=n}function isSessionExpired(e,t){var n=t.maxReplayDuration,r=t.sessionIdleExpire,a=t.targetTime,o=void 0===a?Date.now():a;return isExpired(e.started,n,o)||isExpired(e.lastActivity,r,o)}function shouldRefreshSession(e,t){return!!isSessionExpired(e,{sessionIdleExpire:t.sessionIdleExpire,maxReplayDuration:t.maxReplayDuration})&&("buffer"!==e.sampled||0!==e.segmentId)}function loadOrCreateSession(e,t){var n=e.traceInternals,r=e.sessionIdleExpire,a=e.maxReplayDuration,o=e.previousSessionId,i=t.stickySession&&function(e){if(!hasSessionStorage())return null;try{var t=Z.sessionStorage.getItem(ee);if(!t)return null;var n=JSON.parse(t);return makeSession(n)}catch(e){return null}}(0);return i?shouldRefreshSession(i,{sessionIdleExpire:r,maxReplayDuration:a})?createSession(t,{previousSessionId:i.id}):i:createSession(t,{previousSessionId:o})}function addEventSync(e,t,n){return!!shouldAddEvent(e,t)&&(_addEvent(e,t,n),!0)}function _addEvent(e,t,n){return __addEvent.apply(this,arguments)}function __addEvent(){return(__addEvent=(0,y._)(function(e,t,n){var r,a,o,i;return(0,T.Jh)(this,function(s){switch(s.label){case 0:if(!e.eventBuffer)return[2,null];s.label=1;case 1:if(s.trys.push([1,3,,5]),n&&"buffer"===e.recordingMode&&e.eventBuffer.clear(),n&&(e.eventBuffer.hasCheckout=!0),!(r=function(e,t){try{if("function"==typeof t&&e.type===ey.Custom)return t(e)}catch(e){return null}return e}(t,e.getOptions().beforeAddRecordingEvent)))return[2];return[4,e.eventBuffer.addEvent(r)];case 2:return[2,s.sent()];case 3:return o=(a=s.sent())&&a instanceof eW?"addEventSizeExceeded":"addEvent",[4,e.stop({reason:o})];case 4:return s.sent(),(i=(0,R.s3)())&&i.recordDroppedEvent("internal_sdk_error","replay"),[3,5];case 5:return[2]}})})).apply(this,arguments)}function shouldAddEvent(e,t){if(!e.eventBuffer||e.isPaused()||!e.isEnabled())return!1;var n=timestampToMs(t.timestamp);return!(n+e.timeouts.sessionIdlePause<Date.now())&&(!(n>e.getContext().initialTimestamp+e.getOptions().maxReplayDuration)||(e.getOptions()._experiments.traceInternals,!1))}function isTransactionEvent(e){return"transaction"===e.type}function isFeedbackEvent(e){return"feedback"===e.type}function handleAfterSendEvent(e){var t=function(){var e=(0,R.s3)();if(!e)return!1;var t=e.getTransport();return!!t&&(t.send.__sentry__baseTransport__||!1)}();return function(n,r){if(e.isEnabled()&&(!n.type||isTransactionEvent(n))){var a,o=r&&r.statusCode;if(!t||o&&!(o<200)&&!(o>=300)){if(isTransactionEvent(n)){a=e.getContext(),n.contexts&&n.contexts.trace&&n.contexts.trace.trace_id&&a.traceIds.size<100&&a.traceIds.add(n.contexts.trace.trace_id);return}!function(e,t){var n=e.getContext();if(t.event_id&&n.errorIds.size<100&&n.errorIds.add(t.event_id),"buffer"===e.recordingMode&&t.tags&&t.tags.replayId){var r=e.getOptions().beforeErrorSampling;("function"!=typeof r||r(t))&&setTimeout(function(){e.sendBufferedReplayOrFlush()})}}(e,n)}}}}function createPerformanceSpans(e,t){return t.map(function(t){var n=t.type,r=t.start,a=t.end,o=t.name,i=t.data,s=e.throttledAddEvent({type:ey.Custom,timestamp:r,data:{tag:"performanceSpan",payload:{op:n,description:o,startTimestamp:r,endTimestamp:a,data:i}}});return"string"==typeof s?Promise.resolve(null):s})}function addNetworkBreadcrumb(e,t){var n;e.isEnabled()&&null!==t&&(n=t.name,(0,A.W)(n,(0,R.s3)())||e.addUpdate(function(){return createPerformanceSpans(e,[t]),!0}))}function getBodySize(e,t){if(e)try{if("string"==typeof e)return t.encode(e).length;if(e instanceof URLSearchParams)return t.encode(e.toString()).length;if(e instanceof FormData){var n=_serializeFormData(e);return t.encode(n).length}if(e instanceof Blob)return e.size;if(e instanceof ArrayBuffer)return e.byteLength}catch(e){}}function parseContentLengthHeader(e){if(e){var t=parseInt(e,10);return isNaN(t)?void 0:t}}function getBodyString(e){try{if("string"==typeof e)return[e];if(e instanceof URLSearchParams)return[e.toString()];if(e instanceof FormData)return[_serializeFormData(e)];if(!e)return[void 0]}catch(e){return[void 0,"BODY_PARSE_ERROR"]}return[void 0,"UNPARSEABLE_BODY_TYPE"]}function mergeWarning(e,t){if(!e)return{headers:{},size:void 0,_meta:{warnings:[t]}};var n=(0,_._)({},e._meta),r=n.warnings||[];return n.warnings=(0,I._)(r).concat([t]),e._meta=n,e}function makeNetworkReplayBreadcrumb(e,t){if(!t)return null;var n=t.startTimestamp,r=t.endTimestamp,a=t.url,o=t.method,i=t.statusCode,s=t.request,l=t.response;return{type:e,start:n/1e3,end:r/1e3,name:a,data:(0,W.Jr)({method:o,statusCode:i,request:s,response:l})}}function buildSkippedNetworkRequestOrResponse(e){return{headers:{},size:e,_meta:{warnings:["URL_SKIPPED"]}}}function buildNetworkRequestOrResponse(e,t,n){if(t||0!==Object.keys(e).length){if(!t)return{headers:e};if(!n)return{headers:e,size:t};var r={headers:e,size:t},a=function(e){if(!e||"string"!=typeof e)return{body:e};var t,n,r=e.length>15e4,a=(t=e[0],n=e[e.length-1],"["===t&&"]"===n||"{"===t&&"}"===n);if(r){var o=e.slice(0,15e4);return a?{body:o,warnings:["MAYBE_JSON_TRUNCATED"]}:{body:"".concat(o,"…"),warnings:["TEXT_TRUNCATED"]}}if(a)try{return{body:JSON.parse(e)}}catch(e){}return{body:e}}(n),o=a.body,i=a.warnings;return r.body=o,i&&i.length>0&&(r._meta={warnings:i}),r}}function getAllowedHeaders(e,t){return Object.keys(e).reduce(function(n,r){var a=r.toLowerCase();return t.includes(a)&&e[r]&&(n[a]=e[r]),n},{})}function _serializeFormData(e){return new URLSearchParams(e).toString()}function urlMatches(e,t){var n=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Z.document.baseURI;if(e.startsWith("http://")||e.startsWith("https://")||e.startsWith(Z.location.origin))return e;var n=new URL(e,t);if(n.origin!==new URL(t).origin)return e;var r=n.href;return!e.endsWith("/")&&r.endsWith("/")?r.slice(0,-1):r}(e);return(0,$.U0)(n,t)}function _captureFetchBreadcrumbToReplay(){return(_captureFetchBreadcrumbToReplay=(0,y._)(function(e,t,n){var r;return(0,T.Jh)(this,function(a){switch(a.label){case 0:return a.trys.push([0,2,,3]),[4,function(e,t,n){return __prepareFetchData.apply(this,arguments)}(e,t,n)];case 1:return r=makeNetworkReplayBreadcrumb("resource.fetch",a.sent()),addNetworkBreadcrumb(n.replay,r),[3,3];case 2:return a.sent(),[3,3];case 3:return[2]}})})).apply(this,arguments)}function __prepareFetchData(){return(__prepareFetchData=(0,y._)(function(e,t,n){var r,a,o,i,s,l,c,u,d,p,h,f,m,v,y;return(0,T.Jh)(this,function(g){switch(g.label){case 0:return r=Date.now(),o=void 0===(a=t.startTimestamp)?r:a,s=void 0===(i=t.endTimestamp)?r:i,c=(l=e.data).url,u=l.method,p=void 0===(d=l.status_code)?0:d,h=l.request_body_size,f=l.response_body_size,v=(m=urlMatches(c,n.networkDetailAllowUrls)&&!urlMatches(c,n.networkDetailDenyUrls))?function(e,t,n){var r=e.networkCaptureBodies,a=e.networkRequestHeaders,o=t?1===t.length&&"string"!=typeof t[0]?getHeadersFromOptions(t[0],a):2===t.length?getHeadersFromOptions(t[1],a):{}:{};if(!r)return buildNetworkRequestOrResponse(o,n,void 0);var i=_getFetchRequestArgBody(t),s=(0,w._)(getBodyString(i),2),l=s[0],c=s[1],u=buildNetworkRequestOrResponse(o,n,l);return c?mergeWarning(u,c):u}(n,t.input,h):buildSkippedNetworkRequestOrResponse(h),[4,function(e,t,n,r){return __getResponseInfo.apply(this,arguments)}(m,n,t.response,f)];case 1:return y=g.sent(),[2,{startTimestamp:o,endTimestamp:s,url:c,method:u,statusCode:p,request:v,response:y}]}})})).apply(this,arguments)}function __getResponseInfo(){return(__getResponseInfo=(0,y._)(function(e,t,n,r){var a,o,i,s,l,c,u,d;return(0,T.Jh)(this,function(p){switch(p.label){case 0:if(a=t.networkCaptureBodies,o=t.textEncoder,i=t.networkResponseHeaders,!e&&void 0!==r)return[2,buildSkippedNetworkRequestOrResponse(r)];if(s=n?getAllHeaders(n.headers,i):{},!n||!a&&void 0!==r)return[2,buildNetworkRequestOrResponse(s,r,void 0)];return[4,function(e){return __parseFetchResponseBody.apply(this,arguments)}(n)];case 1:if(c=(l=w._.apply(void 0,[p.sent(),2]))[0],u=l[1],d=function(e,t){var n=t.networkCaptureBodies,r=t.textEncoder,a=t.responseBodySize,o=t.captureDetails,i=t.headers;try{var s=e&&e.length&&void 0===a?getBodySize(e,r):a;if(!o)return buildSkippedNetworkRequestOrResponse(s);if(n)return buildNetworkRequestOrResponse(i,s,e);return buildNetworkRequestOrResponse(i,s,void 0)}catch(e){return buildNetworkRequestOrResponse(i,a,void 0)}}(c,{networkCaptureBodies:a,textEncoder:o,responseBodySize:r,captureDetails:e,headers:s}),u)return[2,mergeWarning(d,u)];return[2,d]}})})).apply(this,arguments)}function __parseFetchResponseBody(){return(__parseFetchResponseBody=(0,y._)(function(e){var t;return(0,T.Jh)(this,function(n){switch(n.label){case 0:if(!(t=function(e){try{return e.clone()}catch(e){}}(e)))return[2,[void 0,"BODY_PARSE_ERROR"]];n.label=1;case 1:var r;return n.trys.push([1,3,,4]),[4,(r=t,new Promise(function(e,t){var n=setTimeout(function(){return t(Error("Timeout while trying to read response body"))},500);(function(e){return __getResponseText.apply(this,arguments)})(r).then(function(t){return e(t)},function(e){return t(e)}).finally(function(){return clearTimeout(n)})}))];case 2:return[2,[n.sent()]];case 3:return n.sent(),[2,[void 0,"BODY_PARSE_ERROR"]];case 4:return[2]}})})).apply(this,arguments)}function _getFetchRequestArgBody(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];if(2===e.length&&"object"==typeof e[1])return e[1].body}function getAllHeaders(e,t){var n={};return t.forEach(function(t){e.get(t)&&(n[t]=e.get(t))}),n}function getHeadersFromOptions(e,t){if(!e)return{};var n=e.headers;return n?n instanceof Headers?getAllHeaders(n,t):Array.isArray(n)?{}:getAllowedHeaders(n,t):{}}function __getResponseText(){return(__getResponseText=(0,y._)(function(e){return(0,T.Jh)(this,function(t){switch(t.label){case 0:return[4,e.text()];case 1:return[2,t.sent()]}})})).apply(this,arguments)}function _captureXhrBreadcrumbToReplay(){return(_captureXhrBreadcrumbToReplay=(0,y._)(function(e,t,n){var r,a;return(0,T.Jh)(this,function(o){try{r=function(e,t,n){var r,a=Date.now(),o=t.startTimestamp,i=void 0===o?a:o,s=t.endTimestamp,l=void 0===s?a:s,c=t.input,u=t.xhr,d=e.data,p=d.url,h=d.method,f=d.status_code,m=void 0===f?0:f,v=d.request_body_size,y=d.response_body_size;if(!p)return null;if(!u||!urlMatches(p,n.networkDetailAllowUrls)||urlMatches(p,n.networkDetailDenyUrls))return{startTimestamp:i,endTimestamp:l,url:p,method:h,statusCode:m,request:buildSkippedNetworkRequestOrResponse(v),response:buildSkippedNetworkRequestOrResponse(y)};var g=u[q.xU],k=g?getAllowedHeaders(g.request_headers,n.networkRequestHeaders):{},S=getAllowedHeaders((r=u.getAllResponseHeaders())?r.split("\r\n").reduce(function(e,t){var n=(0,w._)(t.split(": "),2),r=n[0],a=n[1];return e[r.toLowerCase()]=a,e},{}):{},n.networkResponseHeaders),_=(0,w._)(n.networkCaptureBodies?getBodyString(c):[void 0],2),b=_[0],I=_[1],E=(0,w._)(n.networkCaptureBodies?function(e){var t=[];try{return[e.responseText]}catch(e){t.push(e)}try{return function(e,t){try{if("string"==typeof e)return[e];if(e instanceof Document)return[e.body.outerHTML];if("json"===t&&e&&"object"==typeof e)return[JSON.stringify(e)];if(!e)return[void 0]}catch(e){return[void 0,"BODY_PARSE_ERROR"]}return[void 0,"UNPARSEABLE_BODY_TYPE"]}(e.response,e.responseType)}catch(e){t.push(e)}return[void 0]}(u):[void 0],2),C=E[0],T=E[1],M=buildNetworkRequestOrResponse(k,v,b),x=buildNetworkRequestOrResponse(S,y,C);return{startTimestamp:i,endTimestamp:l,url:p,method:h,statusCode:m,request:I?mergeWarning(M,I):M,response:T?mergeWarning(x,T):x}}(e,t,n),a=makeNetworkReplayBreadcrumb("resource.xhr",r),addNetworkBreadcrumb(n.replay,a)}catch(e){}return[2]})})).apply(this,arguments)}var eq=null;function _addMemoryEntry(){return(_addMemoryEntry=(0,y._)(function(e){return(0,T.Jh)(this,function(t){try{var n,r,a,o,i;return[2,Promise.all(createPerformanceSpans(e,[(r=(n=Z.performance.memory).jsHeapSizeLimit,a=n.totalJSHeapSize,o=n.usedJSHeapSize,i=Date.now()/1e3,{type:"memory",name:"memory",start:i,end:i,data:{memory:{jsHeapSizeLimit:r,totalJSHeapSize:a,usedJSHeapSize:o}}})]))]}catch(e){return[2,[]]}return[2]})})).apply(this,arguments)}function _prepareReplayEvent(){return(_prepareReplayEvent=(0,y._)(function(e){var t,n,r,a,o,i,s,l,c,u;return(0,T.Jh)(this,function(d){switch(d.label){case 0:return t=e.client,n=e.scope,r=e.replayId,a=e.event,o={event_id:r,integrations:"object"!=typeof t._integrations||null===t._integrations||Array.isArray(t._integrations)?void 0:Object.keys(t._integrations)},t.emit&&t.emit("preprocessEvent",a,o),[4,(0,D.R)(t.getOptions(),a,o,n,t,(0,N.aF)())];case 1:if(!(i=d.sent()))return[2,null];return i.platform=i.platform||"javascript",c=(l=(s=t.getSdkMetadata&&t.getSdkMetadata())&&s.sdk||{}).name,u=l.version,i.sdk=(0,b._)((0,_._)({},i.sdk),{name:c||"sentry.javascript.unknown",version:u||"0.0.0"}),[2,i]}})})).apply(this,arguments)}function _sendReplayRequest(){return(_sendReplayRequest=(0,y._)(function(e){var t,n,r,a,o,i,s,l,c,u,d,p,h,f,m,v,y,g,k,S,_,b;return(0,T.Jh)(this,function(w){switch(w.label){case 0:if(t=e.recordingData,n=e.replayId,r=e.segmentId,a=e.eventContext,o=e.timestamp,i=e.session,s=function(e){var t,n=e.recordingData,r=e.headers,a="".concat(JSON.stringify(r),"\n");if("string"==typeof n)t="".concat(a).concat(n);else{var o=new TextEncoder().encode(a);(t=new Uint8Array(o.length+n.length)).set(o),t.set(n,o.length)}return t}({recordingData:t,headers:{segment_id:r}}),l=a.urls,c=a.errorIds,u=a.traceIds,d=a.initialTimestamp,p=(0,R.s3)(),h=(0,R.nZ)(),f=p&&p.getTransport(),m=p&&p.getDsn(),!p||!f||!m||!i.sampled)return[2];return v={type:"replay_event",replay_start_timestamp:d/1e3,timestamp:o/1e3,error_ids:c,trace_ids:u,urls:l,replay_id:n,segment_id:r,replay_type:i.sampled},[4,function(e){return _prepareReplayEvent.apply(this,arguments)}({scope:h,client:p,replayId:n,event:v})];case 1:var I,E,C;if(!(y=w.sent()))return p.recordDroppedEvent("event_processor","replay",v),[2];delete y.sdkProcessingMetadata,I=s,E=m,C=p.getOptions().tunnel,g=(0,Y.Jd)((0,Y.Cd)(y,(0,Y.HY)(y),C,E),[[{type:"replay_event"},y],[{type:"replay_recording",length:"string"==typeof I?new TextEncoder().encode(I).length:I.length},I]]),w.label=2;case 2:return w.trys.push([2,4,,5]),[4,f.send(g)];case 3:return k=w.sent(),[3,5];case 4:S=w.sent(),_=Error(et);try{_.cause=S}catch(e){}throw _;case 5:if(!k)return[2,k];if("number"==typeof k.statusCode&&(k.statusCode<200||k.statusCode>=300))throw new e$(k.statusCode);if(b=(0,X.WG)({},k),(0,X.Q)(b,"replay"))throw new eJ(b);return[2,k]}})})).apply(this,arguments)}var e$=function(e){(0,S._)(TransportStatusCodeError,e);var t=(0,C._)(TransportStatusCodeError);function TransportStatusCodeError(e){return(0,g._)(this,TransportStatusCodeError),t.call(this,"Transport returned status code ".concat(e))}return TransportStatusCodeError}((0,E._)(Error)),eJ=function(e){(0,S._)(RateLimitError,e);var t=(0,C._)(RateLimitError);function RateLimitError(e){var n;return(0,g._)(this,RateLimitError),(n=t.call(this,"Rate limit hit")).rateLimits=e,n}return RateLimitError}((0,E._)(Error));function sendReplay(e){return _sendReplay.apply(this,arguments)}function _sendReplay(){return(_sendReplay=(0,y._)(function(e){var t,n,r,a,o=arguments;return(0,T.Jh)(this,function(i){switch(i.label){case 0:if(t=o.length>1&&void 0!==o[1]?o[1]:{count:0,interval:5e3},n=e.recordingData,e.options,!n.length)return[2];i.label=1;case 1:return i.trys.push([1,3,,4]),[4,function(e){return _sendReplayRequest.apply(this,arguments)}(e)];case 2:return i.sent(),[2,!0];case 3:if((r=i.sent())instanceof e$||r instanceof eJ)throw r;if((0,R.v)("Replays",{_retryCount:t.count}),t.count>=3){a=Error("".concat(et," - max retries exceeded"));try{a.cause=r}catch(e){}throw a}return t.interval*=++t.count,[2,new Promise(function(n,r){setTimeout((0,y._)(function(){return(0,T.Jh)(this,function(a){switch(a.label){case 0:return a.trys.push([0,2,,3]),[4,sendReplay(e,t)];case 1:return a.sent(),n(!0),[3,3];case 2:return r(a.sent()),[3,3];case 3:return[2]}})}),t.interval)})];case 4:return[2]}})})).apply(this,arguments)}var eK="__THROTTLED",eV=function(){function ReplayContainer(e){var t,n,r,a,o=e.options,i=e.recordingOptions,s=this;(0,g._)(this,ReplayContainer),ReplayContainer.prototype.__init.call(this),ReplayContainer.prototype.__init2.call(this),ReplayContainer.prototype.__init3.call(this),ReplayContainer.prototype.__init4.call(this),ReplayContainer.prototype.__init5.call(this),ReplayContainer.prototype.__init6.call(this),this.eventBuffer=null,this.performanceEntries=[],this.replayPerformanceEntries=[],this.recordingMode="session",this.timeouts={sessionIdlePause:3e5,sessionIdleExpire:9e5},this._lastActivity=Date.now(),this._isEnabled=!1,this._isPaused=!1,this._hasInitializedCoreListeners=!1,this._context={errorIds:new Set,traceIds:new Set,urls:[],initialTimestamp:Date.now(),initialUrl:""},this._recordingOptions=i,this._options=o,this._debouncedFlush=function(e,t,n){var r,a,o,i=n&&n.maxWait?Math.max(n.maxWait,t):0;function invokeFunc(){return cancelTimers(),r=e()}function cancelTimers(){void 0!==a&&clearTimeout(a),void 0!==o&&clearTimeout(o),a=o=void 0}function debounced(){return a&&clearTimeout(a),a=setTimeout(invokeFunc,t),i&&void 0===o&&(o=setTimeout(invokeFunc,i)),r}return debounced.cancel=cancelTimers,debounced.flush=function(){return void 0!==a||void 0!==o?invokeFunc():r},debounced}(function(){return s._flush()},this._options.flushMinDelay,{maxWait:this._options.flushMaxDelay}),this._throttledAddEvent=(t=function(e,t){return shouldAddEvent(s,e)?_addEvent(s,e,t):Promise.resolve(null)},n=new Map,r=function(e){var t=e-5;n.forEach(function(e,r){r<t&&n.delete(r)})},a=!1,function(){for(var e=arguments.length,o=Array(e),i=0;i<e;i++)o[i]=arguments[i];var s=Math.floor(Date.now()/1e3);if(r(s),(0,I._)(n.values()).reduce(function(e,t){return e+t},0)>=300){var l=a;return a=!0,l?"__SKIPPED":eK}a=!1;var c=n.get(s)||0;return n.set(s,c+1),t.apply(void 0,(0,I._)(o))});var l=this.getOptions(),c=l.slowClickTimeout,u=l.slowClickIgnoreSelectors,d=c?{threshold:Math.min(3e3,c),timeout:c,scrollTimeout:300,ignoreSelector:u?u.join(","):""}:void 0;d&&(this.clickDetector=new eL(this,d))}return(0,k._)(ReplayContainer,[{key:"getContext",value:function(){return this._context}},{key:"isEnabled",value:function(){return this._isEnabled}},{key:"isPaused",value:function(){return this._isPaused}},{key:"isRecordingCanvas",value:function(){return!!this._canvas}},{key:"getOptions",value:function(){return this._options}},{key:"initializeSampling",value:function(e){var t=this._options,n=t.errorSampleRate,r=t.sessionSampleRate;if(!(n<=0)||!(r<=0)){if(this._initializeSessionForSampling(e),!this.session){this._handleException(Error("Unable to initialize and create session"));return}!1!==this.session.sampled&&(this.recordingMode="buffer"===this.session.sampled&&0===this.session.segmentId?"buffer":"session",this.recordingMode,this._options._experiments.traceInternals,this._initializeRecording())}}},{key:"start",value:function(){if(this._isEnabled&&"session"===this.recordingMode)throw Error("Replay recording is already in progress");if(this._isEnabled&&"buffer"===this.recordingMode)throw Error("Replay buffering is in progress, call `flush()` to save the replay");this._options._experiments.traceInternals;var e=loadOrCreateSession({maxReplayDuration:this._options.maxReplayDuration,sessionIdleExpire:this.timeouts.sessionIdleExpire,traceInternals:this._options._experiments.traceInternals},{stickySession:this._options.stickySession,sessionSampleRate:1,allowBuffering:!1});this.session=e,this._initializeRecording()}},{key:"startBuffering",value:function(){if(this._isEnabled)throw Error("Replay recording is already in progress");this._options._experiments.traceInternals;var e=loadOrCreateSession({sessionIdleExpire:this.timeouts.sessionIdleExpire,maxReplayDuration:this._options.maxReplayDuration,traceInternals:this._options._experiments.traceInternals},{stickySession:this._options.stickySession,sessionSampleRate:0,allowBuffering:!0});this.session=e,this.recordingMode="buffer",this._initializeRecording()}},{key:"startRecording",value:function(){try{var e,t,n=this._canvas;this._stopRecording=record((0,_._)((0,b._)((0,_._)({},this._recordingOptions,"buffer"===this.recordingMode&&{checkoutEveryNms:6e4}),{emit:(e=this,t=!1,function(n,r){if(e.checkAndHandleExpiredSession()){var a=r||!t;t=!0,e.clickDetector&&function(e,t){try{if(3!==t.type)return;var n=t.data.source;if(n===eg.Mutation&&e.registerMutation(t.timestamp),n===eg.Scroll&&e.registerScroll(t.timestamp),t.data.source===eg.MouseInteraction){var r=t.data,a=r.type,o=r.id,i=record.mirror.getNode(o);i instanceof HTMLElement&&a===ek.Click&&e.registerClick(i)}}catch(e){}}(e.clickDetector,n),e.addUpdate(function(){if("buffer"===e.recordingMode&&a&&e.setInitialState(),!addEventSync(e,n,a))return!0;if(!a)return!1;if(t=e,a&&t.session&&0===t.session.segmentId&&addEventSync(t,(r=t.getOptions(),{type:ey.Custom,timestamp:Date.now(),data:{tag:"options",payload:{shouldRecordCanvas:t.isRecordingCanvas(),sessionSampleRate:r.sessionSampleRate,errorSampleRate:r.errorSampleRate,useCompressionOption:r.useCompression,blockAllMedia:r.blockAllMedia,maskAllText:r.maskAllText,maskAllInputs:r.maskAllInputs,useCompression:!!t.eventBuffer&&"worker"===t.eventBuffer.type,networkDetailHasUrls:r.networkDetailAllowUrls.length>0,networkCaptureBodies:r.networkCaptureBodies,networkRequestHasHeaders:r.networkRequestHeaders.length>0,networkResponseHasHeaders:r.networkResponseHeaders.length>0}}}),!1),e.session&&e.session.previousSessionId)return!0;if("buffer"===e.recordingMode&&e.session&&e.eventBuffer){var t,r,o=e.eventBuffer.getEarliestTimestamp();o&&(e.getOptions()._experiments.traceInternals,e.session.started=o,e.getOptions().stickySession&&saveSession(e.session))}return"session"===e.recordingMode&&e.flush(),!0})}}),onMutation:this._onMutationHandler}),n?{recordCanvas:n.recordCanvas,getCanvasManager:n.getCanvasManager,sampling:n.sampling,dataURLOptions:n.dataURLOptions}:{}))}catch(e){this._handleException(e)}}},{key:"stopRecording",value:function(){try{return this._stopRecording&&(this._stopRecording(),this._stopRecording=void 0),!0}catch(e){return this._handleException(e),!1}}},{key:"stop",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.forceFlush,n=void 0!==t&&t,r=(e.reason,this);return(0,y._)(function(){var e;return(0,T.Jh)(this,function(t){switch(t.label){case 0:if(!r._isEnabled)return[2];r._isEnabled=!1,t.label=1;case 1:if(t.trys.push([1,4,,5]),r._options._experiments.traceInternals,r._removeListeners(),r.stopRecording(),r._debouncedFlush.cancel(),!n)return[3,3];return[4,r._flush({force:!0})];case 2:t.sent(),t.label=3;case 3:return r.eventBuffer&&r.eventBuffer.destroy(),r.eventBuffer=null,function(){if(hasSessionStorage())try{Z.sessionStorage.removeItem(ee)}catch(e){}}(),r.session=void 0,[3,5];case 4:return e=t.sent(),r._handleException(e),[3,5];case 5:return[2]}})})()}},{key:"pause",value:function(){this._isPaused||(this._isPaused=!0,this.stopRecording(),this._options._experiments.traceInternals)}},{key:"resume",value:function(){this._isPaused&&this._checkSession()&&(this._isPaused=!1,this.startRecording(),this._options._experiments.traceInternals)}},{key:"sendBufferedReplayOrFlush",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.continueRecording,n=void 0===t||t,r=this;return(0,y._)(function(){var e,t;return(0,T.Jh)(this,function(a){switch(a.label){case 0:if("session"===r.recordingMode)return[2,r.flushImmediate()];return e=Date.now(),r._options._experiments.traceInternals,[4,r.flushImmediate()];case 1:if(a.sent(),t=r.stopRecording(),!n||!t||"session"===r.recordingMode)return[2];return r.recordingMode="session",r.session&&(r._updateUserActivity(e),r._updateSessionActivity(e),r._maybeSaveSession()),r.startRecording(),[2]}})})()}},{key:"addUpdate",value:function(e){var t=e();"buffer"!==this.recordingMode&&!0!==t&&this._debouncedFlush()}},{key:"triggerUserActivity",value:function(){if(this._updateUserActivity(),!this._stopRecording){if(!this._checkSession())return;this.resume();return}this.checkAndHandleExpiredSession(),this._updateSessionActivity()}},{key:"updateUserActivity",value:function(){this._updateUserActivity(),this._updateSessionActivity()}},{key:"conditionalFlush",value:function(){return"buffer"===this.recordingMode?Promise.resolve():this.flushImmediate()}},{key:"flush",value:function(){return this._debouncedFlush()}},{key:"flushImmediate",value:function(){return this._debouncedFlush(),this._debouncedFlush.flush()}},{key:"cancelFlush",value:function(){this._debouncedFlush.cancel()}},{key:"getSessionId",value:function(){return this.session&&this.session.id}},{key:"checkAndHandleExpiredSession",value:function(){if(this._lastActivity&&isExpired(this._lastActivity,this.timeouts.sessionIdlePause)&&this.session&&"session"===this.session.sampled){this.pause();return}return!!this._checkSession()}},{key:"setInitialState",value:function(){var e="".concat(Z.location.pathname).concat(Z.location.hash).concat(Z.location.search),t="".concat(Z.location.origin).concat(e);this.performanceEntries=[],this.replayPerformanceEntries=[],this._clearContext(),this._context.initialUrl=t,this._context.initialTimestamp=Date.now(),this._context.urls.push(t)}},{key:"throttledAddEvent",value:function(e,t){var n=this,r=this._throttledAddEvent(e,t);if(r===eK){var a=createBreadcrumb({category:"replay.throttled"});this.addUpdate(function(){return!addEventSync(n,{type:5,timestamp:a.timestamp||0,data:{tag:"breadcrumb",payload:a,metric:!0}})})}return r}},{key:"getCurrentRoute",value:function(){var e=this.lastTransaction||(0,R.nZ)().getTransaction(),t=(e&&(0,L.XU)(e).data||{})[B.Zj];if(e&&t&&["route","custom"].includes(t))return(0,L.XU)(e).description}},{key:"_initializeRecording",value:function(){this.setInitialState(),this._updateSessionActivity(),this.eventBuffer=function(e){var t=e.useCompression,n=e.workerUrl;if(t&&window.Worker){var r=function(e){try{var t=e||function(){if("undefined"==typeof __SENTRY_EXCLUDE_REPLAY_WORKER__||!__SENTRY_EXCLUDE_REPLAY_WORKER__){var e;return e=new Blob(['var t=Uint8Array,n=Uint16Array,r=Int32Array,e=new t([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),i=new t([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),a=new t([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),s=function(t,e){for(var i=new n(31),a=0;a<31;++a)i[a]=e+=1<<t[a-1];var s=new r(i[30]);for(a=1;a<30;++a)for(var o=i[a];o<i[a+1];++o)s[o]=o-i[a]<<5|a;return{b:i,r:s}},o=s(e,2),f=o.b,h=o.r;f[28]=258,h[258]=28;for(var l=s(i,0).r,u=new n(32768),c=0;c<32768;++c){var v=(43690&c)>>1|(21845&c)<<1;v=(61680&(v=(52428&v)>>2|(13107&v)<<2))>>4|(3855&v)<<4,u[c]=((65280&v)>>8|(255&v)<<8)>>1}var d=function(t,r,e){for(var i=t.length,a=0,s=new n(r);a<i;++a)t[a]&&++s[t[a]-1];var o,f=new n(r);for(a=1;a<r;++a)f[a]=f[a-1]+s[a-1]<<1;if(e){o=new n(1<<r);var h=15-r;for(a=0;a<i;++a)if(t[a])for(var l=a<<4|t[a],c=r-t[a],v=f[t[a]-1]++<<c,d=v|(1<<c)-1;v<=d;++v)o[u[v]>>h]=l}else for(o=new n(i),a=0;a<i;++a)t[a]&&(o[a]=u[f[t[a]-1]++]>>15-t[a]);return o},g=new t(288);for(c=0;c<144;++c)g[c]=8;for(c=144;c<256;++c)g[c]=9;for(c=256;c<280;++c)g[c]=7;for(c=280;c<288;++c)g[c]=8;var w=new t(32);for(c=0;c<32;++c)w[c]=5;var p=d(g,9,0),y=d(w,5,0),m=function(t){return(t+7)/8|0},b=function(n,r,e){return(null==r||r<0)&&(r=0),(null==e||e>n.length)&&(e=n.length),new t(n.subarray(r,e))},M=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],E=function(t,n,r){var e=new Error(n||M[t]);if(e.code=t,Error.captureStackTrace&&Error.captureStackTrace(e,E),!r)throw e;return e},z=function(t,n,r){r<<=7&n;var e=n/8|0;t[e]|=r,t[e+1]|=r>>8},A=function(t,n,r){r<<=7&n;var e=n/8|0;t[e]|=r,t[e+1]|=r>>8,t[e+2]|=r>>16},_=function(r,e){for(var i=[],a=0;a<r.length;++a)r[a]&&i.push({s:a,f:r[a]});var s=i.length,o=i.slice();if(!s)return{t:F,l:0};if(1==s){var f=new t(i[0].s+1);return f[i[0].s]=1,{t:f,l:1}}i.sort((function(t,n){return t.f-n.f})),i.push({s:-1,f:25001});var h=i[0],l=i[1],u=0,c=1,v=2;for(i[0]={s:-1,f:h.f+l.f,l:h,r:l};c!=s-1;)h=i[i[u].f<i[v].f?u++:v++],l=i[u!=c&&i[u].f<i[v].f?u++:v++],i[c++]={s:-1,f:h.f+l.f,l:h,r:l};var d=o[0].s;for(a=1;a<s;++a)o[a].s>d&&(d=o[a].s);var g=new n(d+1),w=x(i[c-1],g,0);if(w>e){a=0;var p=0,y=w-e,m=1<<y;for(o.sort((function(t,n){return g[n.s]-g[t.s]||t.f-n.f}));a<s;++a){var b=o[a].s;if(!(g[b]>e))break;p+=m-(1<<w-g[b]),g[b]=e}for(p>>=y;p>0;){var M=o[a].s;g[M]<e?p-=1<<e-g[M]++-1:++a}for(;a>=0&&p;--a){var E=o[a].s;g[E]==e&&(--g[E],++p)}w=e}return{t:new t(g),l:w}},x=function(t,n,r){return-1==t.s?Math.max(x(t.l,n,r+1),x(t.r,n,r+1)):n[t.s]=r},D=function(t){for(var r=t.length;r&&!t[--r];);for(var e=new n(++r),i=0,a=t[0],s=1,o=function(t){e[i++]=t},f=1;f<=r;++f)if(t[f]==a&&f!=r)++s;else{if(!a&&s>2){for(;s>138;s-=138)o(32754);s>2&&(o(s>10?s-11<<5|28690:s-3<<5|12305),s=0)}else if(s>3){for(o(a),--s;s>6;s-=6)o(8304);s>2&&(o(s-3<<5|8208),s=0)}for(;s--;)o(a);s=1,a=t[f]}return{c:e.subarray(0,i),n:r}},T=function(t,n){for(var r=0,e=0;e<n.length;++e)r+=t[e]*n[e];return r},k=function(t,n,r){var e=r.length,i=m(n+2);t[i]=255&e,t[i+1]=e>>8,t[i+2]=255^t[i],t[i+3]=255^t[i+1];for(var a=0;a<e;++a)t[i+a+4]=r[a];return 8*(i+4+e)},C=function(t,r,s,o,f,h,l,u,c,v,m){z(r,m++,s),++f[256];for(var b=_(f,15),M=b.t,E=b.l,x=_(h,15),C=x.t,U=x.l,F=D(M),I=F.c,S=F.n,L=D(C),O=L.c,j=L.n,q=new n(19),B=0;B<I.length;++B)++q[31&I[B]];for(B=0;B<O.length;++B)++q[31&O[B]];for(var G=_(q,7),H=G.t,J=G.l,K=19;K>4&&!H[a[K-1]];--K);var N,P,Q,R,V=v+5<<3,W=T(f,g)+T(h,w)+l,X=T(f,M)+T(h,C)+l+14+3*K+T(q,H)+2*q[16]+3*q[17]+7*q[18];if(c>=0&&V<=W&&V<=X)return k(r,m,t.subarray(c,c+v));if(z(r,m,1+(X<W)),m+=2,X<W){N=d(M,E,0),P=M,Q=d(C,U,0),R=C;var Y=d(H,J,0);z(r,m,S-257),z(r,m+5,j-1),z(r,m+10,K-4),m+=14;for(B=0;B<K;++B)z(r,m+3*B,H[a[B]]);m+=3*K;for(var Z=[I,O],$=0;$<2;++$){var tt=Z[$];for(B=0;B<tt.length;++B){var nt=31&tt[B];z(r,m,Y[nt]),m+=H[nt],nt>15&&(z(r,m,tt[B]>>5&127),m+=tt[B]>>12)}}}else N=p,P=g,Q=y,R=w;for(B=0;B<u;++B){var rt=o[B];if(rt>255){A(r,m,N[(nt=rt>>18&31)+257]),m+=P[nt+257],nt>7&&(z(r,m,rt>>23&31),m+=e[nt]);var et=31&rt;A(r,m,Q[et]),m+=R[et],et>3&&(A(r,m,rt>>5&8191),m+=i[et])}else A(r,m,N[rt]),m+=P[rt]}return A(r,m,N[256]),m+P[256]},U=new r([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),F=new t(0),I=function(){for(var t=new Int32Array(256),n=0;n<256;++n){for(var r=n,e=9;--e;)r=(1&r&&-306674912)^r>>>1;t[n]=r}return t}(),S=function(){var t=1,n=0;return{p:function(r){for(var e=t,i=n,a=0|r.length,s=0;s!=a;){for(var o=Math.min(s+2655,a);s<o;++s)i+=e+=r[s];e=(65535&e)+15*(e>>16),i=(65535&i)+15*(i>>16)}t=e,n=i},d:function(){return(255&(t%=65521))<<24|(65280&t)<<8|(255&(n%=65521))<<8|n>>8}}},L=function(a,s,o,f,u){if(!u&&(u={l:1},s.dictionary)){var c=s.dictionary.subarray(-32768),v=new t(c.length+a.length);v.set(c),v.set(a,c.length),a=v,u.w=c.length}return function(a,s,o,f,u,c){var v=c.z||a.length,d=new t(f+v+5*(1+Math.ceil(v/7e3))+u),g=d.subarray(f,d.length-u),w=c.l,p=7&(c.r||0);if(s){p&&(g[0]=c.r>>3);for(var y=U[s-1],M=y>>13,E=8191&y,z=(1<<o)-1,A=c.p||new n(32768),_=c.h||new n(z+1),x=Math.ceil(o/3),D=2*x,T=function(t){return(a[t]^a[t+1]<<x^a[t+2]<<D)&z},F=new r(25e3),I=new n(288),S=new n(32),L=0,O=0,j=c.i||0,q=0,B=c.w||0,G=0;j+2<v;++j){var H=T(j),J=32767&j,K=_[H];if(A[J]=K,_[H]=J,B<=j){var N=v-j;if((L>7e3||q>24576)&&(N>423||!w)){p=C(a,g,0,F,I,S,O,q,G,j-G,p),q=L=O=0,G=j;for(var P=0;P<286;++P)I[P]=0;for(P=0;P<30;++P)S[P]=0}var Q=2,R=0,V=E,W=J-K&32767;if(N>2&&H==T(j-W))for(var X=Math.min(M,N)-1,Y=Math.min(32767,j),Z=Math.min(258,N);W<=Y&&--V&&J!=K;){if(a[j+Q]==a[j+Q-W]){for(var $=0;$<Z&&a[j+$]==a[j+$-W];++$);if($>Q){if(Q=$,R=W,$>X)break;var tt=Math.min(W,$-2),nt=0;for(P=0;P<tt;++P){var rt=j-W+P&32767,et=rt-A[rt]&32767;et>nt&&(nt=et,K=rt)}}}W+=(J=K)-(K=A[J])&32767}if(R){F[q++]=268435456|h[Q]<<18|l[R];var it=31&h[Q],at=31&l[R];O+=e[it]+i[at],++I[257+it],++S[at],B=j+Q,++L}else F[q++]=a[j],++I[a[j]]}}for(j=Math.max(j,B);j<v;++j)F[q++]=a[j],++I[a[j]];p=C(a,g,w,F,I,S,O,q,G,j-G,p),w||(c.r=7&p|g[p/8|0]<<3,p-=7,c.h=_,c.p=A,c.i=j,c.w=B)}else{for(j=c.w||0;j<v+w;j+=65535){var st=j+65535;st>=v&&(g[p/8|0]=w,st=v),p=k(g,p+1,a.subarray(j,st))}c.i=v}return b(d,0,f+m(p)+u)}(a,null==s.level?6:s.level,null==s.mem?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(a.length)))):12+s.mem,o,f,u)},O=function(t,n,r){for(;r;++n)t[n]=r,r>>>=8},j=function(){function n(n,r){if("function"==typeof n&&(r=n,n={}),this.ondata=r,this.o=n||{},this.s={l:0,i:32768,w:32768,z:32768},this.b=new t(98304),this.o.dictionary){var e=this.o.dictionary.subarray(-32768);this.b.set(e,32768-e.length),this.s.i=32768-e.length}}return n.prototype.p=function(t,n){this.ondata(L(t,this.o,0,0,this.s),n)},n.prototype.push=function(n,r){this.ondata||E(5),this.s.l&&E(4);var e=n.length+this.s.z;if(e>this.b.length){if(e>2*this.b.length-32768){var i=new t(-32768&e);i.set(this.b.subarray(0,this.s.z)),this.b=i}var a=this.b.length-this.s.z;a&&(this.b.set(n.subarray(0,a),this.s.z),this.s.z=this.b.length,this.p(this.b,!1)),this.b.set(this.b.subarray(-32768)),this.b.set(n.subarray(a),32768),this.s.z=n.length-a+32768,this.s.i=32766,this.s.w=32768}else this.b.set(n,this.s.z),this.s.z+=n.length;this.s.l=1&r,(this.s.z>this.s.w+8191||r)&&(this.p(this.b,r||!1),this.s.w=this.s.i,this.s.i-=2)},n}();function q(t,n){n||(n={});var r=function(){var t=-1;return{p:function(n){for(var r=t,e=0;e<n.length;++e)r=I[255&r^n[e]]^r>>>8;t=r},d:function(){return~t}}}(),e=t.length;r.p(t);var i,a=L(t,n,10+((i=n).filename?i.filename.length+1:0),8),s=a.length;return function(t,n){var r=n.filename;if(t[0]=31,t[1]=139,t[2]=8,t[8]=n.level<2?4:9==n.level?2:0,t[9]=3,0!=n.mtime&&O(t,4,Math.floor(new Date(n.mtime||Date.now())/1e3)),r){t[3]=8;for(var e=0;e<=r.length;++e)t[e+10]=r.charCodeAt(e)}}(a,n),O(a,s-8,r.d()),O(a,s-4,e),a}var B=function(){function t(t,n){this.c=S(),this.v=1,j.call(this,t,n)}return t.prototype.push=function(t,n){this.c.p(t),j.prototype.push.call(this,t,n)},t.prototype.p=function(t,n){var r=L(t,this.o,this.v&&(this.o.dictionary?6:2),n&&4,this.s);this.v&&(function(t,n){var r=n.level,e=0==r?0:r<6?1:9==r?3:2;if(t[0]=120,t[1]=e<<6|(n.dictionary&&32),t[1]|=31-(t[0]<<8|t[1])%31,n.dictionary){var i=S();i.p(n.dictionary),O(t,2,i.d())}}(r,this.o),this.v=0),n&&O(r,r.length-4,this.c.d()),this.ondata(r,n)},t}(),G="undefined"!=typeof TextEncoder&&new TextEncoder,H="undefined"!=typeof TextDecoder&&new TextDecoder;try{H.decode(F,{stream:!0})}catch(t){}var J=function(){function t(t){this.ondata=t}return t.prototype.push=function(t,n){this.ondata||E(5),this.d&&E(4),this.ondata(K(t),this.d=n||!1)},t}();function K(n,r){if(r){for(var e=new t(n.length),i=0;i<n.length;++i)e[i]=n.charCodeAt(i);return e}if(G)return G.encode(n);var a=n.length,s=new t(n.length+(n.length>>1)),o=0,f=function(t){s[o++]=t};for(i=0;i<a;++i){if(o+5>s.length){var h=new t(o+8+(a-i<<1));h.set(s),s=h}var l=n.charCodeAt(i);l<128||r?f(l):l<2048?(f(192|l>>6),f(128|63&l)):l>55295&&l<57344?(f(240|(l=65536+(1047552&l)|1023&n.charCodeAt(++i))>>18),f(128|l>>12&63),f(128|l>>6&63),f(128|63&l)):(f(224|l>>12),f(128|l>>6&63),f(128|63&l))}return b(s,0,o)}const N=new class{constructor(){this._init()}clear(){this._init()}addEvent(t){if(!t)throw new Error("Adding invalid event");const n=this._hasEvents?",":"";this.stream.push(n+t),this._hasEvents=!0}finish(){this.stream.push("]",!0);const t=function(t){let n=0;for(let r=0,e=t.length;r<e;r++)n+=t[r].length;const r=new Uint8Array(n);for(let n=0,e=0,i=t.length;n<i;n++){const i=t[n];r.set(i,e),e+=i.length}return r}(this._deflatedData);return this._init(),t}_init(){this._hasEvents=!1,this._deflatedData=[],this.deflate=new B,this.deflate.ondata=(t,n)=>{this._deflatedData.push(t)},this.stream=new J(((t,n)=>{this.deflate.push(t,n)})),this.stream.push("[")}},P={clear:()=>{N.clear()},addEvent:t=>N.addEvent(t),finish:()=>N.finish(),compress:t=>function(t){return q(K(t))}(t)};addEventListener("message",(function(t){const n=t.data.method,r=t.data.id,e=t.data.arg;if(n in P&&"function"==typeof P[n])try{const t=P[n](e);postMessage({id:r,method:n,success:!0,response:t})}catch(t){postMessage({id:r,method:n,success:!1,response:t.message}),console.error(t)}})),postMessage({id:void 0,method:"init",success:!0,response:void 0});']),URL.createObjectURL(e)}return""}();if(!t)return;var n=new Worker(t);return new ej(n)}catch(e){}}(n);if(r)return r}return new ez}({useCompression:this._options.useCompression,workerUrl:this._options.workerUrl}),this._removeListeners(),this._addListeners(),this._isEnabled=!0,this._isPaused=!1,this.startRecording()}},{key:"_handleException",value:function(e){}},{key:"_initializeSessionForSampling",value:function(e){var t=this._options.errorSampleRate>0,n=loadOrCreateSession({sessionIdleExpire:this.timeouts.sessionIdleExpire,maxReplayDuration:this._options.maxReplayDuration,traceInternals:this._options._experiments.traceInternals,previousSessionId:e},{stickySession:this._options.stickySession,sessionSampleRate:this._options.sessionSampleRate,allowBuffering:t});this.session=n}},{key:"_checkSession",value:function(){if(!this.session)return!1;var e=this.session;return!shouldRefreshSession(e,{sessionIdleExpire:this.timeouts.sessionIdleExpire,maxReplayDuration:this._options.maxReplayDuration})||(this._refreshSession(e),!1)}},{key:"_refreshSession",value:function(e){var t=this;return(0,y._)(function(){return(0,T.Jh)(this,function(n){switch(n.label){case 0:if(!t._isEnabled)return[2];return[4,t.stop({reason:"refresh session"})];case 1:return n.sent(),t.initializeSampling(e.id),[2]}})})()}},{key:"_addListeners",value:function(){try{Z.document.addEventListener("visibilitychange",this._handleVisibilityChange),Z.addEventListener("blur",this._handleWindowBlur),Z.addEventListener("focus",this._handleWindowFocus),Z.addEventListener("keydown",this._handleKeyboardEvent),this.clickDetector&&this.clickDetector.addListeners(),this._hasInitializedCoreListeners||(function(e){var t=(0,R.nZ)(),n=(0,R.s3)();t.addScopeListener(function(t){if(e.isEnabled()){var n,r=(n=t.getLastBreadcrumb&&t.getLastBreadcrumb(),eq!==n&&n?(eq=n,!n.category||["fetch","xhr","sentry.event","sentry.transaction"].includes(n.category)||n.category.startsWith("ui."))?null:"console"===n.category?function(e){var t=e.data&&e.data.arguments;if(!Array.isArray(t)||0===t.length)return createBreadcrumb(e);var n=!1,r=t.map(function(e){if(!e)return e;if("string"==typeof e)return e.length>5e3?(n=!0,"".concat(e.slice(0,5e3),"…")):e;if("object"==typeof e)try{var t=(0,P.Fv)(e,7);if(JSON.stringify(t).length>5e3)return n=!0,"".concat(JSON.stringify(t,null,2).slice(0,5e3),"…");return t}catch(e){}return e});return createBreadcrumb((0,b._)((0,_._)({},e),{data:(0,_._)((0,b._)((0,_._)({},e.data),{arguments:r}),n?{_meta:{warnings:["CONSOLE_ARG_TRUNCATED"]}}:{})}))}(n):createBreadcrumb(n):null);r&&addBreadcrumbEvent(e,r)}}),(0,K.O)(function(t){if(e.isEnabled()){var n,r,a,o=(r=(n=function(e){var t,n="click"===e.name,r=null;try{r=n?getClickTargetNode(e.event):getTargetNode(e.event),t=(0,z.Rt)(r,{maxStringLength:200})||"<unknown>"}catch(e){t="<unknown>"}return{target:r,message:t}}(t)).target,a=n.message,createBreadcrumb((0,_._)({category:"ui.".concat(t.name)},getBaseDomBreadcrumb(r,a))));if(o){var i="click"===t.name,s=i?t.event:void 0;i&&e.clickDetector&&s&&s.target&&!s.altKey&&!s.metaKey&&!s.ctrlKey&&!s.shiftKey&&function(e,t,n){e.handleClick(t,n)}(e.clickDetector,o,getClickTargetNode(t.event)),addBreadcrumbEvent(e,o)}}}),(0,V.a)(function(t){if(e.isEnabled()){var n,r,a,o=(n=t.from,r=t.to,{type:"navigation.push",start:a=Date.now()/1e3,end:a,name:r,data:{previous:n}});null!==o&&(e.getContext().urls.push(o.name),e.triggerUserActivity(),e.addUpdate(function(){return createPerformanceSpans(e,[o]),!1}))}}),function(e){var t=(0,R.s3)();try{var n=new TextEncoder,r=e.getOptions(),a=r.networkDetailAllowUrls,o=r.networkDetailDenyUrls,i=r.networkCaptureBodies,s=r.networkRequestHeaders,l=r.networkResponseHeaders,c={replay:e,textEncoder:n,networkDetailAllowUrls:a,networkDetailDenyUrls:o,networkCaptureBodies:i,networkRequestHeaders:s,networkResponseHeaders:l};t&&t.on?t.on("beforeAddBreadcrumb",function(e,t){return function(e,t,n){if(t.data)try{var r,a,o,i,s,l;"xhr"===t.category&&(r=n)&&r.xhr&&(function(e,t,n){var r=t.xhr,a=t.input;if(r){var o=getBodySize(a,n.textEncoder),i=r.getResponseHeader("content-length")?parseContentLengthHeader(r.getResponseHeader("content-length")):function(e,t,n){try{var r="json"===t&&e&&"object"==typeof e?JSON.stringify(e):e;return getBodySize(r,n)}catch(e){return}}(r.response,r.responseType,n.textEncoder);void 0!==o&&(e.data.request_body_size=o),void 0!==i&&(e.data.response_body_size=i)}}(t,n,e),function(e,t,n){_captureXhrBreadcrumbToReplay.apply(this,arguments)}(t,n,e)),"fetch"===t.category&&(a=n)&&a.response&&(o=n.input,i=n.response,s=getBodySize(o?_getFetchRequestArgBody(o):void 0,e.textEncoder),l=i?parseContentLengthHeader(i.headers.get("content-length")):void 0,void 0!==s&&(t.data.request_body_size=s),void 0!==l&&(t.data.response_body_size=l),function(e,t,n){_captureFetchBreadcrumbToReplay.apply(this,arguments)}(t,n,e))}catch(e){}}(c,e,t)}):((0,J.U)(function(t){e.isEnabled()&&addNetworkBreadcrumb(e,function(e){var t=e.startTimestamp,n=e.endTimestamp,r=e.fetchData,a=e.response;if(!n)return null;var o=r.method;return{type:"resource.fetch",start:t/1e3,end:n/1e3,name:r.url,data:{method:o,statusCode:a?a.status:void 0}}}(t))}),(0,q.UK)(function(t){e.isEnabled()&&addNetworkBreadcrumb(e,function(e){var t=e.startTimestamp,n=e.endTimestamp,r=e.xhr[q.xU];if(!t||!n||!r)return null;var a=r.method,o=r.url,i=r.status_code;return void 0===o?null:{type:"resource.xhr",name:o,start:t/1e3,end:n/1e3,data:{method:a,statusCode:i}}}(t))}))}catch(e){}}(e);var r=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=t?handleAfterSendEvent(e):void 0;return Object.assign(function(t,r){return e.isEnabled()?"replay_event"===t.type?(delete t.breadcrumbs,t):(!t.type||isTransactionEvent(t)||isFeedbackEvent(t))&&e.checkAndHandleExpiredSession()?isFeedbackEvent(t)?(e.flush(),t.contexts.feedback.replay_id=e.getSessionId(),e.triggerUserActivity(),e.addUpdate(function(){return!t.timestamp||(e.throttledAddEvent({type:ey.Custom,timestamp:1e3*t.timestamp,data:{tag:"breadcrumb",payload:{timestamp:t.timestamp,type:"default",category:"sentry.feedback",data:{feedbackId:t.event_id}}}}),!1)}),t):!t.type&&t.exception&&t.exception.values&&t.exception.values.length&&r.originalException&&r.originalException.__rrweb__&&!e.getOptions()._experiments.captureExceptions?null:(("buffer"===e.recordingMode&&t.message!==et&&t.exception&&!t.type&&isSampled(e.getOptions().errorSampleRate)||"session"===e.recordingMode)&&(t.tags=(0,b._)((0,_._)({},t.tags),{replayId:e.getSessionId()})),n&&n(t,{statusCode:200}),t):t:t},{id:"Replay"})}(e,!(n&&n.on));n&&n.addEventProcessor?n.addEventProcessor(r):(0,O.Q)(r),n&&n.on&&(n.on("beforeSendEvent",function(t){if(e.isEnabled()&&!t.type){var n;n=t.exception&&t.exception.values&&t.exception.values[0].value,"string"==typeof n&&(n.match(/reactjs\.org\/docs\/error-decoder\.html\?invariant=(418|419|422|423|425)/)||n.match(/(does not match server-rendered HTML|Hydration failed because)/i))&&addBreadcrumbEvent(e,createBreadcrumb({category:"replay.hydrate-error"}))}}),n.on("afterSendEvent",handleAfterSendEvent(e)),n.on("createDsc",function(t){var n=e.getSessionId();n&&e.isEnabled()&&"session"===e.recordingMode&&e.checkAndHandleExpiredSession()&&(t.replay_id=n)}),n.on("startTransaction",function(t){e.lastTransaction=t}),n.on("finishTransaction",function(t){e.lastTransaction=t}),n.on("beforeSendFeedback",function(t,n){var r=e.getSessionId();n&&n.includeReplay&&e.isEnabled()&&r&&t.contexts&&t.contexts.feedback&&(t.contexts.feedback.replay_id=r)}))}(this),this._hasInitializedCoreListeners=!0)}catch(e){this._handleException(e)}this._performanceCleanupCallback=function(e){function addPerformanceEntry(t){e.performanceEntries.includes(t)||e.performanceEntries.push(t)}function onEntries(e){e.entries.forEach(addPerformanceEntry)}var t=[];return["navigation","paint","resource"].forEach(function(e){t.push((0,Q._j)(e,onEntries))}),t.push((0,Q.$A)(function(t){var n,r,a,o,i,s=t.metric;e.replayPerformanceEntries.push((a=(r=(n=s.entries)[n.length-1])?r.element:void 0,{type:"largest-contentful-paint",name:"largest-contentful-paint",start:i=getAbsoluteTime(o=s.value),end:i,data:{value:o,size:o,nodeId:a?record.mirror.getId(a):void 0}}))})),function(){t.forEach(function(e){return e()})}}(this)}},{key:"_removeListeners",value:function(){try{Z.document.removeEventListener("visibilitychange",this._handleVisibilityChange),Z.removeEventListener("blur",this._handleWindowBlur),Z.removeEventListener("focus",this._handleWindowFocus),Z.removeEventListener("keydown",this._handleKeyboardEvent),this.clickDetector&&this.clickDetector.removeListeners(),this._performanceCleanupCallback&&this._performanceCleanupCallback()}catch(e){this._handleException(e)}}},{key:"__init",value:function(){var e=this;this._handleVisibilityChange=function(){"visible"===Z.document.visibilityState?e._doChangeToForegroundTasks():e._doChangeToBackgroundTasks()}}},{key:"__init2",value:function(){var e=this;this._handleWindowBlur=function(){var t=createBreadcrumb({category:"ui.blur"});e._doChangeToBackgroundTasks(t)}}},{key:"__init3",value:function(){var e=this;this._handleWindowFocus=function(){var t=createBreadcrumb({category:"ui.focus"});e._doChangeToForegroundTasks(t)}}},{key:"__init4",value:function(){var e=this;this._handleKeyboardEvent=function(t){!function(e,t){if(e.isEnabled()){e.updateUserActivity();var n=function(e){var t,n=e.metaKey,r=e.shiftKey,a=e.ctrlKey,o=e.altKey,i=e.key,s=e.target;if(!s||"INPUT"===(t=s).tagName||"TEXTAREA"===t.tagName||t.isContentEditable||!i)return null;var l=1===i.length;if(!(n||a||o)&&l)return null;var c=(0,z.Rt)(s,{maxStringLength:200})||"<unknown>",u=getBaseDomBreadcrumb(s,c);return createBreadcrumb({category:"ui.keyDown",message:c,data:(0,b._)((0,_._)({},u.data),{metaKey:n,shiftKey:r,ctrlKey:a,altKey:o,key:i})})}(t);n&&addBreadcrumbEvent(e,n)}}(e,t)}}},{key:"_doChangeToBackgroundTasks",value:function(e){!this.session||isSessionExpired(this.session,{maxReplayDuration:this._options.maxReplayDuration,sessionIdleExpire:this.timeouts.sessionIdleExpire})||(e&&this._createCustomBreadcrumb(e),this.conditionalFlush())}},{key:"_doChangeToForegroundTasks",value:function(e){this.session&&this.checkAndHandleExpiredSession()&&e&&this._createCustomBreadcrumb(e)}},{key:"_updateUserActivity",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Date.now();this._lastActivity=e}},{key:"_updateSessionActivity",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Date.now();this.session&&(this.session.lastActivity=e,this._maybeSaveSession())}},{key:"_createCustomBreadcrumb",value:function(e){var t=this;this.addUpdate(function(){t.throttledAddEvent({type:ey.Custom,timestamp:e.timestamp||0,data:{tag:"breadcrumb",payload:e}})})}},{key:"_addPerformanceEntries",value:function(){var e=this.performanceEntries.map(createPerformanceEntry).filter(Boolean).concat(this.replayPerformanceEntries);return this.performanceEntries=[],this.replayPerformanceEntries=[],Promise.all(createPerformanceSpans(this,e))}},{key:"_clearContext",value:function(){this._context.errorIds.clear(),this._context.traceIds.clear(),this._context.urls=[]}},{key:"_updateInitialTimestampFromEventBuffer",value:function(){var e=this.session,t=this.eventBuffer;if(e&&t&&!e.segmentId){var n=t.getEarliestTimestamp();n&&n<this._context.initialTimestamp&&(this._context.initialTimestamp=n)}}},{key:"_popEventContext",value:function(){var e={initialTimestamp:this._context.initialTimestamp,initialUrl:this._context.initialUrl,errorIds:Array.from(this._context.errorIds),traceIds:Array.from(this._context.traceIds),urls:this._context.urls};return this._clearContext(),e}},{key:"_runFlush",value:function(){var e=this;return(0,y._)(function(){var t,n,r,a,o,i,s;return(0,T.Jh)(this,function(l){switch(l.label){case 0:if(t=e.getSessionId(),!e.session||!e.eventBuffer||!t)return[2];return[4,e._addPerformanceEntries()];case 1:if(l.sent(),!e.eventBuffer||!e.eventBuffer.hasEvents)return[2];return[4,function(e){return _addMemoryEntry.apply(this,arguments)}(e)];case 2:if(l.sent(),!e.eventBuffer||t!==e.getSessionId())return[2];l.label=3;case 3:if(l.trys.push([3,6,,7]),e._updateInitialTimestampFromEventBuffer(),(n=Date.now())-e._context.initialTimestamp>e._options.maxReplayDuration+3e4)throw Error("Session is too long, not sending replay");return r=e._popEventContext(),a=e.session.segmentId++,e._maybeSaveSession(),[4,e.eventBuffer.finish()];case 4:return o=l.sent(),[4,sendReplay({replayId:t,recordingData:o,segmentId:a,eventContext:r,session:e.session,options:e.getOptions(),timestamp:n})];case 5:return l.sent(),[3,7];case 6:return i=l.sent(),e._handleException(i),e.stop({reason:"sendReplay"}),(s=(0,R.s3)())&&s.recordDroppedEvent("send_error","replay"),[3,7];case 7:return[2]}})})()}},{key:"__init5",value:function(){var e=this;this._flush=(0,y._)(function(){var t,n,r,a,o,i,s,l=arguments;return(0,T.Jh)(this,function(c){switch(c.label){case 0:if(n=void 0!==(t=(l.length>0&&void 0!==l[0]?l[0]:{}).force)&&t,!e._isEnabled&&!n||!e.checkAndHandleExpiredSession()||!e.session)return[2];if(r=e.session.started,a=Date.now()-r,e._debouncedFlush.cancel(),o=a<e._options.minReplayDuration,i=a>e._options.maxReplayDuration+5e3,o||i)return"[Replay] Session duration (".concat(Math.floor(a/1e3),"s) is too ").concat(o?"short":"long",", not sending replay."),e._options._experiments.traceInternals,o&&e._debouncedFlush(),[2];if((s=e.eventBuffer)&&0===e.session.segmentId&&!s.hasCheckout&&e._options._experiments.traceInternals,e._flushLock)return[3,2];return e._flushLock=e._runFlush(),[4,e._flushLock];case 1:return c.sent(),e._flushLock=void 0,[2];case 2:return c.trys.push([2,4,5,6]),[4,e._flushLock];case 3:case 4:return c.sent(),[3,6];case 5:return e._debouncedFlush(),[7];case 6:return[2]}})})}},{key:"_maybeSaveSession",value:function(){this.session&&this._options.stickySession&&saveSession(this.session)}},{key:"__init6",value:function(){var e=this;this._onMutationHandler=function(t){var n=t.length,r=e._options.mutationLimit,a=e._options.mutationBreadcrumbLimit,o=r&&n>r;if(n>a||o){var i=createBreadcrumb({category:"replay.mutations",data:{count:n,limit:o}});e._createCustomBreadcrumb(i)}return!o||(e.stop({reason:"mutationLimit",forceFlush:"session"===e.recordingMode}),!1)}}}]),ReplayContainer}();function getOption(e,t,n,r){var a="string"==typeof r?r.split(","):[],o=(0,I._)(e).concat((0,I._)(a),(0,I._)(t));return void 0!==n&&("string"==typeof n&&o.push(".".concat(n)),(0,H.Cf)(function(){console.warn("[Replay] You are using a deprecated configuration item for privacy. Read the documentation on how to use the new privacy configuration.")})),o.join(",")}var eY='img,image,svg,video,object,picture,embed,map,audio,link[rel="icon"],link[rel="apple-touch-icon"]',eX=["content-length","content-type","accept"],eG=!1,replayIntegration$1=function(e){return new eQ(e)},eQ=function(){function Replay$1(){var e,t,n,r,a,o,i,s,l,c,u,d,p=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},h=p.flushMinDelay,f=p.flushMaxDelay,m=p.minReplayDuration,v=p.maxReplayDuration,y=p.stickySession,k=p.useCompression,S=p.workerUrl,w=p._experiments,E=p.sessionSampleRate,C=p.errorSampleRate,T=p.maskAllText,M=void 0===T||T,x=p.maskAllInputs,R=void 0===x||x,A=p.blockAllMedia,O=p.mutationBreadcrumbLimit,D=p.mutationLimit,N=p.slowClickTimeout,L=p.slowClickIgnoreSelectors,B=p.networkDetailAllowUrls,F=p.networkDetailDenyUrls,P=p.networkCaptureBodies,W=p.networkRequestHeaders,z=void 0===W?[]:W,U=p.networkResponseHeaders,H=void 0===U?[]:U,j=p.mask,q=p.maskAttributes,$=void 0===q?["title","placeholder"]:q,J=p.unmask,K=p.block,V=p.unblock,Y=p.ignore,X=p.maskFn,Q=p.beforeAddRecordingEvent,Z=p.beforeErrorSampling,ee=p.blockClass,et=p.blockSelector,en=p.maskInputOptions,er=p.maskTextClass,ea=p.maskTextSelector,eo=p.ignoreClass;(0,g._)(this,Replay$1),this.name=Replay$1.id;var ei=(t=(e={mask:void 0===j?[]:j,unmask:void 0===J?[]:J,block:void 0===K?[]:K,unblock:void 0===V?[]:V,ignore:void 0===Y?[]:Y,blockClass:ee,blockSelector:et,maskTextClass:er,maskTextSelector:ea,ignoreClass:eo}).mask,n=e.unmask,r=e.block,a=e.unblock,o=e.ignore,i=e.blockClass,s=e.blockSelector,l=e.maskTextClass,c=e.maskTextSelector,u=e.ignoreClass,d={maskTextSelector:getOption(t,[".sentry-mask","[data-sentry-mask]"],l,c),unmaskTextSelector:getOption(n,[".sentry-unmask","[data-sentry-unmask]"]),blockSelector:getOption(r,[".sentry-block","[data-sentry-block]"].concat((0,I._)(['base[href="/"]'])),i,s),unblockSelector:getOption(a,[".sentry-unblock","[data-sentry-unblock]"]),ignoreSelector:getOption(o,[".sentry-ignore","[data-sentry-ignore]",'input[type="file"]'],u)},i instanceof RegExp&&(d.blockClass=i),l instanceof RegExp&&(d.maskTextClass=l),d);if(this._recordingOptions=(0,b._)((0,_._)({maskAllInputs:R,maskAllText:M,maskInputOptions:(0,b._)((0,_._)({},en||{}),{password:!0}),maskTextFn:X,maskInputFn:X,maskAttributeFn:function(e,t,n){var r,a,o,i,s,l,c;return a=(r={maskAttributes:$,maskAllText:M,privacyOptions:ei,key:e,value:t,el:n}).el,o=r.key,i=r.maskAttributes,s=r.maskAllText,l=r.privacyOptions,c=r.value,!s||l.unmaskTextSelector&&a.matches(l.unmaskTextSelector)?c:i.includes(o)||"value"===o&&"INPUT"===a.tagName&&["submit","button"].includes(a.getAttribute("type")||"")?c.replace(/[\S]/g,"*"):c}},ei),{slimDOMOptions:"all",inlineStylesheet:!0,inlineImages:!1,collectFonts:!0,errorHandler:function(e){try{e.__rrweb__=!0}catch(e){}}}),this._initialOptions={flushMinDelay:void 0===h?5e3:h,flushMaxDelay:void 0===f?5500:f,minReplayDuration:Math.min(void 0===m?4999:m,15e3),maxReplayDuration:Math.min(void 0===v?36e5:v,36e5),stickySession:void 0===y||y,sessionSampleRate:E,errorSampleRate:C,useCompression:void 0===k||k,workerUrl:S,blockAllMedia:void 0===A||A,maskAllInputs:R,maskAllText:M,mutationBreadcrumbLimit:void 0===O?750:O,mutationLimit:void 0===D?1e4:D,slowClickTimeout:void 0===N?7e3:N,slowClickIgnoreSelectors:void 0===L?[]:L,networkDetailAllowUrls:void 0===B?[]:B,networkDetailDenyUrls:void 0===F?[]:F,networkCaptureBodies:void 0===P||P,networkRequestHeaders:_getMergedNetworkHeaders(z),networkResponseHeaders:_getMergedNetworkHeaders(H),beforeAddRecordingEvent:Q,beforeErrorSampling:Z,_experiments:void 0===w?{}:w},"number"==typeof E&&(console.warn("[Replay] You are passing `sessionSampleRate` to the Replay integration.\nThis option is deprecated and will be removed soon.\nInstead, configure `replaysSessionSampleRate` directly in the SDK init options, e.g.:\nSentry.init({ replaysSessionSampleRate: ".concat(E," })")),this._initialOptions.sessionSampleRate=E),"number"==typeof C&&(console.warn("[Replay] You are passing `errorSampleRate` to the Replay integration.\nThis option is deprecated and will be removed soon.\nInstead, configure `replaysOnErrorSampleRate` directly in the SDK init options, e.g.:\nSentry.init({ replaysOnErrorSampleRate: ".concat(C," })")),this._initialOptions.errorSampleRate=C),this._initialOptions.blockAllMedia&&(this._recordingOptions.blockSelector=this._recordingOptions.blockSelector?"".concat(this._recordingOptions.blockSelector,",").concat(eY):eY),this._isInitialized&&(0,G.j)())throw Error("Multiple Sentry Session Replay instances are not supported");this._isInitialized=!0}return(0,k._)(Replay$1,[{key:"_isInitialized",get:function(){return eG},set:function(e){eG=e}},{key:"setupOnce",value:function(){var e=this;(0,G.j)()&&(this._setup(),setTimeout(function(){return e._initialize()}))}},{key:"start",value:function(){this._replay&&this._replay.start()}},{key:"startBuffering",value:function(){this._replay&&this._replay.startBuffering()}},{key:"stop",value:function(){return this._replay?this._replay.stop({forceFlush:"session"===this._replay.recordingMode}):Promise.resolve()}},{key:"flush",value:function(e){return this._replay&&this._replay.isEnabled()?this._replay.sendBufferedReplayOrFlush(e):Promise.resolve()}},{key:"getReplayId",value:function(){if(this._replay&&this._replay.isEnabled())return this._replay.getSessionId()}},{key:"_initialize",value:function(){this._replay&&(this._maybeLoadFromReplayCanvasIntegration(),this._replay.initializeSampling())}},{key:"_setup",value:function(){var e,t,n,r,a=(e=this._initialOptions,n=(t=(0,R.s3)())&&t.getOptions(),r=(0,_._)({sessionSampleRate:0,errorSampleRate:0},(0,W.Jr)(e)),n?(null==e.sessionSampleRate&&null==e.errorSampleRate&&null==n.replaysSessionSampleRate&&null==n.replaysOnErrorSampleRate&&(0,H.Cf)(function(){console.warn("Replay is disabled because neither `replaysSessionSampleRate` nor `replaysOnErrorSampleRate` are set.")}),"number"==typeof n.replaysSessionSampleRate&&(r.sessionSampleRate=n.replaysSessionSampleRate),"number"==typeof n.replaysOnErrorSampleRate&&(r.errorSampleRate=n.replaysOnErrorSampleRate)):(0,H.Cf)(function(){console.warn("SDK client is not available.")}),r);this._replay=new eV({options:a,recordingOptions:this._recordingOptions})}},{key:"_maybeLoadFromReplayCanvasIntegration",value:function(){try{var e=(0,R.s3)().getIntegrationByName("ReplayCanvas");if(!e)return;this._replay._canvas=e.getOptions()}catch(e){}}}],[{key:"__initStatic",value:function(){this.id="Replay"}}]),Replay$1}();function _getMergedNetworkHeaders(e){return(0,I._)(eX).concat((0,I._)(e.map(function(e){return e.toLowerCase()})))}eQ.__initStatic()}}]);